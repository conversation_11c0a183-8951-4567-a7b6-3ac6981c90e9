<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 账号接口
 */
class Account extends Api
{

    protected $noNeedLogin = [];
    protected $noNeedRight = ['login', 'export'];

    /**
     * 获取账号列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'status'    => input('status/s', ''),
            'remark'    => input('remark/s', ''),
            'nickname'   => input('nickname/s', ''),
            'hasErrorMsg'   => input('hasErrorMsg/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能查看自己的账号
            $where['user_id'] = $this->auth->id;
        }
        // 构建查询条件
        if ($params['name'] !== '') {
            $whereOr = function ($query) use ($params) {
                $query->where('a.name', 'like', "%{$params['name']}%")
                    ->whereOr('a.config', 'like', "%{$params['name']}%");
            };
            $where[] = $whereOr;
        }
        if ($params['status'] !== '') {
            $where['a.status'] = $params['status'];
        }
        if ($params['remark'] !== '') {
            $where['a.remark'] = ['like', "%{$params['remark']}%"];
        }
        if ($params['nickname'] !== '') {
            $where['u.nickname'] = ['like', "%{$params['nickname']}%"];
        }
        if ($params['hasErrorMsg'] == 'true') {
            // errormsg不为空的账号
            $where['a.errormsg'] = ['neq', ''];
        }
        if ($params['hasErrorMsg'] == 'false') {
            // errormsg为空的账号
            $where['a.errormsg'] = ['eq', ''];
        }
        // 查询数据
        $list = \think\Db::name('account')
            ->alias('a')
            ->join('user u', 'a.user_id = u.id', 'LEFT')
            ->field('a.diamond,a.id,a.pay_limit,a.name,a.status,a.errormsg,a.remark,from_unixtime(a.createtime,"%Y-%m-%d %H:%i:%s") as createtime,from_unixtime(a.pulltime,"%Y-%m-%d %H:%i:%s") as pulltime,u.nickname')
            ->where($where)
            ->order('a.diamond', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 获取今日、昨日、前日的时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
        $beforeStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 将Collection对象转换为数组
        $listArray = collection($list)->toArray();

        // 获取所有账号ID
        $accountIds = array_column($listArray, 'id');

        // 批量统计主表
        $mainStats = [];
        if (!empty($accountIds)) {
            $mainStatsRaw = Db::name('order')
                ->field("account_id,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN 1 ELSE 0 END) as today_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN 1 ELSE 0 END) as yesterday_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN 1 ELSE 0 END) as before_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN amount ELSE 0 END) as today_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN amount ELSE 0 END) as yesterday_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN amount ELSE 0 END) as before_amount
                ")
                ->where('account_id', 'in', $accountIds)
                ->group('account_id')
                ->select();
            foreach ($mainStatsRaw as $row) {
                $mainStats[$row['account_id']] = $row;
            }
        }
        // 批量统计归档表
        $archiveStats = [];
        if (!empty($accountIds)) {
            $archiveStatsRaw = Db::name('order_history')
                ->field("account_id,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN 1 ELSE 0 END) as today_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN 1 ELSE 0 END) as yesterday_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN 1 ELSE 0 END) as before_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN amount ELSE 0 END) as today_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN amount ELSE 0 END) as yesterday_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN amount ELSE 0 END) as before_amount
                ")
                ->where('account_id', 'in', $accountIds)
                ->group('account_id')
                ->select();
            foreach ($archiveStatsRaw as $row) {
                $archiveStats[$row['account_id']] = $row;
            }
        }
        // 合并主表和归档表统计
        $statsMap = [];
        foreach ($accountIds as $aid) {
            $main = isset($mainStats[$aid]) ? $mainStats[$aid] : [
                'today_count' => 0,
                'yesterday_count' => 0,
                'before_count' => 0,
                'today_amount' => 0,
                'yesterday_amount' => 0,
                'before_amount' => 0
            ];
            $archive = isset($archiveStats[$aid]) ? $archiveStats[$aid] : [
                'today_count' => 0,
                'yesterday_count' => 0,
                'before_count' => 0,
                'today_amount' => 0,
                'yesterday_amount' => 0,
                'before_amount' => 0
            ];
            $statsMap[$aid] = [
                'today_count' => $main['today_count'] + $archive['today_count'],
                'yesterday_count' => $main['yesterday_count'] + $archive['yesterday_count'],
                'before_count' => $main['before_count'] + $archive['before_count'],
                'today_amount' => $main['today_amount'] + $archive['today_amount'],
                'yesterday_amount' => $main['yesterday_amount'] + $archive['yesterday_amount'],
                'before_amount' => $main['before_amount'] + $archive['before_amount'],
            ];
        }
        // 为每个账号填充统计数据
        foreach ($list as &$item) {
            $aid = $item['id'];
            $item['today_count'] = $statsMap[$aid]['today_count'] ?? 0;
            $item['yesterday_count'] = $statsMap[$aid]['yesterday_count'] ?? 0;
            $item['before_count'] = $statsMap[$aid]['before_count'] ?? 0;
            $item['today_amount'] = number_format($statsMap[$aid]['today_amount'] ?? 0, 2, '.', '');
            $item['yesterday_amount'] = number_format($statsMap[$aid]['yesterday_amount'] ?? 0, 2, '.', '');
            $item['before_amount'] = number_format($statsMap[$aid]['before_amount'] ?? 0, 2, '.', '');
        }
        unset($item); // 清除引用，避免后续循环出现问题

        // 查询账号总数（不使用订单表，直接查account表）
        $total = \think\Db::name('account')
            ->alias('a')
            ->join('user u', 'a.user_id = u.id', 'LEFT')
            ->where($where)
            ->count();
        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 导出账号列表
     */
    public function export()
    {
        // 获取查询参数（与list接口完全一致）
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'status'    => input('status/s', ''),
            'remark'    => input('remark/s', ''),
            'nickname'   => input('nickname/s', ''),
            'hasErrorMsg'   => input('hasErrorMsg/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能导出自己的账号
            $where['user_id'] = $this->auth->id;
        }

        // 构建查询条件（与list接口完全一致）
        if ($params['name'] !== '') {
            $whereOr = function ($query) use ($params) {
                $query->where('a.name', 'like', "%{$params['name']}%")
                    ->whereOr('a.config', 'like', "%{$params['name']}%");
            };
            $where[] = $whereOr;
        }
        if ($params['status'] !== '') {
            $where['a.status'] = $params['status'];
        }
        if ($params['remark'] !== '') {
            $where['a.remark'] = ['like', "%{$params['remark']}%"];
        }
        if ($params['nickname'] !== '') {
            $where['u.nickname'] = ['like', "%{$params['nickname']}%"];
        }
        if ($params['hasErrorMsg'] == 'true') {
            // errormsg不为空的账号
            $where['a.errormsg'] = ['neq', ''];
        }
        if ($params['hasErrorMsg'] == 'false') {
            // errormsg为空的账号
            $where['a.errormsg'] = ['eq', ''];
        }

        // 查询所有数据（不分页，但使用相同的查询逻辑）
        $list = \think\Db::name('account')
            ->alias('a')
            ->join('user u', 'a.user_id = u.id', 'LEFT')
            ->field('a.diamond,a.id,a.name,a.status,a.errormsg,a.remark,from_unixtime(a.createtime,"%Y-%m-%d %H:%i:%s") as createtime,from_unixtime(a.pulltime,"%Y-%m-%d %H:%i:%s") as pulltime,u.nickname')
            ->where($where)
            ->order('a.diamond', 'desc')
            ->select();

        // 检查是否有数据
        if (empty($list)) {
            $this->error('没有找到符合条件的账号数据');
        }

        // 获取今日开始和结束时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取昨日开始和结束时间戳
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 获取前日开始和结束时间戳
        $beforeStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 将Collection对象转换为数组
        $listArray = collection($list)->toArray();

        // 获取所有账号ID
        $accountIds = array_column($listArray, 'id');

        // 批量统计主表
        $mainStats = [];
        if (!empty($accountIds)) {
            $mainStatsRaw = Db::name('order')
                ->field("account_id,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN 1 ELSE 0 END) as today_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN 1 ELSE 0 END) as yesterday_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN 1 ELSE 0 END) as before_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN amount ELSE 0 END) as today_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN amount ELSE 0 END) as yesterday_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN amount ELSE 0 END) as before_amount
                ")
                ->where('account_id', 'in', $accountIds)
                ->group('account_id')
                ->select();
            foreach ($mainStatsRaw as $row) {
                $mainStats[$row['account_id']] = $row;
            }
        }
        // 批量统计归档表
        $archiveStats = [];
        if (!empty($accountIds)) {
            $archiveStatsRaw = Db::name('order_history')
                ->field("account_id,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN 1 ELSE 0 END) as today_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN 1 ELSE 0 END) as yesterday_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN 1 ELSE 0 END) as before_count,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN amount ELSE 0 END) as today_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN amount ELSE 0 END) as yesterday_amount,
                    SUM(CASE WHEN pay_status=1 AND callback_time BETWEEN $beforeStart AND $beforeEnd THEN amount ELSE 0 END) as before_amount
                ")
                ->where('account_id', 'in', $accountIds)
                ->group('account_id')
                ->select();
            foreach ($archiveStatsRaw as $row) {
                $archiveStats[$row['account_id']] = $row;
            }
        }
        // 合并主表和归档表统计
        $statsMap = [];
        foreach ($accountIds as $aid) {
            $main = isset($mainStats[$aid]) ? $mainStats[$aid] : [
                'today_count' => 0,
                'yesterday_count' => 0,
                'before_count' => 0,
                'today_amount' => 0,
                'yesterday_amount' => 0,
                'before_amount' => 0
            ];
            $archive = isset($archiveStats[$aid]) ? $archiveStats[$aid] : [
                'today_count' => 0,
                'yesterday_count' => 0,
                'before_count' => 0,
                'today_amount' => 0,
                'yesterday_amount' => 0,
                'before_amount' => 0
            ];
            $statsMap[$aid] = [
                'today_count' => $main['today_count'] + $archive['today_count'],
                'yesterday_count' => $main['yesterday_count'] + $archive['yesterday_count'],
                'before_count' => $main['before_count'] + $archive['before_count'],
                'today_amount' => $main['today_amount'] + $archive['today_amount'],
                'yesterday_amount' => $main['yesterday_amount'] + $archive['yesterday_amount'],
                'before_amount' => $main['before_amount'] + $archive['before_amount'],
            ];
        }
        // 为每个账号填充统计数据
        foreach ($list as &$item) {
            $aid = $item['id'];
            $item['today_count'] = $statsMap[$aid]['today_count'] ?? 0;
            $item['yesterday_count'] = $statsMap[$aid]['yesterday_count'] ?? 0;
            $item['before_count'] = $statsMap[$aid]['before_count'] ?? 0;
            $item['today_amount'] = number_format($statsMap[$aid]['today_amount'] ?? 0, 2, '.', '');
            $item['yesterday_amount'] = number_format($statsMap[$aid]['yesterday_amount'] ?? 0, 2, '.', '');
            $item['before_amount'] = number_format($statsMap[$aid]['before_amount'] ?? 0, 2, '.', '');
        }
        unset($item); // 清除引用，避免后续循环出现问题

        // 准备导出数据
        $exportData = [];

        // 添加表头
        $exportData[] = [
            'ID',
            '账号名称',
            '状态',
            '号内余额',
            '错误信息',
            '备注',
            '创建时间',
            '拉取时间',
            '用户昵称',
            '今日订单数',
            '昨日订单数',
            '前日订单数',
            '今日金额',
            '昨日金额',
            '前日金额'
        ];

        // 添加数据行
        foreach ($list as $item) {
            $statusText = $item['status'] == 1 ? '正常' : '禁用';

            $exportData[] = [
                $item['id'],
                $item['name'],
                $statusText,
                $item['diamond'],
                $item['errormsg'] ?: '',
                $item['remark'] ?: '',
                $item['createtime'],
                $item['pulltime'],
                $item['nickname'] ?: '',
                $item['today_count'],
                $item['yesterday_count'],
                $item['before_count'],
                $item['today_amount'],
                $item['yesterday_amount'],
                $item['before_amount']
            ];
        }

        // 生成CSV文件
        $filename = '账号列表_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'export' . DS;

        // 确保目录存在
        if (!is_dir($filepath)) {
            if (!mkdir($filepath, 0755, true)) {
                throw new \Exception('创建导出目录失败');
            }
        }

        $fullpath = $filepath . $filename;

        // 写入CSV文件
        $fp = fopen($fullpath, 'w');
        if ($fp === false) {
            throw new \Exception('无法创建导出文件');
        }

        // 添加BOM头，解决中文乱码问题
        fwrite($fp, "\xEF\xBB\xBF");

        foreach ($exportData as $row) {
            if (fputcsv($fp, $row) === false) {
                fclose($fp);
                throw new \Exception('写入CSV数据失败');
            }
        }
        fclose($fp);

        // 检查文件是否成功创建
        if (!file_exists($fullpath)) {
            throw new \Exception('导出文件创建失败');
        }

        // 清理7天前的导出文件
        $this->cleanOldExportFiles($filepath);

        // 返回下载链接
        $downloadUrl = $this->request->domain() . '/uploads/export/' . $filename;

        $this->success('导出成功', [
            'download_url' => $downloadUrl,
            'filename' => $filename,
            'total_count' => count($list)
        ]);
    }

    /**
     * 清理过期的导出文件
     * @param string $filepath 文件路径
     */
    private function cleanOldExportFiles($filepath)
    {
        try {
            $files = glob($filepath . '*.csv');
            $expireTime = time() - 7 * 24 * 60 * 60; // 7天前

            foreach ($files as $file) {
                if (filemtime($file) < $expireTime) {
                    unlink($file);
                }
            }
        } catch (\Exception $e) {
            // 清理失败不影响主流程，只记录日志
            \think\Log::warning('清理过期导出文件失败：' . $e->getMessage());
        }
    }

    /**
     * 添加账号
     */
    public function add()
    {
        $params = input();
        $params['config'] = '';
        if ($params['cookie']) {
            $config['cookie'] = $params['cookie'];
            $config['phone'] = $params['phone'];
            $config['password'] = $params['password'];
            $params['config'] = json_encode($config);
        }
        Db::startTrans();
        try {
            $account = new \app\common\model\Account();
            if ($account->where('name', $params['name'])->find()) {
                throw new \Exception('账号已存在');
            }
            $data['pay_limit'] = $params['pay_limit'];
            $data['pulltime'] = time();
            $data['name'] = $params['name'];
            $data['status'] = 1;
            $data['remark'] = $params['remark'];
            $data['user_id'] = $this->auth->id;
            $data['config'] = $params['config'];
            $account->save($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('添加成功');
    }

    /**
     * 删除账号
     */
    public function delete()
    {
        $id = input('id');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        // 权限检查：非管理员只能删除自己的账号
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $account['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限删除该账号');
        }
        if (!$isAdmin) {
            // 48小时内不能删除有支付订单的账号
            $order = Db::name('order')->where('account_id', $id)->where('pay_status', 1)->where('create_time', '>', time() - 48 * 60 * 60)->find();
            if ($order) {
                $this->error('48小时内不能删除有支付订单的账号');
            }
        }
        $account->delete();
        $this->success('删除账号成功');
    }

    /**
     * 修改账号状态
     */
    public function status()
    {
        $id = input('id');
        $status = input('status');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        // 权限检查：非管理员只能修改自己的账号
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $account['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限修改该账号状态');
        }
        $account->errormsg = '';
        $account->status = $status;
        $account->save();
        $this->success('修改账号状态成功');
    }

    /**
     * 修改账号收款状态
     */
    public function setPayStatus()
    {
        $id = input('id');
        $paystatus = input('paystatus');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        $account->paystatus = $paystatus;
        $account->save();
        $this->success('修改账号收款状态成功');
    }

    /**
     * 修改账号信息
     */
    public function update()
    {
        $params = input();
        $params['config'] = '';
        if ($params['cookie']) {
            $config['cookie'] = $params['cookie'];
            $config['phone'] = $params['phone'];
            $config['password'] = $params['password'];
            $params['config'] = json_encode($config);
        }

        Db::startTrans();
        try {
            $account = new \app\common\model\Account();
            $account = $account->where('id', $params['id'])->find();
            if (!$account) {
                throw new \Exception('账号不存在');
            }
            // 权限检查：非管理员只能修改自己的账号
            $isAdmin = $this->auth->group_id === 1;
            $isOwner = $account['user_id'] === $this->auth->id;
            if (!$isAdmin && !$isOwner) {
                throw new \Exception('您没有权限修改该账号');
            }
            $data['pay_limit'] = $params['pay_limit'];
            $data['errormsg'] = '';
            $data['name'] = $params['name'];
            $data['status'] = $params['status'];
            $data['remark'] = $params['remark'];
            $data['config'] = $params['config'];
            $account->save($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('修改账号信息成功');
    }

    /**
     * 获取账号信息
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        // 权限检查：非管理员只能查看自己的账号
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $account['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限查看该账号');
        }
        $account->config = json_decode($account->config, true);
        $this->success('获取账号信息成功', $account);
    }

    public function login()
    {
        $params = input();
        // $proxy = \Proxy::getProxyByShenLong(1, "");
        $res = \laifeng\Pay::accountLogin($params['phone'], $params['password']);
        if (!$res['success']) {
            $this->error($res['msg']);
        }
        
        $cookie = "L_pck_rm=".urlencode($res['data']['token']);
        $userid = $res['data']['userId'];
        
        $this->success('登录成功', ['cookie' => $cookie, 'userid' => $userid]);
    }

    /**
     * 合并主表(order)和归档表(order_history)的统计数据
     * @param array $where 查询条件
     * @param string $type 统计类型 count/sum
     * @param string $field 字段名，sum时用
     * @return float|int
     */
    private function getOrderStats($where, $type = 'count', $field = 'id')
    {
        if ($type === 'count') {
            $main = \think\Db::name('order')->where($where)->count($field);
            $archive = \think\Db::name('order_history')->where($where)->count($field);
        } elseif ($type === 'sum') {
            $main = \think\Db::name('order')->where($where)->sum($field);
            $archive = \think\Db::name('order_history')->where($where)->sum($field);
        } else {
            $main = 0;
            $archive = 0;
        }
        return $main + $archive;
    }

    /**
     * 去除where条件中的表别名前缀（如a.）
     * @param array $where
     * @param string $prefix
     * @return array
     */
    private function stripAliasPrefix($where, $prefix = 'a.')
    {
        $result = [];
        foreach ($where as $k => $v) {
            if (strpos($k, $prefix) === 0) {
                $result[substr($k, strlen($prefix))] = $v;
            } else {
                $result[$k] = $v;
            }
        }
        return $result;
    }

    /**
     * 过滤掉不属于order表的字段
     * @param array $where
     * @return array
     */
    private function filterOrderFields($where)
    {
        $orderFields = [
            'id',
            'user_id',
            'merchant_id',
            'account_id',
            'out_trade_no',
            'dy_order_id',
            'pay_status',
            'paytype',
            'amount',
            'callback_status',
            'create_time',
            'callback_time',
            'remark'
        ];
        $result = [];
        foreach ($where as $k => $v) {
            if (in_array($k, $orderFields)) {
                $result[$k] = $v;
            }
        }
        return $result;
    }
}
