# 数据库设计文档

## 1. 数据库概览

### 数据库信息
- **数据库类型**: MySQL 5.6+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **表前缀**: fa_

### 核心表结构
- **订单表**: order (主表) + order_history (归档表)
- **账号表**: account (收款账号管理)
- **商户表**: merchant (商户信息)
- **用户表**: user (系统用户)

### 新增功能特性
- **异步支付监控**: 自动检查订单支付状态
- **订单自动归档**: 历史数据自动迁移
- **多渠道支持**: 支持微信和支付宝
- **代理池管理**: 支持多代理轮换

## 2. 主要数据表

### 2.1 订单表 (order)
```sql
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `out_trade_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `pay_trade_no` varchar(64) DEFAULT NULL COMMENT '支付平台订单号',
  `dy_order_id` varchar(64) DEFAULT NULL COMMENT '来疯订单号',
  `mid` int(11) NOT NULL COMMENT '商户ID',
  `merchant_id` int(11) NOT NULL COMMENT '商户ID(关联merchant表)',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '收款账号ID',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `real_amount` decimal(10,2) DEFAULT NULL COMMENT '实际支付金额',
  `channel_code` varchar(32) DEFAULT NULL COMMENT '支付渠道代码',
  `paytype` varchar(32) DEFAULT NULL COMMENT '支付类型(lfwx/lfali)',
  `pay_url` text COMMENT '支付链接',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
  `callback_url` varchar(255) DEFAULT NULL COMMENT '同步回调地址',
  `pay_status` tinyint(1) DEFAULT 0 COMMENT '支付状态(0未支付1已支付)',
  `callback_status` tinyint(1) DEFAULT 0 COMMENT '回调状态(0未回调1成功2失败)',
  `callback_retry_count` int(11) DEFAULT 0 COMMENT '回调重试次数',
  `last_callback_time` int(11) DEFAULT NULL COMMENT '最后回调时间',
  `callback_time` int(11) DEFAULT NULL COMMENT '回调时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '订单状态(0异常1正常)',
  `remark` text COMMENT '备注信息',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `pay_time` int(11) DEFAULT NULL COMMENT '支付时间',
  `fix_time` int(11) DEFAULT NULL COMMENT '修复时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_out_trade_no` (`out_trade_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_callback_status` (`callback_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_pay_trade_no` (`pay_trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

### 2.2 商户表 (merchant)
```sql
CREATE TABLE `merchant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商户ID',
  `name` varchar(100) NOT NULL COMMENT '商户名称',
  `key` varchar(64) NOT NULL COMMENT '商户密钥',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0禁用1启用)',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
  `rate` decimal(5,4) DEFAULT 0.0000 COMMENT '费率',
  `min_amount` decimal(10,2) DEFAULT 1.00 COMMENT '最小金额',
  `max_amount` decimal(10,2) DEFAULT 50000.00 COMMENT '最大金额',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '默认通知地址',
  `callback_url` varchar(255) DEFAULT NULL COMMENT '默认回调地址',
  `white_ip` text COMMENT '白名单IP',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户表';
```

### 2.3 收款账号表 (account)
```sql
CREATE TABLE `account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账号ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '账号名称',
  `config` text COMMENT '账号配置(JSON格式,包含cookie、phone、password等)',
  `status` tinyint(1) DEFAULT 1 COMMENT '账号状态(0禁用1启用)',
  `paystatus` tinyint(1) DEFAULT 1 COMMENT '收款状态(0禁用1启用)',
  `errormsg` text COMMENT '错误信息',
  `pulltime` int(11) DEFAULT NULL COMMENT '最后拉单时间',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `diamond` int(11) DEFAULT 0 COMMENT '钻石余额',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_paystatus` (`paystatus`),
  KEY `idx_pulltime` (`pulltime`),
  KEY `idx_name` (`name`),
  KEY `idx_diamond` (`diamond`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款账号表';
```

### 2.4 用户表 (user)
```sql
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `group_id` int(11) DEFAULT 1 COMMENT '用户组ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `password` varchar(32) NOT NULL COMMENT '密码',
  `salt` varchar(30) DEFAULT NULL COMMENT '密码盐',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(11) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `level` tinyint(1) DEFAULT 1 COMMENT '等级',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别(0未知1男2女)',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `bio` varchar(100) DEFAULT NULL COMMENT '格言',
  `money` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `score` int(11) DEFAULT 0 COMMENT '积分',
  `successions` int(11) DEFAULT 1 COMMENT '连续登录天数',
  `maxsuccessions` int(11) DEFAULT 1 COMMENT '最大连续登录天数',
  `prevtime` int(11) DEFAULT NULL COMMENT '上次登录时间',
  `logintime` int(11) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `loginfailure` tinyint(1) DEFAULT 0 COMMENT '失败次数',
  `joinip` varchar(50) DEFAULT NULL COMMENT '加入IP',
  `jointime` int(11) DEFAULT NULL COMMENT '加入时间',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) DEFAULT NULL COMMENT 'Token',
  `status` varchar(30) DEFAULT 'normal' COMMENT '状态',
  `verification` varchar(255) DEFAULT NULL COMMENT '验证',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 2.5 管理员表 (admin)
```sql
CREATE TABLE `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(20) NOT NULL COMMENT '用户名',
  `nickname` varchar(50) NOT NULL COMMENT '昵称',
  `password` varchar(32) NOT NULL COMMENT '密码',
  `salt` varchar(30) NOT NULL COMMENT '密码盐',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `loginfailure` tinyint(1) DEFAULT 0 COMMENT '失败次数',
  `logintime` int(11) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) DEFAULT NULL COMMENT 'Token',
  `status` varchar(30) DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
```

### 2.6 订单历史表 (order_history)
```sql
CREATE TABLE `order_history` (
  `id` int(11) NOT NULL COMMENT '原订单ID',
  `out_trade_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `pay_trade_no` varchar(64) DEFAULT NULL COMMENT '支付平台订单号',
  `dy_order_id` varchar(64) DEFAULT NULL COMMENT '抖音订单号',
  `mid` int(11) NOT NULL COMMENT '商户ID',
  `merchant_id` int(11) NOT NULL COMMENT '商户ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '收款账号ID',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `real_amount` decimal(10,2) DEFAULT NULL COMMENT '实际支付金额',
  `channel_code` varchar(32) DEFAULT NULL COMMENT '支付渠道代码',
  `paytype` varchar(32) DEFAULT NULL COMMENT '支付类型',
  `pay_status` tinyint(1) DEFAULT 0 COMMENT '支付状态',
  `callback_status` tinyint(1) DEFAULT 0 COMMENT '回调状态',
  `status` tinyint(1) DEFAULT 1 COMMENT '订单状态',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `pay_time` int(11) DEFAULT NULL COMMENT '支付时间',
  `archive_time` int(11) NOT NULL COMMENT '归档时间',
  PRIMARY KEY (`id`),
  KEY `idx_out_trade_no` (`out_trade_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_archive_time` (`archive_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单历史表';
```

## 3. 数据字典

### 3.1 订单状态枚举
| 字段 | 值 | 说明 |
|------|----|----|
| pay_status | 0 | 未支付 |
| pay_status | 1 | 已支付 |
| callback_status | 0 | 未回调 |
| callback_status | 1 | 回调成功 |
| callback_status | 2 | 回调失败 |
| status | 0 | 异常 |
| status | 1 | 正常 |

### 3.2 支付类型枚举
| 值 | 说明 | 对应渠道 |
|----|----|----|
| lfwx | 来疯微信 | Wx-Wap |
| lfali | 来疯支付宝 | Zfb-Wap |

### 3.3 账号状态枚举
| 字段 | 值 | 说明 |
|------|----|----|
| status | 0 | 禁用 |
| status | 1 | 启用 |
| paystatus | 0 | 收款禁用 |
| paystatus | 1 | 收款启用 |

## 4. 索引设计说明

### 4.1 主要索引
- **订单表**: 商户订单号唯一索引、商户ID索引、支付状态索引
- **商户表**: 商户名称唯一索引、状态索引
- **账号表**: 用户ID索引、状态索引、拉单时间索引
- **用户表**: 用户名唯一索引、用户组索引

### 4.2 复合索引
- **订单查询**: (merchant_id, pay_status, create_time)
- **账号筛选**: (status, paystatus, pulltime)
- **历史数据**: (create_time, archive_time)

## 5. 数据约束

### 5.1 业务约束
- 订单金额: 0.01 - 50000.00
- 商户费率: 0.0000 - 1.0000
- 回调重试: 最多5次
- 支付超时: 5分钟

### 5.2 数据完整性
- 外键约束: order.merchant_id -> merchant.id
- 外键约束: order.account_id -> account.id
- 外键约束: account.user_id -> user.id
- 唯一约束: 商户订单号全局唯一

## 6. 分表策略

### 6.1 订单表分表
- **按时间分表**: 按月分表 order_202401, order_202402...
- **历史数据**: 超过30天的数据自动归档到 order_history
- **查询优化**: 根据时间范围路由到对应分表

### 6.2 日志表分表
- **按日期分表**: admin_log_20240101, admin_log_20240102...
- **自动清理**: 超过90天的日志自动删除

## 7. 备份策略

### 7.1 全量备份
- **频率**: 每日凌晨2点
- **保留**: 保留30天
- **存储**: 本地+云存储双重备份

### 7.2 增量备份
- **频率**: 每4小时一次
- **保留**: 保留7天
- **恢复**: 支持任意时间点恢复

## 8. 性能优化

### 8.1 查询优化
- 避免全表扫描
- 合理使用索引
- 分页查询限制
- 慢查询监控

### 8.2 存储优化
- 定期清理历史数据
- 压缩存储空间
- 优化表结构
- 监控磁盘使用率