<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Exception;

/**
 * 仪表盘接口
 */
class Dashboard extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 获取基础查询条件
     */
    protected function getBaseWhere()
    {
        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能查看自己的数据
            $where['user_id'] = $this->auth->id;
        }
        return $where;
    }

    /**
     * 获取时间范围查询条件（用于未支付订单）
     */
    protected function getTimeWhere($startDate = null, $endDate = null)
    {
        $where = [];

        if ($startDate) {
            $startTime = strtotime($startDate . ' 00:00:00');
            $where['callback_time'] = ['egt', $startTime];
        }

        if ($endDate) {
            $endTime = strtotime($endDate . ' 23:59:59');
            if (isset($where['callback_time'])) {
                $where['callback_time'] = ['between', [$startTime, $endTime]];
            } else {
                $where['callback_time'] = ['elt', $endTime];
            }
        }

        return $where;
    }

    /**
     * 获取时间范围查询条件（用于已支付订单）
     */
    protected function getTimeWhereForPaid($startDate = null, $endDate = null)
    {
        $where = [];

        if ($startDate) {
            $startTime = strtotime($startDate . ' 00:00:00');
            $where['callback_time'] = ['egt', $startTime];
        }

        if ($endDate) {
            $endTime = strtotime($endDate . ' 23:59:59');
            if (isset($where['callback_time'])) {
                $where['callback_time'] = ['between', [$startTime, $endTime]];
            } else {
                $where['callback_time'] = ['elt', $endTime];
            }
        }

        return $where;
    }

    /**
     * 合并主表(order)和归档表(order_history)的统计数据
     * @param string $table 统计的表名（order 或 order_history）
     * @param array $where 查询条件
     * @param string|array $field 查询字段
     * @return array
     */
    private function getOrderStats($where, $paytype, $field, $extraWhere = [])
    {
        $main = Db::name('order')
            ->where($where)
            ->where('paytype', $paytype)
            ->where($extraWhere)
            ->field($field)
            ->find();
        $archive = Db::name('order_history')
            ->where($where)
            ->where('paytype', $paytype)
            ->where($extraWhere)
            ->field($field)
            ->find();
        // 合并结果
        $result = [];
        if ($main) {
            foreach ($main as $k => $v) {
                $result[$k] = $v;
            }
        }
        if ($archive) {
            foreach ($archive as $k => $v) {
                if (isset($result[$k])) {
                    $result[$k] += $v;
                } else {
                    $result[$k] = $v;
                }
            }
        }
        return $result;
    }

    /**
     * 合并主表(order)和归档表(order_history)的分组统计数据
     * @param array $where 查询条件
     * @param string $paytype 支付类型
     * @param string $field 查询字段
     * @param string $group 分组字段
     * @param array $extraWhere 额外条件
     * @return array
     */
    private function getOrderGroupStats($where, $paytype, $field, $group, $extraWhere = [])
    {
        $main = Db::name('order')
            ->where($where)
            ->where('paytype', $paytype)
            ->where($extraWhere)
            ->field($field)
            ->group($group)
            ->order($group . ' asc')
            ->select();
        $archive = Db::name('order_history')
            ->where($where)
            ->where('paytype', $paytype)
            ->where($extraWhere)
            ->field($field)
            ->group($group)
            ->order($group . ' asc')
            ->select();
        // 合并分组结果
        $map = [];
        foreach ($main as $row) {
            $map[$row[$group]] = $row;
        }
        foreach ($archive as $row) {
            if (isset($map[$row[$group]])) {
                foreach ($row as $k => $v) {
                    if ($k != $group) {
                        $map[$row[$group]][$k] += $v;
                    }
                }
            } else {
                $map[$row[$group]] = $row;
            }
        }
        // 返回有序数组
        ksort($map);
        return array_values($map);
    }

    /**
     * 获取历史区间数据（不含今天）
     */
    private function getOverviewData($baseWhere, $paytype, $startDate, $endDate)
    {
        // 全部订单用create_time
        $totalWhere = $baseWhere;
        if ($startDate) {
            $totalWhere['create_time'][] = ['egt', strtotime($startDate . ' 00:00:00')];
        }
        if ($endDate) {
            $totalWhere['create_time'][] = ['elt', strtotime($endDate . ' 23:59:59')];
        }
        $totalStats = $this->getOrderStats(
            $totalWhere,
            $paytype,
            'COUNT(*) as total_count, SUM(amount) as total_amount'
        );

        // 成功订单用callback_time
        $paidWhere = $baseWhere;
        if ($startDate) {
            $paidWhere['callback_time'][] = ['egt', strtotime($startDate . ' 00:00:00')];
        }
        if ($endDate) {
            $paidWhere['callback_time'][] = ['elt', strtotime($endDate . ' 23:59:59')];
        }
        $paidStats = $this->getOrderStats(
            $paidWhere,
            $paytype,
            'COUNT(*) as success_count, SUM(amount) as success_amount',
            ['callback_status' => 1]
        );

        $successRate = $totalStats['total_count'] > 0
            ? round(($paidStats['success_count'] / $totalStats['total_count']) * 100, 2)
            : 0;
        return [
            'amount' => $paidStats['success_amount'] ?: 0,
            'orderCount' => $totalStats['total_count'] ?: 0,
            'successRate' => $successRate,
            'orderTotalAmount' => $totalStats['total_amount'] ?: 0
        ];
    }

    /**
     * 获取仪表盘数据（历史自动缓存+今日实时查询+合并返回）
     */
    public function overview()
    {
        try {
            $today = date('Y-m-d');
            $startDate = input('startDate/s', $today);
            $endDate = input('endDate/s', $today);
            $baseWhere = $this->getBaseWhere();
            $paytype = 'lfwx';

            // 历史区间（不含今天）
            $historyEnd = date('Y-m-d', strtotime('-1 day'));
            $historyData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($startDate < $today && $historyEnd >= $startDate) {
                $cacheKey = 'overview_history_' . $paytype . '_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
                $historyData = \think\Cache::get($cacheKey);
                if ($historyData === false) {
                    $historyData = $this->getOverviewData($baseWhere, $paytype, $startDate, $historyEnd);
                    \think\Cache::set($cacheKey, $historyData, 86400);
                }
            }

            // 今天的数据
            $todayData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($endDate >= $today) {
                $todayData = $this->getOverviewData($baseWhere, $paytype, $today, $endDate);
            }

            // 合并
            $totalOrderCount = $historyData['orderCount'] + $todayData['orderCount'];
            $totalAmount = $historyData['amount'] + $todayData['amount'];
            $totalOrderTotalAmount = $historyData['orderTotalAmount'] + $todayData['orderTotalAmount'];
            $successRate = $totalOrderCount > 0 ? round(($totalAmount / $totalOrderTotalAmount) * 100, 2) : 0;
            $data = [
                'todayAmount' => $todayData['amount'],
                'todayOrderCount' => $todayData['orderCount'],
                'todaySuccessRate' => $todayData['successRate'],
                'todayOrderTotalAmount' => $todayData['orderTotalAmount'],
                'historyAmount' => $historyData['amount'],
                'historyOrderCount' => $historyData['orderCount'],
                'historySuccessRate' => $historyData['successRate'],
                'historyOrderTotalAmount' => $historyData['orderTotalAmount'],
                'totalAmount' => $totalAmount,
                'totalOrderCount' => $totalOrderCount,
                'totalOrderTotalAmount' => $totalOrderTotalAmount,
                'totalSuccessRate' => $successRate
            ];
            $this->success('获取仪表盘数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取支付宝数据（历史自动缓存+今日实时查询+合并返回）
     */
    public function overview_ali()
    {
        try {
            $today = date('Y-m-d');
            $startDate = input('startDate/s', $today);
            $endDate = input('endDate/s', $today);
            $baseWhere = $this->getBaseWhere();
            $paytype = 'lfali';

            // 历史区间（不含今天）
            $historyEnd = date('Y-m-d', strtotime('-1 day'));
            $historyData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($startDate < $today && $historyEnd >= $startDate) {
                $cacheKey = 'overview_history_' . $paytype . '_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
                $historyData = \think\Cache::get($cacheKey);
                if ($historyData === false) {
                    $historyData = $this->getOverviewData($baseWhere, $paytype, $startDate, $historyEnd);
                    \think\Cache::set($cacheKey, $historyData, 86400);
                }
            }

            // 今天的数据
            $todayData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($endDate >= $today) {
                $todayData = $this->getOverviewData($baseWhere, $paytype, $today, $endDate);
            }

            // 合并
            $totalOrderCount = $historyData['orderCount'] + $todayData['orderCount'];
            $totalAmount = $historyData['amount'] + $todayData['amount'];
            $totalOrderTotalAmount = $historyData['orderTotalAmount'] + $todayData['orderTotalAmount'];
            $successRate = $totalOrderCount > 0 ? round(($totalAmount / $totalOrderTotalAmount) * 100, 2) : 0;
            $data = [
                'todayAmount' => $todayData['amount'],
                'todayOrderCount' => $todayData['orderCount'],
                'todaySuccessRate' => $todayData['successRate'],
                'todayOrderTotalAmount' => $todayData['orderTotalAmount'],
                'historyAmount' => $historyData['amount'],
                'historyOrderCount' => $historyData['orderCount'],
                'historySuccessRate' => $historyData['successRate'],
                'historyOrderTotalAmount' => $historyData['orderTotalAmount'],
                'totalAmount' => $totalAmount,
                'totalOrderCount' => $totalOrderCount,
                'totalOrderTotalAmount' => $totalOrderTotalAmount,
                'totalSuccessRate' => $successRate
            ];
            $this->success('获取仪表盘数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取历史区间渠道数据（不含今天）
     */
    private function getChannelsNewData($baseWhere, $paytype, $startDate, $endDate)
    {
        // 1. 统计全部订单（按create_time分组）
        $totalStats = $this->getOrderGroupStats(
            $baseWhere,
            $paytype,
            'FROM_UNIXTIME(create_time, "%Y-%m-%d") as date, COUNT(*) as total_count, SUM(amount) as total_amount',
            'date',
            ['create_time' => ['between', [strtotime($startDate . ' 00:00:00'), strtotime($endDate . ' 23:59:59')]]]
        );

        // 2. 统计成功订单（按callback_time分组）
        $successStats = $this->getOrderGroupStats(
            $baseWhere,
            $paytype,
            'FROM_UNIXTIME(callback_time, "%Y-%m-%d") as date, COUNT(*) as success_count, SUM(amount) as success_amount',
            'date',
            [
                'callback_status' => 1,
                'callback_time' => ['between', [strtotime($startDate . ' 00:00:00'), strtotime($endDate . ' 23:59:59')]]
            ]
        );

        // 3. 合并数据
        $successMap = [];
        foreach ($successStats as $row) {
            $successMap[$row['date']] = $row;
        }

        $data = [];
        foreach ($totalStats as $stat) {
            $date = $stat['date'];
            $success = isset($successMap[$date]) ? $successMap[$date] : ['success_count' => 0, 'success_amount' => 0];
            $successRate = $stat['total_count'] > 0
                ? round(($success['success_count'] / $stat['total_count']) * 100, 2)
                : 0;
            $data[] = [
                'date' => $date,
                'volumeAmount' => $stat['total_amount'] ?: 0,
                'amount' => $success['success_amount'] ?: 0,
                'orderCount' => $stat['total_count'] ?: 0,
                'successOrderCount' => $success['success_count'] ?: 0,
                'successRate' => $successRate
            ];
        }
        return $data;
    }

    /**
     * 渠道数据（只统计历史数据，不含今天）
     */
    public function channels_new()
    {
        try {
            $today = date('Y-m-d');
            $startDate = input('startDate/s', $today);
            $endDate = input('endDate/s', $today);
            $baseWhere = $this->getBaseWhere();
            $historyEnd = date('Y-m-d', strtotime('-1 day'));

            // 历史数据缓存key
            $wechatHistoryKey = 'channels_new_history_wechat_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
            $alipayHistoryKey = 'channels_new_history_alipay_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
            $wechatHistory = [];
            $alipayHistory = [];
            if ($startDate < $today && $historyEnd >= $startDate) {
                $wechatHistory = \think\Cache::get($wechatHistoryKey);
                if ($wechatHistory === false) {
                    $wechatHistory = $this->getChannelsNewData($baseWhere, 'lfwx', $startDate, $historyEnd);
                    \think\Cache::set($wechatHistoryKey, $wechatHistory, 86400);
                }
                $alipayHistory = \think\Cache::get($alipayHistoryKey);
                if ($alipayHistory === false) {
                    $alipayHistory = $this->getChannelsNewData($baseWhere, 'lfali', $startDate, $historyEnd);
                    \think\Cache::set($alipayHistoryKey, $alipayHistory, 86400);
                }
            }

            // 不统计今天的数据
            $data = [
                'wechat' => $wechatHistory,
                'alipay' => $alipayHistory
            ];
            $this->success('获取按日期统计数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取最近一小时成功率（5分钟前的一小时），直接输出lfwx和lfali
     */
    public function recent_hour_success_rate()
    {
        try {
            $baseWhere = $this->getBaseWhere();
            $now = time();
            $end = $now - 300; // 5分钟前
            $start = $end - 3600; // 向前1小时
            $result = [];
            foreach (["lfwx", "lfali"] as $paytype) {
                // 全部订单数
                $totalStats = $this->getOrderStats(
                    array_merge($baseWhere, ['create_time' => ['between', [$start, $end]]]),
                    $paytype,
                    'COUNT(*) as total_count'
                );
                // 成功订单数
                $successStats = $this->getOrderStats(
                    array_merge($baseWhere, ['create_time' => ['between', [$start, $end]], 'pay_status' => 1]),
                    $paytype,
                    'COUNT(*) as success_count'
                );
                $successRate = $totalStats['total_count'] > 0 ? round(($successStats['success_count'] / $totalStats['total_count']) * 100, 2) : 0;
                $result[$paytype] = [
                    'successRate' => $successRate,
                    'totalCount' => $totalStats['total_count'],
                    'successCount' => $successStats['success_count'],
                    'startTime' => date('Y-m-d H:i:s', $start),
                    'endTime' => date('Y-m-d H:i:s', $end)
                ];
            }
            $this->success('获取最近一小时成功率成功', $result);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取最近5分钟订单量，直接输出lfwx和lfali
     */
    public function recent_five_minute_order_count()
    {
        try {
            $baseWhere = $this->getBaseWhere();
            $now = time();
            $start = $now - 300; // 5分钟前
            $end = $now;
            $result = [];
            foreach (["lfwx", "lfali"] as $paytype) {
                $orderStats = $this->getOrderStats(
                    array_merge($baseWhere, ['create_time' => ['between', [$start, $end]]]),
                    $paytype,
                    'COUNT(*) as order_count'
                );
                $result[$paytype] = [
                    'orderCount' => $orderStats['order_count'],
                    'startTime' => date('Y-m-d H:i:s', $start),
                    'endTime' => date('Y-m-d H:i:s', $end)
                ];
            }
            $this->success('获取最近5分钟订单量成功', $result);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }
}
