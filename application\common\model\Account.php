<?php

namespace app\common\model;

use think\Db;
use think\Model;
use think\Validate;


/**
 * 账号模型
 */
class Account extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
    ];

    // public function getCreatetimeAttr($value)
    // {
    //     return date('Y-m-d H:i:s', $value);
    // }

    // public function getPulltimeAttr($value)
    // {
    //     return date('Y-m-d H:i:s', $value);
    // }

    // 定义与用户表的关联关系
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }
}
