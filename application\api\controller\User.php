<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Config;
use think\Validate;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login'];
    protected $noNeedRight = ['info', 'logout', 'profile'];

    public function _initialize()
    {
        parent::_initialize();

        // if (!Config::get('fastadmin.usercenter')) {
        //     $this->error(__('User center already closed'));
        // }

    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('username');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=false, description="用户名")
     * @ApiParams (name="nickname", type="string", required=false, description="昵称")
     * @ApiParams (name="oldPassword", type="string", required=false, description="旧密码")
     * @ApiParams (name="newPassword", type="string", required=false, description="新密码")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $oldPassword = $this->request->post('oldPassword');
        $newPassword = $this->request->post('newPassword');

        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        if ($oldPassword && $newPassword) {
            // 验证新密码格式
            if (!Validate::make()->check(['newPassword' => $newPassword], ['newPassword' => 'require|regex:\S{6,30}'])) {
                $this->error(__('Password must be 6 to 30 characters'));
            }
            // 验证旧密码是否正确并修改新密码
            $ret = $this->auth->changepwd($newPassword, $oldPassword);
            if (!$ret) {
                $this->error($this->auth->getError());
            }
        }
        $user->save();
        $this->success();
    }


    /**
     * 获取用户信息
     */
    public function info()
    {
        $user = $this->auth->getUser();
        $groups = db('user_group')->where('id', $user->group_id)->select();

        // 从 $groups 中提取 name 字段组成新数组
        $group = array_column(is_array($groups) ? $groups : (array)$groups, 'name');

        $this->success('查询成功', ['roles' => $group, 'nickname' => $user->nickname, 'username' => $user->username, 'money' => $user->money, 'logintime' => $user->logintime, 'loginip' => $user->loginip]);
    }

    /**
     * 添加用户
     */
    public function add()
    {
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $password = $this->request->post('password');
        $status = $this->request->post('status');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if (\app\common\model\User::getByUsername($username)) {
            $this->error(__('Username already exists'));
        }
        if (\app\common\model\User::getByNickname($nickname)) {
            $this->error(__('Nickname already exists'));
        }
        $ip = request()->ip();
        $time = time();
        $group_id = 2;
        $data = [
            'username' => $username,
            'password' => $password,
            'level'    => 1,
            'score'    => 0,
            'avatar'   => '',
        ];
        $params = array_merge($data, [
            'nickname'  => $nickname,
            'salt'      => Random::alnum(),
            'jointime'  => $time,
            'group_id'  => $group_id,
            'joinip'    => $ip,
            'logintime' => $time,
            'loginip'   => $ip,
            'prevtime'  => $time,
            'status'    => $status
        ]);
        $params['password'] = $this->auth->getEncryptPassword($password, $params['salt']);

        //需要开启事务,避免出现垃圾数据
        \think\Db::startTrans();
        try {
            $user = \app\common\model\User::create($params, true);
            \think\Db::commit();
        } catch (\think\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('创建用户成功');
    }

    /**
     * 获取用户列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'username'   => input('username/s', ''),
            'nickname'   => input('nickname/s', ''),
            'status'    => input('status/s', '')
        ];

        // 验证status参数
        $allowedStatus = ['normal', 'hidden', 'locked'];
        $where = [];

        $where['id'] = ['<>', $this->auth->id];

        // 构建查询条件
        if ($params['username'] !== '') {
            $where['username'] = ['like', "%{$params['username']}%"];
        }
        if ($params['nickname'] !== '') {
            $where['nickname'] = ['like', "%{$params['nickname']}%"];
        }
        if ($params['status'] !== '' && in_array($params['status'], $allowedStatus)) {
            $where['status'] = $params['status'];
        }

        // 获取今日和昨日的时间戳
        $today = strtotime(date('Y-m-d 00:00:00'));
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 查询数据
        $list = \think\Db::name('user')
            ->where($where)
            ->field('id,username,nickname,money,status')
            ->order('id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 查询总数
        $total = \think\Db::name('user')
            ->where($where)
            ->count();

        // 获取所有用户ID
        $userIds = array_column(is_array($list) ? $list : (array)$list, 'id');

        $mainStats = [];
        $archiveStats = [];
        if (!empty($userIds)) {
            // 主表批量统计
            $mainStatsRaw = \think\Db::name('order')
                ->field([
                    'user_id',
                    'SUM(CASE WHEN callback_status = 1 THEN amount ELSE 0 END) as total_amount',
                    'SUM(CASE WHEN callback_status = 1 AND callback_time >= ' . $today . ' THEN amount ELSE 0 END) as today_amount',
                    'SUM(CASE WHEN callback_status = 1 AND callback_time >= ' . $yesterdayStart . ' AND callback_time <= ' . $yesterdayEnd . ' THEN amount ELSE 0 END) as yesterday_amount'
                ])
                ->where('user_id', 'in', $userIds)
                ->group('user_id')
                ->select();
            $mainStatsRaw = is_array($mainStatsRaw) ? $mainStatsRaw : $mainStatsRaw->toArray();
            foreach ($mainStatsRaw as $row) {
                $mainStats[$row['user_id']] = $row;
            }
            // 归档表批量统计
            $archiveStatsRaw = \think\Db::name('order_history')
                ->field([
                    'user_id',
                    'SUM(CASE WHEN callback_status = 1 THEN amount ELSE 0 END) as total_amount',
                    'SUM(CASE WHEN callback_status = 1 AND callback_time >= ' . $today . ' THEN amount ELSE 0 END) as today_amount',
                    'SUM(CASE WHEN callback_status = 1 AND callback_time >= ' . $yesterdayStart . ' AND callback_time <= ' . $yesterdayEnd . ' THEN amount ELSE 0 END) as yesterday_amount'
                ])
                ->where('user_id', 'in', $userIds)
                ->group('user_id')
                ->select();
            $archiveStatsRaw = is_array($archiveStatsRaw) ? $archiveStatsRaw : $archiveStatsRaw->toArray();
            foreach ($archiveStatsRaw as $row) {
                $archiveStats[$row['user_id']] = $row;
            }
        }
        // 合并主表和归档表统计
        $statsMap = [];
        foreach ($userIds as $uid) {
            $main = isset($mainStats[$uid]) ? $mainStats[$uid] : [
                'today_amount' => 0,
                'yesterday_amount' => 0,
                'total_amount' => 0
            ];
            $archive = isset($archiveStats[$uid]) ? $archiveStats[$uid] : [
                'today_amount' => 0,
                'yesterday_amount' => 0,
                'total_amount' => 0
            ];
            $statsMap[$uid] = [
                'today_amount' => $main['today_amount'] + $archive['today_amount'],
                'yesterday_amount' => $main['yesterday_amount'] + $archive['yesterday_amount'],
                'total_amount' => $main['total_amount'] + $archive['total_amount'],
            ];
        }
        // 为每个用户添加收款统计
        foreach ($list as &$user) {
            $uid = $user['id'];
            $user['todayAmount'] = number_format($statsMap[$uid]['today_amount'] ?? 0, 2, '.', '');
            $user['yesterdayAmount'] = number_format($statsMap[$uid]['yesterday_amount'] ?? 0, 2, '.', '');
            $user['totalAmount'] = number_format($statsMap[$uid]['total_amount'] ?? 0, 2, '.', '');
        }

        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 删除用户
     */
    public function delete()
    {
        $id = input('id');
        if (!$id) {
            $this->error('用户ID不能为空');
        }
        // 只有管理员可以删除用户
        $user = \app\common\model\User::where('id', $id)->find();
        if (!$user) {
            $this->error('用户不存在');
        }
        if ($this->auth->group_id != 1) {
            $this->error('您没有权限删除该用户');
        }
        $user->delete();
        $this->success('删除用户成功');
    }

    /**
     * 修改用户状态
     */
    public function status()
    {
        $id = input('id');
        if (!$id) {
            $this->error('用户ID不能为空');
        }
        $user = \app\common\model\User::where('id', $id)->find();
        if (!$user) {
            $this->error('用户不存在');
        }
        // 只有管理员可以修改用户状态
        if ($this->auth->group_id != 1) {
            $this->error('您没有权限修改该用户状态');
        }
        $status = $this->request->post('status');
        $user->status = $status;
        $user->save();
        $this->success('修改用户状态成功');
    }

    /**
     * 修改用户信息
     */
    public function update()
    {
        $id = input('id');
        if (!$id) {
            $this->error('用户ID不能为空');
        }
        $nickname = input('nickname');
        $status = input('status');
        $newPassword = input('password');
        $user = \app\common\model\User::where('id', $id)->find();
        if (!$user) {
            $this->error('用户不存在');
        }
        // 只有管理员可以修改用户信息
        if ($this->auth->group_id != 1) {
            $this->error('您没有权限修改该用户信息');
        }
        $user->nickname = $nickname;
        $user->status = $status;
        if ($newPassword) {
            // 验证新密码格式
            if (!Validate::make()->check(['newPassword' => $newPassword], ['newPassword' => 'require|regex:\S{6,30}'])) {
                $this->error(__('Password must be 6 to 30 characters'));
            }
            $salt = Random::alnum();
            $newpassword = $this->auth->getEncryptPassword($newPassword, $salt);
            $user->password = $newpassword;
            $user->salt = $salt;
        }
        $user->save();

        $this->success('修改用户信息成功');
    }

    /**
     * 增减余额
     */
    public function balance()
    {
        $id = input('id');
        $balance = input('amount');
        $remark = input('remark');
        if (!$id) {
            $this->error('用户ID不能为空');
        }
        // 只有管理员可以修改用户余额
        if ($this->auth->group_id != 1) {
            $this->error('您没有权限修改该用户余额');
        }
        \app\common\model\User::money($balance, $id, $remark);

        $this->success('修改用户余额成功');
    }
}
