<?php

namespace app\common\model;

use think\Db;
use think\Model;
use think\Validate;


/**
 * 订单模型
 */
class Order extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    // 追加属性
    protected $append = [
    ];

    // public function getCreatetimeAttr($value)
    // {
    //     return date('Y-m-d H:i:s', $value);
    // }

    // public function getPulltimeAttr($value)
    // {
    //     return date('Y-m-d H:i:s', $value);
    // }

    // 定义与用户表的关联关系
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }

    // 定义与商户表的关联关系
    public function merchant()
    {
        return $this->belongsTo('Merchant', 'merchant_id', 'id');
    }

    // 定义与账号表的关联关系
    public function account()
    {
        return $this->belongsTo('Account', 'account_id', 'id');
    }
}
