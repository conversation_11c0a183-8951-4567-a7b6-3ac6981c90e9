(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c09ff98c"],{"057d":function(e,t,a){},"0ccb":function(e,t,a){var n=a("50c4"),r=a("1148"),i=a("1d80"),s=Math.ceil,l=function(e){return function(t,a,l){var o,c,u=String(i(t)),d=u.length,m=void 0===l?" ":String(l),p=n(a);return p<=d||""==m?u:(o=p-d,c=r.call(m,s(o/m.length)),c.length>o&&(c=c.slice(0,o)),e?u+c:c+u)}};e.exports={start:l(!1),end:l(!0)}},1148:function(e,t,a){"use strict";var n=a("a691"),r=a("1d80");e.exports="".repeat||function(e){var t=String(r(this)),a="",i=n(e);if(i<0||i==1/0)throw RangeError("Wrong number of repetitions");for(;i>0;(i>>>=1)&&(t+=t))1&i&&(a+=t);return a}},"408a":function(e,t,a){var n=a("c6b6");e.exports=function(e){if("number"!=typeof e&&"Number"!=n(e))throw TypeError("Incorrect invocation");return+e}},"4d90":function(e,t,a){"use strict";var n=a("23e7"),r=a("0ccb").start,i=a("9a0c");n({target:"String",proto:!0,forced:i},{padStart:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})},"9a0c":function(e,t,a){var n=a("342f");e.exports=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(n)},b680:function(e,t,a){"use strict";var n=a("23e7"),r=a("a691"),i=a("408a"),s=a("1148"),l=a("d039"),o=1..toFixed,c=Math.floor,u=function(e,t,a){return 0===t?a:t%2===1?u(e,t-1,a*e):u(e*e,t/2,a)},d=function(e){var t=0,a=e;while(a>=4096)t+=12,a/=4096;while(a>=2)t+=1,a/=2;return t},m=o&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!l((function(){o.call({})}));n({target:"Number",proto:!0,forced:m},{toFixed:function(e){var t,a,n,l,o=i(this),m=r(e),p=[0,0,0,0,0,0],f="",b="0",g=function(e,t){var a=-1,n=t;while(++a<6)n+=e*p[a],p[a]=n%1e7,n=c(n/1e7)},h=function(e){var t=6,a=0;while(--t>=0)a+=p[t],p[t]=c(a/e),a=a%e*1e7},v=function(){var e=6,t="";while(--e>=0)if(""!==t||0===e||0!==p[e]){var a=String(p[e]);t=""===t?a:t+s.call("0",7-a.length)+a}return t};if(m<0||m>20)throw RangeError("Incorrect fraction digits");if(o!=o)return"NaN";if(o<=-1e21||o>=1e21)return String(o);if(o<0&&(f="-",o=-o),o>1e-21)if(t=d(o*u(2,69,1))-69,a=t<0?o*u(2,-t,1):o/u(2,t,1),a*=****************,t=52-t,t>0){g(0,a),n=m;while(n>=7)g(1e7,0),n-=7;g(u(10,n,1),0),n=t-1;while(n>=23)h(1<<23),n-=23;h(1<<n),g(1,1),h(2),b=v()}else g(0,a),g(1<<-t,0),b=v()+s.call("0",m);return m>0?(l=b.length,b=f+(l<=m?"0."+s.call("0",m-l)+b:b.slice(0,l-m)+"."+b.slice(l-m))):b=f+b,b}})},e382:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"用户名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.username,callback:function(t){e.$set(e.listQuery,"username",t)},expression:"listQuery.username"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"昵称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.nickname,callback:function(t){e.$set(e.listQuery,"nickname",t)},expression:"listQuery.nickname"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v(" 新增 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{prop:"id",label:"用户ID",width:"80",align:"center"}}),a("el-table-column",{attrs:{prop:"username",label:"用户名","min-width":"120",align:"center"}}),a("el-table-column",{attrs:{prop:"nickname",label:"昵称","min-width":"120",align:"center"}}),a("el-table-column",{attrs:{prop:"money",label:"余额","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:["money-value",t.row.money>=0?"money-positive":"money-negative"]},[e._v(" "+e._s(e._f("formatMoney")(t.row.money))+" ")])]}}])}),a("el-table-column",{attrs:{label:"收款金额",align:"center"}},[a("el-table-column",{attrs:{prop:"todayAmount",label:"今日收款",width:"90",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"today-amount"},[e._v(e._s(e.formatAmount(t.row.todayAmount)))])]}}])}),a("el-table-column",{attrs:{prop:"yesterdayAmount",label:"昨日收款",width:"90",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"amount-text"},[e._v(e._s(e.formatAmount(t.row.yesterdayAmount)))])]}}])}),a("el-table-column",{attrs:{prop:"totalAmount",label:"总收款",width:"90",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"amount-text"},[e._v(e._s(e.formatAmount(t.row.totalAmount)))])]}}])})],1),a("el-table-column",{attrs:{prop:"status",label:"状态","min-width":"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"normal","inactive-value":"hidden"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{prop:"createtime",label:"登录时间","min-width":"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTimestamp(t.row.createtime))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v(" 编辑 ")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"warning",icon:"el-icon-money"},on:{click:function(a){return e.handleBalance(t.row)}}},[e._v(" 余额 ")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(" 删除 ")])]}}])})],1),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"","current-page":e.currentPage,"page-sizes":[10,20,30,50],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),a("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"500px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"dataForm",attrs:{model:e.temp,"label-position":"right","label-width":"100px",rules:e.rules}},["新增用户"===e.dialogTitle?a("el-form-item",{attrs:{label:"用户名",prop:"username"}},[a("el-input",{attrs:{placeholder:"请输入用户名"},model:{value:e.temp.username,callback:function(t){e.$set(e.temp,"username",t)},expression:"temp.username"}})],1):e._e(),a("el-form-item",{attrs:{label:"昵称",prop:"nickname"}},[a("el-input",{attrs:{placeholder:"请输入昵称"},model:{value:e.temp.nickname,callback:function(t){e.$set(e.temp,"nickname",t)},expression:"temp.nickname"}})],1),"新增用户"===e.dialogTitle?[a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{type:"password",placeholder:"请输入密码"},model:{value:e.temp.password,callback:function(t){e.$set(e.temp,"password",t)},expression:"temp.password"}})],1)]:[a("el-form-item",{attrs:{label:"修改密码"}},[a("el-switch",{model:{value:e.changePassword,callback:function(t){e.changePassword=t},expression:"changePassword"}})],1),e.changePassword?[a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{type:"password",placeholder:"请输入新密码"},model:{value:e.temp.password,callback:function(t){e.$set(e.temp,"password",t)},expression:"temp.password"}})],1)]:e._e()],a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.temp.status,callback:function(t){e.$set(e.temp,"status",t)},expression:"temp.status"}},[a("el-radio",{attrs:{label:"normal"}},[e._v("启用")]),a("el-radio",{attrs:{label:"hidden"}},[e._v("禁用")])],1)],1)],2),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.saveData}},[e._v("确 定")])],1)],1),a("el-dialog",{attrs:{title:"增减余额",visible:e.balanceDialogVisible,width:"400px"},on:{"update:visible":function(t){e.balanceDialogVisible=t}}},[a("el-form",{ref:"balanceForm",attrs:{model:e.balanceForm,"label-position":"right","label-width":"100px",rules:e.balanceRules}},[a("el-form-item",{attrs:{label:"当前余额"}},[a("span",[e._v(e._s(e._f("formatMoney")(e.balanceForm.currentMoney)))])]),a("el-form-item",{attrs:{label:"操作类型",prop:"type"}},[a("el-radio-group",{model:{value:e.balanceForm.type,callback:function(t){e.$set(e.balanceForm,"type",t)},expression:"balanceForm.type"}},[a("el-radio",{attrs:{label:"add"}},[e._v("增加")]),a("el-radio",{attrs:{label:"subtract"}},[e._v("减少")])],1)],1),a("el-form-item",{attrs:{label:"变更金额",prop:"amount"}},[a("el-input-number",{staticStyle:{width:"200px"},attrs:{min:0,precision:2,step:10},model:{value:e.balanceForm.amount,callback:function(t){e.$set(e.balanceForm,"amount",t)},expression:"balanceForm.amount"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入备注信息"},model:{value:e.balanceForm.remark,callback:function(t){e.$set(e.balanceForm,"remark",t)},expression:"balanceForm.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.balanceDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.saveBalance}},[e._v("确 定")])],1)],1)],1)},r=[],i=a("c7eb"),s=a("5530"),l=a("1da1"),o=(a("99af"),a("d81d"),a("b680"),a("d3b7"),a("25f0"),a("4d90"),a("a573"),a("c24f")),c={name:"User",filters:{formatMoney:function(e){return e?"¥".concat(e):"0.00"},formatStatus:function(e){var t={normal:"启用",hidden:"禁用"};return t[e]||e}},data:function(){return{listQuery:{username:"",nickname:"",role:"",status:"",page:1,limit:10},roleOptions:[{key:"admin",label:"管理员"},{key:"editor",label:"编辑员"}],statusOptions:[{key:"normal",label:"启用"},{key:"hidden",label:"禁用"}],tableData:[],currentPage:1,pageSize:10,total:0,loading:!1,dialogVisible:!1,dialogTitle:"",temp:{username:"",nickname:"",password:"",status:"normal"},changePassword:!1,rules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少为6个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},balanceDialogVisible:!1,balanceForm:{userId:null,username:"",currentMoney:0,type:"add",amount:0,remark:""},balanceRules:{type:[{required:!0,message:"请选择操作类型",trigger:"change"}],amount:[{required:!0,message:"请输入变更金额",trigger:"blur"}],remark:[{required:!0,message:"请输入备注信息",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getStatusLabel:function(e){var t={normal:"启用",hidden:"禁用"};return t[e]||e},getList:function(){var e=this;return Object(l["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.loading=!0;try{Object(o["d"])(e.listQuery).then((function(t){var a=t.data,n=a.list,r=a.total,i=a.page,l=a.limit;e.tableData=n.map((function(t){return Object(s["a"])(Object(s["a"])({},t),{},{statusLabel:e.getStatusLabel(t.status),loginTime:e.formatTimestamp(t.createtime)})})),e.total=r,e.currentPage=i,e.pageSize=l})).finally((function(){e.loading=!1}))}catch(a){e.$message.error("获取数据失败"),console.error("Error:",a)}case 2:case"end":return t.stop()}}),t)})))()},formatTimestamp:function(e){if(!e)return"-";var t=new Date(10===e.toString().length?1e3*e:e),a=t.getFullYear(),n=(t.getMonth()+1).toString().padStart(2,"0"),r=t.getDate().toString().padStart(2,"0"),i=t.getHours().toString().padStart(2,"0"),s=t.getMinutes().toString().padStart(2,"0"),l=t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(n,"-").concat(r," ").concat(i,":").concat(s,":").concat(l)},handleFilter:function(){this.listQuery.page=1,this.getList()},handleAdd:function(){var e=this;this.dialogTitle="新增用户",this.changePassword=!1,this.temp={username:"",nickname:"",password:"",status:"normal"},this.dialogVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},handleEdit:function(e){var t=this;this.dialogTitle="编辑用户",this.changePassword=!1,this.temp={id:e.id,username:e.username,nickname:e.nickname,password:"",status:e.status},this.dialogVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},handleDelete:function(e){var t=this;return Object(l["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$confirm("确认删除该用户吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 3:Object(o["b"])({id:e.id}).then((function(e){t.$message.success("删除成功"),t.getList()})),a.next=9;break;case 6:a.prev=6,a.t0=a["catch"](0),"cancel"!==a.t0&&(t.$message.error("删除失败"),console.error("Error:",a.t0));case 9:case"end":return a.stop()}}),a,null,[[0,6]])})))()},handleStatusChange:function(e){var t=this;return Object(l["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:Object(o["j"])({id:e.id,status:e.status}).then((function(a){t.$message.success("用户 ".concat(e.username," 状态已更新"))}));case 1:case"end":return a.stop()}}),a)})))()},handleExport:function(){this.$message.success("触发了导出功能")},handleSizeChange:function(e){this.listQuery.limit=e,this.getList()},handleCurrentChange:function(e){this.listQuery.page=e,this.getList()},saveData:function(){var e=this;return Object(l["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$refs["dataForm"].validate(function(){var t=Object(l["a"])(Object(i["a"])().mark((function t(a){var n,r,l,c;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=16;break}return t.prev=1,n="新增用户"===e.dialogTitle,r=n?o["a"]:o["i"],l=Object(s["a"])({},e.temp),n||e.changePassword||delete l.password,t.next=8,r(l);case 8:c=t.sent,1===c.code?(e.$message.success(n?"新增成功":"编辑成功"),e.dialogVisible=!1,e.getList()):e.$message.error(c.msg||(n?"新增失败":"编辑失败")),t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](1),e.$message.error("新增用户"===e.dialogTitle?"新增失败":"编辑失败"),console.error("Error:",t.t0);case 16:case"end":return t.stop()}}),t,null,[[1,12]])})));return function(e){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t)})))()},handleBalance:function(e){var t=this;this.balanceDialogVisible=!0,this.balanceForm={userId:e.id,username:e.username,currentMoney:e.money||0,type:"add",amount:0,remark:""},this.$nextTick((function(){var e;null===(e=t.$refs["balanceForm"])||void 0===e||e.clearValidate()}))},saveBalance:function(){var e=this;return Object(l["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$refs["balanceForm"].validate(function(){var t=Object(l["a"])(Object(i["a"])().mark((function t(a){var n,r,s,l,c,u;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=13;break}return t.prev=1,n=e.balanceForm,r=n.userId,s=n.type,l=n.amount,c=n.remark,t.next=5,Object(o["g"])({id:r,amount:"add"===s?l:-l,remark:c});case 5:u=t.sent,1===u.code?(e.$message.success("余额调整成功"),e.balanceDialogVisible=!1,e.getList()):e.$message.error(u.msg||"余额调整失败"),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](1),e.$message.error("余额调整失败"),console.error("Error:",t.t0);case 13:case"end":return t.stop()}}),t,null,[[1,9]])})));return function(e){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t)})))()},formatAmount:function(e){if(void 0===e||null===e)return"¥0.00";var t=parseFloat(e);return isNaN(t)?"¥0.00":"¥".concat(t.toFixed(2))}}},u=c,d=(a("fe14"),a("2877")),m=Object(d["a"])(u,n,r,!1,null,"1215fafd",null);t["default"]=m.exports},fe14:function(e,t,a){"use strict";a("057d")}}]);