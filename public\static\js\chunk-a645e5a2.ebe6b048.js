(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a645e5a2"],{"0ccb":function(t,e,a){var s=a("50c4"),i=a("1148"),r=a("1d80"),l=Math.ceil,n=function(t){return function(e,a,n){var c,o,u=String(r(e)),d=u.length,m=void 0===n?" ":String(n),p=s(a);return p<=d||""==m?u:(c=p-d,o=i.call(m,l(c/m.length)),o.length>c&&(o=o.slice(0,c)),t?u+o:o+u)}};t.exports={start:n(!1),end:n(!0)}},1148:function(t,e,a){"use strict";var s=a("a691"),i=a("1d80");t.exports="".repeat||function(t){var e=String(i(this)),a="",r=s(t);if(r<0||r==1/0)throw RangeError("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(a+=e);return a}},"4d90":function(t,e,a){"use strict";var s=a("23e7"),i=a("0ccb").start,r=a("9a0c");s({target:"String",proto:!0,forced:r},{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"634a":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"filter-container",attrs:{shadow:"never"}},[a("el-form",{attrs:{inline:!0,model:t.listQuery,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"订单号"}},[a("div",{staticClass:"order-no-search"},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"商户订单号",clearable:""},on:{input:t.debounceSearch},model:{value:t.listQuery.out_trade_no,callback:function(e){t.$set(t.listQuery,"out_trade_no","string"===typeof e?e.trim():e)},expression:"listQuery.out_trade_no"}})],1)]),a("el-form-item",{attrs:{label:"支付状态"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.listQuery.pay_status,callback:function(e){t.$set(t.listQuery,"pay_status",e)},expression:"listQuery.pay_status"}},t._l(t.payStatusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"回调状态"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.listQuery.callback_status,callback:function(e){t.$set(t.listQuery,"callback_status",e)},expression:"listQuery.callback_status"}},t._l(t.callbackStatusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"创建时间"}},[a("div",{staticClass:"date-filter-container"},[a("el-date-picker",{staticStyle:{width:"380px"},attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期时间","end-placeholder":"结束日期时间","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}}),a("div",{staticClass:"quick-date-filters"},[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(e){return t.setQuickDateRange("hour")}}},[t._v("最近1小时")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(e){return t.setQuickDateRange("today")}}},[t._v("今天")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(e){return t.setQuickDateRange("yesterday")}}},[t._v("昨天")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(e){return t.setQuickDateRange("week")}}},[t._v("最近一周")])],1)],1)]),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",loading:t.searchLoading},on:{click:t.handleSearch}},[t._v("查询")]),a("el-button",{attrs:{icon:"el-icon-refresh",loading:t.searchLoading},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),a("el-card",{staticClass:"table-container",attrs:{shadow:"never"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("订单列表")]),a("div",{staticStyle:{float:"right"}},[a("el-popover",{attrs:{placement:"bottom",width:"200",trigger:"click","popper-class":"column-filter-popover"}},[a("el-checkbox",{on:{change:t.handleCheckAllChange},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}},[t._v("全选")]),a("div",{staticClass:"column-filter-list"},[a("el-checkbox-group",{on:{change:t.handleCheckedColumnsChange},model:{value:t.checkedColumns,callback:function(e){t.checkedColumns=e},expression:"checkedColumns"}},t._l(t.columnOptions,(function(e){return a("el-checkbox",{key:e.prop,attrs:{label:e.prop}},[t._v(" "+t._s(e.label)+" ")])})),1)],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{slot:"reference",size:"mini",type:"primary",plain:"",icon:"el-icon-s-operation"},slot:"reference"},[t._v("列设置")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",type:"primary",plain:"",icon:"el-icon-refresh-right",loading:t.refreshLoading},on:{click:t.handleRefresh}},[t._v("刷新")])],1)]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"compact-table",staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",size:"mini","row-class-name":t.tableRowClassName},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t.isColumnVisible("out_trade_no")?a("el-table-column",{attrs:{label:"订单号","min-width":"180",align:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"order-no-column"},[a("div",{staticClass:"order-no-item"},[a("span",{staticClass:"value copy-value"},[t._v(" "+t._s(e.row.out_trade_no)+" "),a("el-tooltip",{attrs:{content:"复制单号",placement:"top",effect:"light"}},[a("i",{staticClass:"el-icon-document-copy copy-icon",on:{click:function(a){return t.copyToClipboard(e.row.out_trade_no)}}})])],1)])])]}}],null,!1,**********)}):t._e(),t.isColumnVisible("account_name")?a("el-table-column",{attrs:{prop:"account_name",label:"收款账号","min-width":"120",align:"center"}}):t._e(),t.isColumnVisible("amount")?a("el-table-column",{attrs:{prop:"amount",label:"收款金额","min-width":"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-value"},[t._v("¥"+t._s(e.row.amount))])]}}],null,!1,**********)}):t._e(),t.isColumnVisible("paytype")?a("el-table-column",{attrs:{prop:"paytype",label:"通道编码","min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{size:"mini"}},[t._v(t._s(e.row.paytype))])]}}],null,!1,**********)}):t._e(),t.isColumnVisible("pay_status")?a("el-table-column",{attrs:{prop:"pay_status",label:"支付状态","min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{size:"mini",type:t._f("payStatusFilter")(e.row.pay_status)}},[t._v(" "+t._s(t._f("payStatusText")(e.row.pay_status))+" ")])]}}],null,!1,**********)}):t._e(),t.isColumnVisible("callback_status")?a("el-table-column",{attrs:{prop:"callback_status",label:"回调状态","min-width":"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{size:"mini",type:t._f("callbackStatusFilter")(e.row.callback_status)}},[t._v(" "+t._s(t._f("callbackStatusText")(e.row.callback_status))+" ")])]}}],null,!1,1146804837)}):t._e(),t.isColumnVisible("callback_time")?a("el-table-column",{attrs:{prop:"callback_time",label:"回调时间","min-width":"140",align:"center","class-name":"time-column"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("formatTimestamp")(e.row.callback_time))+" ")]}}],null,!1,3821871914)}):t._e(),t.isColumnVisible("create_time")?a("el-table-column",{attrs:{prop:"create_time",label:"创建时间","min-width":"140",align:"center","class-name":"time-column"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("formatTimestamp")(e.row.create_time))+" ")]}}],null,!1,4220073863)}):t._e(),t.isColumnVisible("merchant_name")?a("el-table-column",{attrs:{prop:"merchant_name",label:"商户","min-width":"100",align:"center"}}):t._e(),t.isColumnVisible("user_nickname")?a("el-table-column",{attrs:{prop:"user_nickname",label:"核销","min-width":"100",align:"center"}}):t._e(),t.isColumnVisible("remark")?a("el-table-column",{attrs:{prop:"remark",label:"备注","min-width":"120",align:"center","show-overflow-tooltip":""}}):t._e(),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.handleDetail(e.row)}}},[t._v("详情")]),a("el-button",{attrs:{size:"mini",type:"warning",disabled:"1"===e.row.callback_status||1===e.row.callback_status},on:{click:function(a){return t.handleCallback(e.row)}}},[t._v("回调")])]}}])})],1),a("div",{staticClass:"pagination-container"},[a("el-pagination",{staticClass:"compact-pagination",attrs:{background:"","current-page":t.listQuery.page,"page-sizes":[10,20,30,50],"page-size":t.listQuery.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total,small:""},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"page",e)},"update:current-page":function(e){return t.$set(t.listQuery,"page",e)}}})],1)],1),a("el-dialog",{attrs:{title:"修改备注",visible:t.remarkDialogVisible,width:"30%"},on:{"update:visible":function(e){t.remarkDialogVisible=e}}},[a("el-form",{attrs:{model:t.remarkForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入备注内容"},model:{value:t.remarkForm.remark,callback:function(e){t.$set(t.remarkForm,"remark",e)},expression:"remarkForm.remark"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.remarkDialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitRemark}},[t._v("确 定")])],1)],1),a("el-dialog",{attrs:{title:"订单详情",visible:t.detailDialogVisible,width:"65%","custom-class":"order-detail-dialog","close-on-click-modal":!1},on:{"update:visible":function(e){t.detailDialogVisible=e}}},[a("div",{staticClass:"order-detail-container"},[a("div",{staticClass:"order-detail-header"},[a("div",{staticClass:"order-id-section"},[a("div",{staticClass:"order-id-title"},[a("i",{staticClass:"el-icon-document"}),t._v(" 订单信息 ")]),a("div",{staticClass:"order-id-content"},[a("div",{staticClass:"order-id-item"},[a("span",{staticClass:"label"},[t._v("商户单号：")]),a("span",{staticClass:"value copy-value"},[t._v(" "+t._s(t.currentOrder.out_trade_no)+" "),a("el-tooltip",{attrs:{content:"复制单号",placement:"top",effect:"light"}},[a("i",{staticClass:"el-icon-document-copy copy-icon",on:{click:function(e){return t.copyToClipboard(t.currentOrder.out_trade_no)}}})])],1)])])]),a("div",{staticClass:"order-status-section"},[a("div",{staticClass:"status-item"},[a("div",{staticClass:"status-label"},[t._v("支付状态")]),a("el-tag",{attrs:{size:"medium",type:t._f("payStatusFilter")(t.currentOrder.pay_status),effect:"dark"}},[a("i",{class:1==t.currentOrder.pay_status?"el-icon-check":"el-icon-time"}),t._v(" "+t._s(t._f("payStatusText")(t.currentOrder.pay_status))+" ")])],1),a("div",{staticClass:"status-item"},[a("div",{staticClass:"status-label"},[t._v("回调状态")]),a("el-tag",{attrs:{size:"medium",type:t._f("callbackStatusFilter")(t.currentOrder.callback_status),effect:"dark"}},[a("i",{class:1==t.currentOrder.callback_status?"el-icon-check":"el-icon-time"}),t._v(" "+t._s(t._f("callbackStatusText")(t.currentOrder.callback_status))+" ")])],1)])]),a("div",{staticClass:"detail-cards-container"},[a("el-card",{staticClass:"detail-card payment-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-money"}),t._v(" 支付信息 ")]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("收款账号")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentOrder.account_name))])]),a("div",{staticClass:"detail-item amount-item"},[a("span",{staticClass:"detail-label"},[t._v("收款金额")]),a("span",{staticClass:"detail-value highlight"},[t._v("¥"+t._s(t.currentOrder.amount))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("通道编码")]),a("span",{staticClass:"detail-value"},[a("el-tag",{staticClass:"centered-tag",attrs:{size:"medium",effect:"plain"}},[t._v(t._s(t.currentOrder.paytype))])],1)])])]),a("el-card",{staticClass:"detail-card time-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-time"}),t._v(" 时间信息 ")]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("创建时间")]),a("span",{staticClass:"detail-value time-value"},[a("i",{staticClass:"el-icon-date"}),t._v(" "+t._s(t._f("formatTimestamp")(t.currentOrder.create_time))+" ")])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("回调时间")]),a("span",{staticClass:"detail-value time-value"},[a("i",{staticClass:"el-icon-date"}),t._v(" "+t._s(t._f("formatTimestamp")(t.currentOrder.callback_time))+" ")])])])]),a("el-card",{staticClass:"detail-card owner-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-user"}),t._v(" 归属信息 ")]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("下单商户")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentOrder.merchant_name))])]),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("核销")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentOrder.user_nickname||"暂无"))])])])])],1),a("el-card",{staticClass:"remark-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"el-icon-edit-outline"}),t._v(" 备注信息 ")]),a("div",{staticClass:"remark-content"},[t.currentOrder.remark?a("span",[t._v(t._s(t.currentOrder.remark))]):a("span",{staticClass:"no-remark"},[t._v("暂无备注信息")])])])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.detailDialogVisible=!1}}},[t._v("关 闭")]),a("el-button",{attrs:{type:"warning",disabled:"1"===t.currentOrder.callback_status||1===t.currentOrder.callback_status},on:{click:function(e){return t.handleCallback(t.currentOrder)}}},[a("i",{staticClass:"el-icon-refresh"}),t._v(" 回调 ")])],1)])],1)},i=[],r=a("53ca"),l=(a("99af"),a("caad"),a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("2532"),a("4d90"),a("a573"),a("b775"));function n(t){return Object(l["a"])({url:"/order/list",method:"get",params:t})}function c(t){return Object(l["a"])({url:"/order/detail",method:"post",data:{id:t}})}function o(t){return Object(l["a"])({url:"/order/update-remark",method:"post",data:t})}function u(t){return Object(l["a"])({url:"/order/callback",method:"post",data:{id:t}})}var d={name:"OrderManagement",filters:{payStatusFilter:function(t){var e={0:"info",1:"success"};return e[t]},payStatusText:function(t){var e={0:"未支付",1:"已支付"};return e[t]},callbackStatusFilter:function(t){var e="string"===typeof t?parseInt(t):t,a={0:"info",1:"success"};return a[e]},callbackStatusText:function(t){var e="string"===typeof t?parseInt(t):t,a={0:"未回调",1:"已回调"};return a[e]},formatTimestamp:function(t){if(!t||""===t)return"暂无";var e="string"===typeof t?parseInt(t):t;if(isNaN(e))return"无效时间";var a=10===e.toString().length?new Date(1e3*e):new Date(e);if(isNaN(a.getTime()))return"无效时间";var s=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),r=String(a.getDate()).padStart(2,"0"),l=String(a.getHours()).padStart(2,"0"),n=String(a.getMinutes()).padStart(2,"0"),c=String(a.getSeconds()).padStart(2,"0");return"".concat(s,"-").concat(i,"-").concat(r," ").concat(l,":").concat(n,":").concat(c)}},data:function(){return{list:[],total:0,listLoading:!0,searchLoading:!1,refreshLoading:!1,searchTimer:null,listQuery:{page:1,limit:10,out_trade_no:"",pay_status:"",callback_status:"",start_time:"",end_time:""},dateRange:[],pickerOptions:{shortcuts:[{text:"最近一小时",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-36e5),t.$emit("pick",[a,e])}},{text:"今天",onClick:function(t){var e=new Date,a=new Date;a.setHours(0,0,0,0),t.$emit("pick",[a,e])}},{text:"昨天",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-864e5),a.setHours(0,0,0,0),e.setTime(e.getTime()-864e5),e.setHours(23,59,59,999),t.$emit("pick",[a,e])}},{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}}]},payStatusOptions:[{value:"0",label:"未支付"},{value:"1",label:"已支付"}],callbackStatusOptions:[{value:"0",label:"未回调"},{value:"1",label:"已回调"}],remarkDialogVisible:!1,detailDialogVisible:!1,remarkForm:{id:"",remark:""},currentOrder:{},multipleSelection:[],checkAll:!1,checkedColumns:[],columnOptions:[{prop:"out_trade_no",label:"商户单号"},{prop:"account_name",label:"收款账号"},{prop:"amount",label:"收款金额"},{prop:"paytype",label:"收款类型"},{prop:"pay_status",label:"支付状态"},{prop:"callback_status",label:"回调状态"},{prop:"callback_time",label:"回调时间"},{prop:"create_time",label:"创建时间"},{prop:"merchant_name",label:"下单商户"},{prop:"user_nickname",label:"所属用户"},{prop:"remark",label:"备注"}]}},watch:{dateRange:function(t){t?(this.listQuery.start_time=t[0],this.listQuery.end_time=t[1]):(this.listQuery.start_time="",this.listQuery.end_time="")}},created:function(){this.initColumnSettings(),this.restoreQueryState(),this.fetchData()},methods:{initColumnSettings:function(){this.checkedColumns=this.columnOptions.map((function(t){return t.prop})),this.checkAll=!0},saveQueryState:function(){var t={listQuery:this.listQuery,dateRange:this.dateRange};localStorage.setItem("orderQueryState",JSON.stringify(t))},restoreQueryState:function(){try{var t=localStorage.getItem("orderQueryState");if(t){var e=JSON.parse(t);e.listQuery&&(delete e.listQuery.merchantOrderNo,delete e.listQuery.orderNo,delete e.listQuery.payStatus,delete e.listQuery.callbackStatus,delete e.listQuery.startTime,delete e.listQuery.endTime,delete e.listQuery.pay_trade_no,e.listQuery.out_trade_no=e.listQuery.out_trade_no||"",e.listQuery.pay_status=e.listQuery.pay_status||"",e.listQuery.callback_status=e.listQuery.callback_status||"",e.listQuery.start_time=e.listQuery.start_time||"",e.listQuery.end_time=e.listQuery.end_time||""),this.listQuery=e.listQuery||this.listQuery,this.dateRange=e.dateRange||this.dateRange}}catch(a){localStorage.removeItem("orderQueryState")}},fetchData:function(){var t=this;return this.listLoading=!0,n(this.listQuery).then((function(e){return e.data&&e.data.list&&Array.isArray(e.data.list)?(t.list=e.data.list,t.total=e.data.total||0):(t.list=[],t.total=0),t.listLoading=!1,Promise.resolve()})).catch((function(e){return t.listLoading=!1,t.$message.error(e.message||"获取订单列表失败"),Promise.reject(e)}))},handleSearch:function(){var t=this;this.listQuery.page=1,this.searchLoading=!0,this.fetchData().finally((function(){t.searchLoading=!1,t.saveQueryState()}))},resetQuery:function(){var t=this;this.listQuery={page:1,limit:10,out_trade_no:"",pay_status:"",callback_status:"",start_time:"",end_time:""},this.dateRange=[],this.searchLoading=!0,this.fetchData().finally((function(){t.searchLoading=!1,localStorage.removeItem("orderQueryState")}))},handleSizeChange:function(t){this.listQuery.limit=t,this.fetchData()},handleCurrentChange:function(t){this.listQuery.page=t,this.fetchData()},handleDetail:function(t){var e=this;this.listLoading=!0,c(t.id).then((function(t){t.data&&"object"===Object(r["a"])(t.data)?(e.currentOrder=t.data,e.detailDialogVisible=!0):e.$message.error("获取订单详情失败: 响应格式错误"),e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error(t.message||"获取订单详情失败")}))},handleCallback:function(t){var e=this,a="string"===typeof t.callback_status?parseInt(t.callback_status):t.callback_status;1!==a?this.$confirm("确定要对订单 ".concat(t.out_trade_no," 进行回调操作吗?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitCallback(t)})).catch((function(){e.$message({type:"info",message:"已取消回调操作"})})):this.$message({type:"warning",message:"该订单已成功回调，无需再次操作"})},submitCallback:function(t){var e=this;this.listLoading=!0,u(t.id).then((function(t){e.$message({type:"success",message:"回调操作成功!"}),e.fetchData()})).catch((function(t){e.listLoading=!1}))},formatDateTime:function(t){var e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),i=String(t.getHours()).padStart(2,"0"),r=String(t.getMinutes()).padStart(2,"0"),l=String(t.getSeconds()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(s," ").concat(i,":").concat(r,":").concat(l)},handleRemark:function(t){this.remarkForm.id=t.id,this.remarkForm.remark=t.remark,this.remarkDialogVisible=!0},submitRemark:function(){var t=this;this.listLoading=!0,o(this.remarkForm).then((function(e){t.$message({type:"success",message:"备注修改成功!"}),t.remarkDialogVisible=!1,t.fetchData()})).catch((function(e){t.$message({type:"error",message:e.message||"备注修改失败!"}),t.listLoading=!1}))},debounceSearch:function(){var t=this;this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){t.handleSearch()}),500)},handleSelectionChange:function(t){this.multipleSelection=t},handleCheckAllChange:function(t){this.checkedColumns=t?this.columnOptions.map((function(t){return t.prop})):[]},handleCheckedColumnsChange:function(t){this.checkedColumns=t},isColumnVisible:function(t){return this.checkedColumns.includes(t)},tableRowClassName:function(t){var e=t.row,a="string"===typeof e.callback_status?parseInt(e.callback_status):e.callback_status;return 0===e.pay_status?"warning-row":1===e.pay_status&&0===a?"info-row":1===e.pay_status&&1===a?"success-row":""},handleRefresh:function(){var t=this;this.refreshLoading=!0,this.fetchData().finally((function(){t.refreshLoading=!1,t.$message({type:"success",message:"数据已刷新",duration:1500})}))},setQuickDateRange:function(t){var e=new Date,a=new Date;switch(t){case"hour":a.setTime(a.getTime()-36e5);break;case"today":a.setHours(0,0,0,0);break;case"yesterday":a.setTime(a.getTime()-864e5),a.setHours(0,0,0,0),e.setTime(e.getTime()-864e5),e.setHours(23,59,59,999);break;case"week":a.setTime(a.getTime()-6048e5);break}this.dateRange=[this.formatDateTime(a),this.formatDateTime(e)],this.handleSearch()},copyToClipboard:function(t){var e=this;if(navigator.clipboard)navigator.clipboard.writeText(t).then((function(){e.$message({message:"复制成功",type:"success",duration:1500})})).catch((function(){e.$message.error("复制失败，请手动复制")}));else{var a=document.createElement("textarea");a.value=t,document.body.appendChild(a),a.select();try{document.execCommand("copy"),this.$message({message:"复制成功",type:"success",duration:1500})}catch(s){this.$message.error("复制失败，请手动复制")}document.body.removeChild(a)}}}},m=d,p=(a("8296"),a("2877")),h=Object(p["a"])(m,s,i,!1,null,"8a281b5e",null);e["default"]=h.exports},8296:function(t,e,a){"use strict";a("8e59")},"8e59":function(t,e,a){},"9a0c":function(t,e,a){var s=a("342f");t.exports=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(s)},e9c4:function(t,e,a){var s=a("23e7"),i=a("d066"),r=a("d039"),l=i("JSON","stringify"),n=/[\uD800-\uDFFF]/g,c=/^[\uD800-\uDBFF]$/,o=/^[\uDC00-\uDFFF]$/,u=function(t,e,a){var s=a.charAt(e-1),i=a.charAt(e+1);return c.test(t)&&!o.test(i)||o.test(t)&&!c.test(s)?"\\u"+t.charCodeAt(0).toString(16):t},d=r((function(){return'"\\udf06\\ud834"'!==l("\udf06\ud834")||'"\\udead"'!==l("\udead")}));l&&s({target:"JSON",stat:!0,forced:d},{stringify:function(t,e,a){var s=l.apply(null,arguments);return"string"==typeof s?s.replace(n,u):s}})}}]);