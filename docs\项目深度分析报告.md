# 来疯支付网关系统 - 深度分析报告 (v2.0)

## 项目概述

本项目是一个基于 **ThinkPHP 5** 框架开发的企业级支付网关系统，专门用于处理来疯平台的支付业务。系统采用模块化设计，具备完整的支付、用户、订单管理功能，并提供了强大的安全保障机制。

### 最新更新特性 (v2.0)
- **异步支付监控**: 自动监控订单支付状态，实时更新
- **多渠道支持**: 支持来疯微信(lfwx)和来疯支付宝(lfali)
- **智能订单号提取**: 支持多种格式的订单号提取
- **订单自动归档**: 历史数据自动迁移，保持系统性能
- **代理池管理**: 支持多代理轮换和智能切换
- **性能优化**: 批量处理和并发优化

## 技术架构

### 框架与技术栈
- **后端框架**: ThinkPHP 5.x
- **数据库**: MySQL (主表+归档表)
- **缓存**: Redis/File Cache
- **API设计**: RESTful API
- **安全机制**: MD5签名验证、Token认证
- **异步处理**: curl_multi并发请求
- **命令行工具**: ThinkPHP Console

### 目录结构
```
application/
├── admin/          # 后台管理模块
├── api/            # API接口模块
├── common/         # 公共模块
└── index/          # 前台模块

extend/
├── laifeng/        # 来疯API封装
│   ├── Pay.php     # 支付订单创建
│   └── Service.php # 基础服务和工具方法
├── fast/           # 快速开发工具
└── Proxy.php       # 代理IP管理

public/
├── assets/         # 静态资源
└── uploads/        # 上传文件
```

## 核心模块分析

### 1. API控制器层 (`application/api/controller/`)

#### Gateway.php - 支付网关核心
**功能职责**:
- 支付订单创建
- 商户验证
- 签名校验
- 服务商分配

**关键方法**:
- `index()` - 创建支付订单
- `validateRequest()` - 请求参数验证
- `getMerchant()` - 商户信息获取
- `getUserCanUse()` - 可用服务商分配

#### Account.php - 账号管理 (已更新)
**新增功能**:
- 账号登录验证
- 统计数据展示（今日、昨日、前日）
- 数据导出功能
- 权限控制优化

**关键方法**:
- `login()` - 账号登录
- `list()` - 账号列表（含统计数据）
- `export()` - 数据导出
- `add()` - 添加账号
- `status()` - 状态管理

#### Order.php - 订单管理 (已优化)
**优化特性**:
- 主表+归档表联合查询
- 默认查询最近3小时数据
- 智能分页处理
- 数据导出功能

**关键方法**:
- `list()` - 订单列表查询
- `detail()` - 订单详情
- `callback()` - 手动回调
- `export()` - 数据导出

### 2. 来疯支付服务层 (`extend/laifeng/`)

#### Pay.php - 支付订单创建
**核心功能**:
- 支持微信和支付宝两种支付方式
- 自动提取支付链接和订单号
- 账号登录验证

**关键方法**:
```php
// 创建支付订单
createOrder($amount, $cookie, $chcode, $proxy)

// 账号登录
accountLogin($phone, $password, $proxy)
```

#### Service.php - 基础服务 (新增工具方法)
**新增工具方法**:
- `extractAlipayUrl()` - 提取支付宝支付链接
- `extractOrderNo()` - 从字符串提取订单号
- `extractOrderNoFromUrl()` - 从URL提取订单号
- `status()` - 查询支付状态

### 3. 命令行服务 (`application/common/command/`)

#### OrderPaymentCheck.php - 异步支付监控 (新增)
**核心特性**:
- 无限循环监控，每轮完成后间隔5秒
- 查询近5分钟的未支付订单
- 异步并发处理，最大100并发
- 自动更新支付状态和执行回调
- 详细的性能统计

**执行命令**:
```bash
php think order:payment-check
```

#### ArchiveOrder.php - 订单归档服务
**功能特性**:
- 自动归档30天前的订单
- 每天凌晨2点执行
- 批量处理，保证数据完整性

#### AccountPull.php - 账号拉单服务
**功能特性**:
- 定期检查账号状态
- 自动拉单测试可用性
- 更新账号错误信息
**功能职责**:
- 来疯账号管理
- 二维码登录
- Cookie配置
- 账号状态监控
- 数据导出

**关键方法**:
- `getQeid()` - 获取登录二维码
- `tryQrcode()` - 检查登录状态
- `add()` - 添加账号
- `export()` - 导出账号数据

#### Order.php - 订单管理
**功能职责**:
- 订单查询
- 支付状态检查
- 订单导出
- 历史数据管理

**关键方法**:
- `index()` - 订单列表查询
- `callback()` - 支付状态查询
- `export()` - 订单数据导出

### 2. 业务扩展层 (`extend/`)

#### lfwx/Pay.php - 来疯支付接口
**功能职责**:
- 来疯API调用封装
- 支付渠道映射
- 订单创建处理

#### lfwx/Service.php - 来疯服务接口
**功能职责**:
- 登录状态管理
- 充值接口调用
- 订单状态查询

## 详细业务流程分析

### 1. 完整支付流程（端到端）

#### 阶段一：收银台订单创建

```mermaid
sequenceDiagram
    participant User as 用户
    participant Cashier as 收银台
    participant Gateway as API网关
    participant DB as 数据库
    participant lfwx as 来疯API

    User->>Cashier: 选择商户和通道
    User->>Cashier: 输入充值金额
    Cashier->>Cashier: 验证金额范围
    Cashier->>Cashier: 生成订单号(时间戳)
    Cashier->>Cashier: 计算MD5签名
    Cashier->>Gateway: 调用内部API网关
    Gateway->>DB: 创建本地订单
    Gateway->>lfwx: 调用来疯充值接口
    lfwx-->>Gateway: 返回支付链接
    Gateway-->>Cashier: 返回支付页面URL
    Cashier-->>User: 重定向到支付页面
```

**详细步骤**：
1. **用户选择** - 在收银台选择商户和支付通道（微信/支付宝）
2. **金额验证** - 输入充值金额，系统验证金额范围
3. **订单生成** - 生成订单号（使用时间戳确保唯一性）
4. **签名计算** - 计算MD5签名（参数按字典序+商户密钥）
5. **API调用** - 调用内部API网关创建订单
6. **页面跳转** - 重定向到支付页面

#### 阶段二：来疯拉单核心流程

```mermaid
flowchart TD
    A[接收支付请求] --> B[订单验证]
    B --> C{订单已有支付链接?}
    C -->|是| D[直接返回链接]
    C -->|否| E[获取缓存锁]
    E --> F{获取锁成功?}
    F -->|否| G[返回处理中提示]
    F -->|是| H[双重检查订单状态]
    H --> I[智能账号选择]
    I --> J[代理IP分配]
    J --> K[调用来疯API]
    K --> L{API调用成功?}
    L -->|否| M[记录错误日志]
    L -->|是| N[处理特殊渠道]
    N --> O[更新订单信息]
    O --> P[释放缓存锁]
    P --> Q[返回支付链接]
    M --> R[更新账号错误状态]
    R --> P
    G --> S[结束]
    D --> S
    Q --> S
```

**关键技术点**：

1. **并发控制机制**
```php
// 缓存锁防止并发处理
if (!CacheLock::acquireOrderLock($order['out_trade_no'], 5)) {
    return $this->jsonResponse(0, '订单正在处理中，请稍后重试');
}

try {
    // 双重检查机制
    $orderModel = \app\common\model\Order::where('id', $order['id'])->find();
    if (empty($orderModel['pay_url'])) {
        $orderModel->save($orderData);
    }
} finally {
    // 确保释放锁
    CacheLock::releaseOrderLock($order['out_trade_no']);
}
```

2. **智能账号选择算法**
```php
// 轮询模式配置
$pollingConfig = Config::get('polling');
$enabled = $pollingConfig['enabled'] ?? false;

if ($enabled) {
    // 轮询模式 - 按使用时间排序，确保账号均匀使用
    $orderStr = 'pulltime ASC';
} else {
    // 非轮询模式 - ID倒序，优先使用新账号
    $orderStr = 'id DESC';
}

// 多重过滤条件
$accountList = db('account')
    ->whereNotLike('errormsg', '%异常%')  // 排除被封账号
    ->whereNotLike('errormsg', '%频繁%')  // 排除频繁操作账号
    ->whereNotLike('errormsg', '%登录%')  // 排除需重新登录账号
    ->whereNotLike('errormsg', '%违规%')  // 排除违规账号
    ->whereNotLike('errormsg', '%风险%')  // 排除风险账号
    ->where('status', 1)                  // 只选择正常状态账号
    ->order($orderStr)
    ->select();
```

3. **代理IP智能分配**
```php
// 获取客户端真实IP
$clientIp = $params['ip'];
if ($clientIp == '127.0.0.1') {
    $regionCode = '';
} else {
    // 通过IP库查询地区代码
    $regionCode = Proxy::getRegionCode($clientIp);
}

// 优先分配同地区代理
$proxy = Proxy::getProxyByShenLong(1, $regionCode);
if (!$proxy) {
    // 无同地区代理时使用通用代理
    $proxy = Proxy::getProxyByShenLong(1, "");
}
```

### 2. 支付渠道处理机制

#### 渠道映射策略
```php
public static function createOrder($amount, $cookie, $chcode, $proxy)
{
    if ($chcode == 'lfali') {
        $chcode = 'Zfb-Wap';  // 雅虎渠道 -> 支付宝
    } else {
        $chcode = 'Wx-Wap';   // 来疯渠道 -> 微信
    }
    
    $res = Service::charge($amount, $cookie, $chcode, $proxy);
}
```

#### 特殊渠道处理
```php
// 雅虎渠道需要提取抖音订单号
if ($order['paytype'] == 'lfali') {
    $outTradeNo = lfwx\Service::extractOutTradeNo($res['data']['payUrl']);
    if ($outTradeNo) {
        $orderData['dy_order_id'] = $outTradeNo;  // 存储抖音订单号
    }
}
```

**数据库字段映射**：
- `pay_trade_no` - 来疯平台订单号
- `dy_order_id` - 抖音订单号（雅虎渠道专用）
- `pay_url` - 最终支付链接

### 3. 支付状态监控与轮询

#### 前端轮询机制
```javascript
let requestCount = 0;
const MAX_RETRY = 30;  // 最大重试30次

function requestPaymentUrl() {
    if (requestCount >= MAX_RETRY) {
        showError('获取支付链接超时，请重新发起支付');
        return;
    }
    
    fetch('/index/cashier/getPayInfo', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'out_trade_no=' + encodeURIComponent(orderNumber) + '&ip=' + userIP
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1 && data.url) {
            // 获取到支付链接，自动跳转
            window.location.href = data.url;
        } else {
            // 失败重试，间隔1秒
            requestCount++;
            setTimeout(requestPaymentUrl, 1000);
        }
    })
    .catch(error => {
        requestCount++;
        setTimeout(requestPaymentUrl, 1000);
    });
}
```

#### 支付倒计时机制
```javascript
// 5分钟支付时限
let timeLeft = 300;
const countdownTimer = setInterval(() => {
    timeLeft--;
    updateCountdownDisplay(timeLeft);
    
    if (timeLeft <= 0) {
        clearInterval(countdownTimer);
        showError('支付超时，请重新发起支付');
        disablePayment();
    }
}, 1000);
```

### 4. 订单状态流转机制

#### 状态定义
```sql
-- 订单状态字段
pay_status: 0=未支付, 1=已支付
callback_status: 0=未回调, 1=回调成功, 2=回调失败
status: 1=正常, 0=异常
```

#### 状态流转图
```mermaid
stateDiagram-v2
    [*] --> 订单创建
    订单创建 --> 等待支付: pay_status=0, callback_status=0
    等待支付 --> 支付成功: pay_status=1, callback_status=0
    支付成功 --> 回调成功: pay_status=1, callback_status=1
    支付成功 --> 回调失败: pay_status=1, callback_status=2
    回调失败 --> 回调成功: 重试成功
    等待支付 --> 支付超时: 超过时限
    支付超时 --> [*]
    回调成功 --> [*]
```

### 5. 异常处理与恢复机制

#### 账号错误分类处理
```php
protected function updateAccountError($accountId, $errorMsg)
{
    // 根据错误类型设置不同的恢复策略
    $errorTypes = [
        '异常' => ['recovery_time' => 3600, 'auto_disable' => true],
        '频繁' => ['recovery_time' => 1800, 'auto_disable' => false],
        '登录' => ['recovery_time' => 0, 'need_manual' => true],
        '违规' => ['recovery_time' => 86400, 'auto_disable' => true],
        '风险' => ['recovery_time' => 7200, 'auto_disable' => false]
    ];
    
    db('account')->where('id', $accountId)->update([
        'errormsg' => $errorMsg,
        'pulltime' => time(),
        'error_time' => time(),
        'status' => $this->shouldDisableAccount($errorMsg) ? 0 : 1
    ]);
}
```

#### 订单修复机制
```php
public function fixOrderStatus()
{
    // 修复callback_status=1但pay_status=0的异常订单
    $abnormalOrders = db('order')
        ->where('callback_status', 1)
        ->where('pay_status', 0)
        ->select();
        
    foreach ($abnormalOrders as $order) {
        $this->infoLog("修复异常订单状态", $order['out_trade_no']);
    }
    
    // 批量更新为正确状态
    db('order')
        ->where('callback_status', 1)
        ->where('pay_status', 0)
        ->update([
            'pay_status' => 1, 
            'update_time' => time(),
            'fix_time' => time()
        ]);
}
```

### 6. 商户回调重试机制

#### 智能重试策略
```php
public function retryCallback()
{
    // 查找需要重试的订单（已支付但未成功回调）
    $orders = db('order')
        ->where('pay_status', 1)
        ->where('callback_status', 'neq', 1)
        ->where('callback_retry_count', '<', 5)  // 最多重试5次
        ->select();
        
    foreach ($orders as $order) {
        $merchant = db('merchant')->where('id', $order['merchant_id'])->find();
        
        // 计算重试间隔（指数退避）
        $retryInterval = pow(2, $order['callback_retry_count']) * 60; // 1分钟、2分钟、4分钟...
        
        if (time() - $order['last_callback_time'] >= $retryInterval) {
            $rs = \app\common\library\Order::merchantsCallback(
                $order['callback_url'],
                $merchant['key'],
                $order['out_trade_no'],
                $order['amount'],
                $order['paytype'],
                1,
                $order['user_id']
            );
            
            // 更新重试记录
            db('order')->where('id', $order['id'])->update([
                'callback_retry_count' => $order['callback_retry_count'] + 1,
                'last_callback_time' => time(),
                'callback_status' => $rs ? 1 : 2
            ]);
        }
    }
}
```

### 7. 性能优化策略

#### 数据库查询优化
```sql
-- 关键索引设计
CREATE INDEX idx_out_trade_no ON order(out_trade_no);
CREATE INDEX idx_pay_status_callback ON order(pay_status, callback_status);
CREATE INDEX idx_account_status ON account(status, user_id);
CREATE INDEX idx_pulltime ON account(pulltime);
```

#### 缓存策略
```php
// 商户信息缓存
$merchant = Cache::remember('merchant_' . $merchantId, 3600, function() use ($merchantId) {
    return db('merchant')->where('id', $merchantId)->find();
});

// 账号列表缓存
$accountKey = 'accounts_' . $userId . '_' . md5(serialize($filters));
$accounts = Cache::remember($accountKey, 300, function() use ($userId, $filters) {
    return $this->getAccountCanUseList($userId, $filters);
});
```

### 8. 监控与告警机制

#### 关键指标监控
```php
// 实时监控指标
$metrics = [
    'order_success_rate' => $this->calculateSuccessRate(),      // 订单成功率
    'account_available_rate' => $this->getAccountAvailability(), // 账号可用率
    'avg_response_time' => $this->getAvgResponseTime(),         // 平均响应时间
    'error_rate' => $this->getErrorRate(),                      // 错误率
    'concurrent_orders' => $this->getConcurrentOrderCount()     // 并发订单数
];

// 告警阈值检查
if ($metrics['order_success_rate'] < 0.95) {
    $this->sendAlert('订单成功率低于95%');
}
if ($metrics['account_available_rate'] < 0.8) {
    $this->sendAlert('可用账号不足80%');
}
```

## 数据模型设计

### 核心数据表

#### order - 订单表
```sql
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `out_trade_no` varchar(50) NOT NULL COMMENT '商户订单号',
  `pay_trade_no` varchar(50) DEFAULT NULL COMMENT '来疯订单号',
  `dy_order_id` varchar(50) DEFAULT NULL COMMENT '抖音订单号',
  `merchant_id` int(11) NOT NULL COMMENT '商户ID',
  `user_id` int(11) NOT NULL COMMENT '服务商ID',
  `account_id` int(11) DEFAULT NULL COMMENT '使用的账号ID',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `pay_status` tinyint(1) DEFAULT 0 COMMENT '支付状态',
  `callback_status` tinyint(1) DEFAULT 0 COMMENT '回调状态',
  `pay_url` text COMMENT '支付链接',
  `paytype` varchar(20) DEFAULT NULL COMMENT '支付类型',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_out_trade_no` (`out_trade_no`),
  KEY `idx_pay_status_callback` (`pay_status`, `callback_status`)
);
```

#### account - 账号表
```sql
CREATE TABLE `account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '账号名称',
  `cookie` text COMMENT '登录Cookie',
  `config` text COMMENT '账号配置JSON',
  `paytype` varchar(20) DEFAULT NULL COMMENT '支付类型',
  `status` tinyint(1) DEFAULT 1 COMMENT '账号状态',
  `errormsg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `pulltime` int(11) DEFAULT 0 COMMENT '最后拉单时间',
  `user_id` int(11) NOT NULL COMMENT '所属用户',
  `diamond` int(11) DEFAULT 0 COMMENT '钻石余额',
  PRIMARY KEY (`id`),
  KEY `idx_status_user` (`status`, `user_id`),
  KEY `idx_pulltime` (`pulltime`)
);
```

#### merchant - 商户表
```sql
CREATE TABLE `merchant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '商户名称',
  `key` varchar(100) NOT NULL COMMENT '商户密钥',
  `status` tinyint(1) DEFAULT 1 COMMENT '商户状态',
  `notify_url` varchar(500) DEFAULT NULL COMMENT '回调地址',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## 安全机制

### 1. 签名验证
- **算法**: MD5签名
- **参数**: 按字典序排列后拼接
- **密钥**: 商户专属密钥

### 2. 权限控制
- **角色权限**: 基于用户组的权限控制
- **接口权限**: `$noNeedLogin`和`$noNeedRight`配置
- **数据权限**: 用户只能操作自己的数据

### 3. 防护措施
- **SQL注入防护**: 使用ORM和参数绑定
- **XSS防护**: 输出过滤和转义
- **CSRF防护**: Token验证机制
- **并发控制**: 缓存锁机制

## 性能优化

### 1. 缓存策略
- **订单锁**: 防止重复处理
- **账号缓存**: 减少数据库查询
- **配置缓存**: 系统配置信息缓存

### 2. 数据库优化
- **索引优化**: 关键字段建立索引
- **分页查询**: 大数据量分页处理
- **连接池**: 数据库连接复用

### 3. 代码优化
- **延迟加载**: 按需加载数据
- **批量操作**: 减少数据库交互次数
- **异步处理**: 耗时操作异步执行

## 监控与日志

### 1. 日志系统
- **操作日志**: AdminLog记录管理员操作
- **错误日志**: 异常信息详细记录
- **业务日志**: 关键业务流程日志

### 2. 监控指标
- **订单成功率**: 支付成功/总订单数
- **账号可用率**: 可用账号/总账号数
- **响应时间**: API接口响应时间
- **错误率**: 错误请求/总请求数

## 部署与运维

### 1. 环境要求
- **PHP**: >= 7.0
- **MySQL**: >= 5.6
- **Redis**: >= 3.0
- **Nginx**: 推荐使用

### 2. 配置文件
- **数据库配置**: `config/database.php`
- **应用配置**: `config/app.php`
- **缓存配置**: `config/cache.php`

### 3. 定时任务
- **订单归档**: 每日凌晨执行
- **日志清理**: 定期清理过期日志
- **文件清理**: 清理临时文件

## 扩展性设计

### 1. 模块化架构
- **控制器分离**: 不同业务独立控制器
- **服务层封装**: 业务逻辑服务化
- **数据访问层**: 统一数据访问接口

### 2. 插件机制
- **支付渠道**: 可扩展其他支付渠道
- **通知方式**: 可扩展多种通知方式
- **数据导出**: 可扩展多种导出格式

### 3. API版本控制
- **向后兼容**: 保持API向后兼容
- **版本隔离**: 不同版本独立维护
- **平滑升级**: 支持平滑版本升级

## 项目优势

1. **架构清晰** - MVC分层明确，职责分离
2. **功能完整** - 支付、用户、订单管理齐全
3. **安全可靠** - 多重验证机制，安全防护完善
4. **易于维护** - 代码规范，注释完整
5. **扩展性强** - 模块化设计，易于扩展
6. **性能优化** - 缓存机制，数据库优化

## 改进建议

### 1. 技术改进
- **引入消息队列** - 处理高并发场景
- **微服务架构** - 拆分为独立服务
- **容器化部署** - 使用Docker部署
- **自动化测试** - 增加单元测试和集成测试

### 2. 功能增强
- **实时监控** - 增加实时监控面板
- **智能报警** - 异常情况自动报警
- **数据分析** - 增加数据分析功能
- **多租户支持** - 支持多租户模式

### 3. 运维优化
- **自动化部署** - CI/CD流水线
- **配置管理** - 统一配置管理
- **日志分析** - ELK日志分析系统
- **性能监控** - APM性能监控

## 总结

这是一个设计良好的企业级支付网关系统，具备完整的业务功能和安全保障。系统架构清晰，代码质量高，具有良好的扩展性和维护性。特别是在并发控制、错误处理、状态管理等关键技术点上都有完善的解决方案。在实际使用中，建议根据业务需求进行相应的优化和扩展。

---
**文档版本**: v2.0  
**生成时间**: 2024年12月  
**维护人员**: 开发团队

