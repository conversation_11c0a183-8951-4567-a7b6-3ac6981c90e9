(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67bfc794"],{"3c1e":function(e,t,a){"use strict";a("57b3")},"57b3":function(e,t,a){},eb43:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"config-container"},[a("el-card",{staticClass:"config-card"},[a("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("系统配置")])]),a("el-row",{staticClass:"config-section",attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("h3",[e._v("轮询模式配置")]),a("el-form",{attrs:{model:e.pollingForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"轮询模式状态"}},[a("el-switch",{attrs:{"active-text":"开启","inactive-text":"关闭",loading:e.pollingLoading},on:{change:e.handlePollingChange},model:{value:e.pollingForm.enabled,callback:function(t){e.$set(e.pollingForm,"enabled",t)},expression:"pollingForm.enabled"}}),a("div",{staticClass:"form-tip"},[a("i",{staticClass:"el-icon-info"}),e._v(" 开启轮询模式后，系统将自动轮询订单状态 ")])],1)],1)],1)],1),a("el-row",{staticClass:"config-section",attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("h3",[e._v("数据清理配置")]),a("el-form",{attrs:{model:e.deleteForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"删除天数"}},[a("el-input-number",{staticStyle:{width:"200px"},attrs:{min:1,max:365,placeholder:"请输入要删除多少天前的订单数据"},model:{value:e.deleteForm.days,callback:function(t){e.$set(e.deleteForm,"days",t)},expression:"deleteForm.days"}}),a("span",{staticClass:"unit-text"},[e._v("天前")]),a("div",{staticClass:"form-tip"},[a("i",{staticClass:"el-icon-warning"}),e._v(" 此操作将永久删除指定天数前的订单数据，请谨慎操作 ")])],1),a("el-form-item",[a("el-button",{attrs:{type:"danger",loading:e.deleteLoading,disabled:!e.deleteForm.days||e.deleteForm.days<1},on:{click:e.handleDeleteOrders}},[e._v(" 删除订单数据 ")])],1)],1)],1)],1)],1)],1)},r=[],l=a("c7eb"),s=a("1da1"),o=(a("99af"),a("b775"));function c(){return Object(o["a"])({url:"/common/getPollingStatus",method:"get"})}function i(e){return Object(o["a"])({url:"/common/setPollingMode",method:"post",data:e})}function d(e){return Object(o["a"])({url:"/common/deleteOldOrders",method:"post",data:e})}var u={name:"Config",data:function(){return{pollingForm:{enabled:!1},pollingLoading:!1,deleteForm:{days:30},deleteLoading:!1}},created:function(){this.getPollingStatus()},methods:{getPollingStatus:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var a;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.pollingLoading=!0,t.next=4,c();case 4:a=t.sent,e.pollingForm.enabled=a.data.enabled,e.$message.success("获取轮询模式状态成功"),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),console.error("获取轮询模式状态失败:",t.t0),e.$message.error("获取轮询模式状态失败");case 13:return t.prev=13,e.pollingLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[0,9,13,16]])})))()},handlePollingChange:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function a(){var n;return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,t.pollingLoading=!0,a.next=4,i({enabled:e});case 4:n=a.sent,t.pollingForm.enabled=n.data.enabled,t.$message.success(e?"轮询模式已开启":"轮询模式已关闭"),a.next=14;break;case 9:a.prev=9,a.t0=a["catch"](0),console.error("设置轮询模式失败:",a.t0),t.$message.error("设置轮询模式失败"),t.pollingForm.enabled=!e;case 14:return a.prev=14,t.pollingLoading=!1,a.finish(14);case 17:case"end":return a.stop()}}),a,null,[[0,9,14,17]])})))()},handleDeleteOrders:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var a;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.deleteForm.days&&!(e.deleteForm.days<1)){t.next=3;break}return e.$message.warning("请输入有效的删除天数"),t.abrupt("return");case 3:return t.prev=3,t.next=6,e.$confirm("确定要删除".concat(e.deleteForm.days,"天前的订单数据吗？此操作不可恢复！"),"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});case 6:return e.deleteLoading=!0,t.next=9,d({days:e.deleteForm.days,confirm:!0});case 9:a=t.sent,e.$message.success("成功删除".concat(a.data.deleted_count,"条").concat(e.deleteForm.days,"天前的订单数据")),t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](3),"cancel"!==t.t0&&(console.error("删除订单数据失败:",t.t0),e.$message.error("删除订单数据失败"));case 16:return t.prev=16,e.deleteLoading=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,null,[[3,13,16,19]])})))()}}},m=u,g=(a("3c1e"),a("2877")),p=Object(g["a"])(m,n,r,!1,null,"5622091a",null);t["default"]=p.exports}}]);