<?php

namespace app\api\validata;

use think\Db;
use think\Validate;


class OrderCreate extends Validate
{
    protected $rule = [
        'out_trade_no'  => 'require|unique:order,out_trade_no',
        'mid'  => ['require', 'checkMerchants'],
        'amount'   => ['float', 'between:20,500', 'checkAmount'],
        'channel_code' => ['require'],
        'notify_url' => 'require',
        'sign' => ['require', 'checkSign']
    ];

    protected $msg = [
        'out_trade_no.require' => '订单号不能为空',
        'mid.require' => '商户ID不能为空',
        'amount.require' => '金额必须',
        'channel_code.require' => '通道编码不能为空',
        'notify_url.require' => '回调地址不能为空',
        'sign.require' => '签名不能为空',
        'amount.float'   => '金额必须是数字',
        'amount.between'  => '金额只能在1-2000之间'
    ];

    public function checkAmount($value, $rule, $data, $field)
    {
        //        $merchants = DB::name("merchants")->where('code', $data['merchants_code'])->find();
        //        $tenant = DB::name("admin")->where('id', $merchants['admin_id'])->find();
        //        if ($data['channel_code']==8001) {
        //            if ($value>$tenant['aweme_gold']) {
        //                return '租户额度不足';
        //            }
        //        }
        return true;
    }

    public function checkMerchants($value)
    {
        $merchants = DB::name("merchant")->where('id', $value)->find();
        if (!$merchants) {
            return '商户不存在';
        }
        if ($merchants['status'] == 'hidden') {
            return '商户未启用';
        }
        //        $tenant = DB::name("admin")->where('id', $merchants['admin_id'])->find();
        //        if (!$tenant) {
        //            return '租户不存在';
        //        }
        //        if ($tenant['status']=='hidden') {
        //            return '租户未启用';
        //        }
        return true; // 返回true表示验证通过，false则表示验证失败
    }


    public function checkSign($value, $rule, $data, $field)
    {
        $merchant = DB::name("merchant")->where('id', $data['mid'])->find();
        $sign = md5(urldecode(\app\common\library\Order::ascii($data)) . "&key=" . $merchant['key']);
        if ($sign != $value) {
            return '签名错误';
        }
        return true;
    }
}
