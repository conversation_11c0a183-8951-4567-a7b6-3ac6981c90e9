<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use fast\Http;
use Proxy;
use think\Db;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        return $this->view->fetch();
    }

    protected function searchByKey($array, $value)
    {
        foreach ($array as $k => $v) {
            if ($array["hyname"] == $value) {
                return $array['cookie'];
            }
        }
        return false;
    }

    public function getorder()
    {
        $orderList = db('order')
            ->alias('o')
            ->join('account a', 'o.account_id = a.id')
            ->field('o.id, o.pay_trade_no, a.config')
            ->where('o.pay_status', 0)
            ->where('o.pay_url', 'not null')
            ->where('o.pay_trade_no', 'not null')
            ->whereTime('o.create_time', '-5 minutes')
            ->select();

        // 处理config中的cookie
        foreach ($orderList as &$order) {
            $config = json_decode($order['config'], true);
            $order['cookie'] = isset($config['cookie']) ? $config['cookie'] : '';
            unset($order['config']); // 移除原始config字段
        }

        return json($orderList);
    }

    public function orderispay($id)
    {
        $order = db('order')->where('id', $id)->find();
        db('order')->where('id', $id)->update(['pay_status' => 1]);
        if ($order['callback_status'] !== 1) {
            $merchants = db('merchant')->where('id', $order['merchant_id'])->find();
            $rs = \app\common\library\Order::merchantsCallback($order['callback_url'], $merchants['key'], $order['out_trade_no'], $order['amount'], $order['paytype'], 1, $order['user_id']);
            if ($rs) {
                db('order')->where('id', $id)->update(['callback_status' => 1, 'callback_time' => time()]);
            }
        }

        return "ok";
    }

    /**
     * 重试未成功回调的订单
     * @return string
     */
    public function retryCallback()
    {
        // 查找所有已支付但未成功回调的订单
        $orders = db('order')
            ->where('pay_status', 1)
            ->where('callback_status', 'neq', 1)
            ->select();

        $successCount = 0;
        $failCount = 0;

        foreach ($orders as $order) {
            $merchants = db('merchants')->where('id', $order['merchant_id'])->find();
            if (!$merchants) {
                $failCount++;
                continue;
            }

            $rs = \app\common\library\Order::merchantsCallback(
                $order['callback_url'],
                $merchants['key'],
                $order['out_trade_no'],
                $order['amount'],
                $order['paytype'],
                1,
                $order['user_id']
            );

            if ($rs) {
                db('order')->where('id', $order['id'])->update([
                    'callback_status' => 1,
                    'callback_time' => time()
                ]);
                $successCount++;
            } else {
                $failCount++;
            }
        }

        return json([
            'code' => 1,
            'msg' => '处理完成',
            'data' => [
                'total' => count($orders),
                'success' => $successCount,
                'fail' => $failCount
            ]
        ]);
    }

    /**
     * 修复订单状态异常
     * 查询callback_status=1但pay_status=0的订单，并修正pay_status=1
     * @return string
     */
    public function fixOrderStatus()
    {
        // 开启事务
        Db::startTrans();
        try {
            // 查询异常订单
            $abnormalOrders = db('order')
                ->where('callback_status', 1)
                ->where('pay_status', 0)
                ->field('id, out_trade_no, amount, paytype')
                ->select();

            if (empty($abnormalOrders)) {
                Db::rollback();
                return "没有发现异常订单";
            }

            // 批量更新订单状态
            $updateResult = db('order')
                ->where('callback_status', 1)
                ->where('pay_status', 0)
                ->update([
                    'pay_status' => 1,
                    'update_time' => time()
                ]);

            if ($updateResult === false) {
                Db::rollback();
                return "更新订单状态失败";
            }

            Db::commit();
            return "成功修复 " . count($abnormalOrders) . " 个异常订单";
        } catch (\Exception $e) {
            Db::rollback();
            return "系统异常：" . $e->getMessage();
        }
    }
}
