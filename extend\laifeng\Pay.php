<?php

namespace laifeng;

class Pay
{
    /**
     * 创建订单
     * @param int $amount 充值金额
     * @param string $cookie 请求cookie
     * @param string $proxy 代理
     * @return array|false 返回请求结果或false
     */
    public static function createOrder($amount, $cookie, $chcode, $proxy="")
    {
        $data = [];
        if ($chcode == 'lfali') {
            $channelId = '14';
        } elseif ($chcode== 'lfwx'){
            $channelId = '16';
        } else {
            return ['success' => false, 'msg' => '不支持的支付方式'];
        }
        $res = Service::charge($amount, $cookie, $proxy);
        if (!$res) {
            return ['success' => false, 'msg' => '代理连接失败或超时1'];
        }
        if ($res['ret'][0] != "SUCCESS::调用成功") {
            return ['success' => false, 'msg' => $res['ret'][0]];
        }
        $data['chargeNo'] = $res['data']['thirdOrderId']; // 来疯单号
        
        $res = Service::pay($data['chargeNo'], $channelId, $cookie, $proxy);
        if (!$res) {
            return ['success' => false, 'msg' => '代理连接失败或超时2'];
        }
        if ($res['ret'][0] != "SUCCESS::调用成功") {
            return ['success' => false, 'msg' => $res['ret'][0]];
        }
        if ($channelId == '14') {
            $alipayUrl = Service::extractAlipayUrl($res);
            if (!$alipayUrl) {
                return ['success' => false, 'msg' => '提取支付宝支付链接失败'];
            }
            $data['payUrl'] = $alipayUrl; // 支付宝支付链接
            $data['payTradeNo'] = Service::extractOrderNo($data['chargeNo']); // 支付宝支付订单号
        }
        if ($channelId == '16') {
            $data['payUrl'] = $res['data']['channelParams'];
            $data['payTradeNo'] = Service::extractOrderNoFromUrl($data['payUrl']); // 微信支付订单号
        }
        return ['success' => true, 'data' => $data];
    }

    public static function accountLogin($phone, $password, $proxy="")
    {
        $res = Service::login($phone, $password, $proxy);
        if (!$res) {
            return ['success' => false, 'msg' => '代理连接失败或超时'];
        }
        if ($res['ret'][0] != "SUCCESS::调用成功") {
            return ['success' => false, 'msg' => $res['ret'][0]];
        }
        // $res['data']：
        // {
        //     "expireTime": "*************",
        //     "loginType": "ACCOUNT_LOGIN",
        //     "refreshTime": "*************",
        //     "userId": ***********,
        //     "token": "rw+SD6ZClFDu1gPoVmF8mewQq3EDBGnxZcR+SJWRMoRLDuDXNdrtGy0mZzuC1/oXq8RkF6HoeS8i/vI4NzgEclcsbF1BVmmbMYeju7LToKf+Y5PT7ZQkDuYsE35G/rBRx7UZa8ezaHqkD5CBKmisdQAQiwtZV7AGoJjh/cigYr8=",
        //     "ecode": "p0_***********_1752947940192_1"
        // }
        // $cookie = "L_pck_rm=".urlencode($res['data']['token']);
        return ['success' => true, 'data' => $res['data']];
    }
}