if(!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";function n(t,e,n){var i,o,a=(n=n||ut).createElement("script");if(a.text=t,e)for(i in pt)(o=e[i]||e.getAttribute&&e.getAttribute(i))&&a.setAttribute(i,o);n.head.appendChild(a).parentNode.removeChild(a)}function i(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?it[ot.call(t)]||"object":typeof t}function o(t){var e=!!t&&"length"in t&&t.length,n=i(t);return!ct(t)&&!dt(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}function a(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}function r(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}function s(t,e,n){return ct(e)?mt.grep(t,function(t,i){return!!e.call(t,i,t)!==n}):e.nodeType?mt.grep(t,function(t){return t===e!==n}):"string"!=typeof e?mt.grep(t,function(t){return-1<nt.call(e,t)!==n}):mt.filter(e,t,n)}function l(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}function c(t){return t}function d(t){throw t}function u(t,e,n,i){var o;try{t&&ct(o=t.promise)?o.call(t).done(e).fail(n):t&&ct(o=t.then)?o.call(t,e,n):e.apply(void 0,[t].slice(i))}catch(t){n.apply(void 0,[t])}}function p(){ut.removeEventListener("DOMContentLoaded",p),t.removeEventListener("load",p),mt.ready()}function h(t,e){return e.toUpperCase()}function f(t){return t.replace(Mt,"ms-").replace(Pt,h)}function m(){this.expando=mt.expando+m.uid++}function g(t,e,n){var i,o;if(void 0===n&&1===t.nodeType)if(i="data-"+e.replace(Yt,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(i))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:zt.test(o)?JSON.parse(o):o)}catch(t){}Ht.set(t,e,n)}else n=void 0;return n}function v(t,e,n,i){var o,a,r=20,s=i?function(){return i.cur()}:function(){return mt.css(t,e,"")},l=s(),c=n&&n[3]||(mt.cssNumber[e]?"":"px"),d=t.nodeType&&(mt.cssNumber[e]||"px"!==c&&+l)&&Ut.exec(mt.css(t,e));if(d&&d[3]!==c){for(l/=2,c=c||d[3],d=+l||1;r--;)mt.style(t,e,d+c),(1-a)*(1-(a=s()/l||.5))<=0&&(r=0),d/=a;d*=2,mt.style(t,e,d+c),n=n||[]}return n&&(d=+d||+l||0,o=n[1]?d+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=d,i.end=o)),o}function y(t,e){for(var n,i,o,a,r,s,l,c=[],d=0,u=t.length;d<u;d++)(i=t[d]).style&&(n=i.style.display,e?("none"===n&&(c[d]=jt.get(i,"display")||null,c[d]||(i.style.display="")),""===i.style.display&&Xt(i)&&(c[d]=(l=r=a=void 0,r=(o=i).ownerDocument,s=o.nodeName,(l=Qt[s])||(a=r.body.appendChild(r.createElement(s)),l=mt.css(a,"display"),a.parentNode.removeChild(a),"none"===l&&(l="block"),Qt[s]=l)))):"none"!==n&&(c[d]="none",jt.set(i,"display",n)));for(d=0;d<u;d++)null!=c[d]&&(t[d].style.display=c[d]);return t}function b(t,e){var n;return n="undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e||"*"):"undefined"!=typeof t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&a(t,e)?mt.merge([t],n):n}function x(t,e){for(var n=0,i=t.length;n<i;n++)jt.set(t[n],"globalEval",!e||jt.get(e[n],"globalEval"))}function w(t,e,n,o,a){for(var r,s,l,c,d,u,p=e.createDocumentFragment(),h=[],f=0,m=t.length;f<m;f++)if((r=t[f])||0===r)if("object"===i(r))mt.merge(h,r.nodeType?[r]:r);else if(ie.test(r)){for(s=s||p.appendChild(e.createElement("div")),l=(te.exec(r)||["",""])[1].toLowerCase(),c=ne[l]||ne._default,s.innerHTML=c[1]+mt.htmlPrefilter(r)+c[2],u=c[0];u--;)s=s.lastChild;mt.merge(h,s.childNodes),(s=p.firstChild).textContent=""}else h.push(e.createTextNode(r));for(p.textContent="",f=0;r=h[f++];)if(o&&-1<mt.inArray(r,o))a&&a.push(r);else if(d=Vt(r),s=b(p.appendChild(r),"script"),d&&x(s),n)for(u=0;r=s[u++];)ee.test(r.type||"")&&n.push(r);return p}function _(){return!0}function k(){return!1}function C(t,e,n,i,o,a){var r,s;if("object"==typeof e){for(s in"string"!=typeof n&&(i=i||n,n=void 0),e)C(t,s,n,i,e[s],a);return t}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=k;else if(!o)return t;return 1===a&&(r=o,(o=function(t){return mt().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=mt.guid++)),t.each(function(){mt.event.add(this,e,o,i,n)})}function S(t,e,n){n?(jt.set(t,e,!1),mt.event.add(t,e,{namespace:!1,handler:function(t){var n,i=jt.get(this,e);if(1&t.isTrigger&&this[e]){if(i)(mt.event.special[e]||{}).delegateType&&t.stopPropagation();else if(i=J.call(arguments),jt.set(this,e,i),this[e](),n=jt.get(this,e),jt.set(this,e,!1),i!==n)return t.stopImmediatePropagation(),t.preventDefault(),n}else i&&(jt.set(this,e,mt.event.trigger(i[0],i.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=_)}})):void 0===jt.get(t,e)&&mt.event.add(t,e,_)}function T(t,e){return a(t,"table")&&a(11!==e.nodeType?e:e.firstChild,"tr")&&mt(t).children("tbody")[0]||t}function $(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function D(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function E(t,e){var n,i,o,a,r,s;if(1===e.nodeType){if(jt.hasData(t)&&(s=jt.get(t).events))for(o in jt.remove(e,"handle events"),s)for(n=0,i=s[o].length;n<i;n++)mt.event.add(e,o,s[o][n]);Ht.hasData(t)&&(a=Ht.access(t),r=mt.extend({},a),Ht.set(e,r))}}function F(t,e,i,o){e=tt(e);var a,r,s,l,c,d,u=0,p=t.length,h=p-1,f=e[0],m=ct(f);if(m||1<p&&"string"==typeof f&&!lt.checkClone&&re.test(f))return t.each(function(n){var a=t.eq(n);m&&(e[0]=f.call(this,n,a.html())),F(a,e,i,o)});if(p&&(r=(a=w(e,t[0].ownerDocument,!1,t,o)).firstChild,1===a.childNodes.length&&(a=r),r||o)){for(l=(s=mt.map(b(a,"script"),$)).length;u<p;u++)c=a,u!==h&&(c=mt.clone(c,!0,!0),l&&mt.merge(s,b(c,"script"))),i.call(t[u],c,u);if(l)for(d=s[s.length-1].ownerDocument,mt.map(s,D),u=0;u<l;u++)c=s[u],ee.test(c.type||"")&&!jt.access(c,"globalEval")&&mt.contains(d,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?mt._evalUrl&&!c.noModule&&mt._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},d):n(c.textContent.replace(se,""),c,d))}return t}function A(t,e,n){for(var i,o=e?mt.filter(e,t):t,a=0;null!=(i=o[a]);a++)n||1!==i.nodeType||mt.cleanData(b(i)),i.parentNode&&(n&&Vt(i)&&x(b(i,"script")),i.parentNode.removeChild(i));return t}function O(t,e,n){var i,o,a,r,s=ce.test(e),l=t.style;return(n=n||de(t))&&(r=n.getPropertyValue(e)||n[e],s&&r&&(r=r.replace(xt,"$1")||void 0),""!==r||Vt(t)||(r=mt.style(t,e)),!lt.pixelBoxStyles()&&le.test(r)&&pe.test(e)&&(i=l.width,o=l.minWidth,a=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=n.width,l.width=i,l.minWidth=o,l.maxWidth=a)),void 0!==r?r+"":r}function N(t,e){return{get:function(){return t()?void delete this.get:(this.get=e).apply(this,arguments)}}}function L(t){var e=mt.cssProps[t]||me[t];return e||(t in fe?t:me[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=he.length;n--;)if((t=he[n]+e)in fe)return t}(t)||t)}function R(t,e,n){var i=Ut.exec(e);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):e}function M(t,e,n,i,o,a){var r="width"===e?1:0,s=0,l=0,c=0;if(n===(i?"border":"content"))return 0;for(;r<4;r+=2)"margin"===n&&(c+=mt.css(t,n+qt[r],!0,o)),i?("content"===n&&(l-=mt.css(t,"padding"+qt[r],!0,o)),"margin"!==n&&(l-=mt.css(t,"border"+qt[r]+"Width",!0,o))):(l+=mt.css(t,"padding"+qt[r],!0,o),"padding"!==n?l+=mt.css(t,"border"+qt[r]+"Width",!0,o):s+=mt.css(t,"border"+qt[r]+"Width",!0,o));return!i&&0<=a&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-a-l-s-.5))||0),l+c}function P(t,e,n){var i=de(t),o=(!lt.boxSizingReliable()||n)&&"border-box"===mt.css(t,"boxSizing",!1,i),r=o,s=O(t,e,i),l="offset"+e[0].toUpperCase()+e.slice(1);if(le.test(s)){if(!n)return s;s="auto"}return(!lt.boxSizingReliable()&&o||!lt.reliableTrDimensions()&&a(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===mt.css(t,"display",!1,i))&&t.getClientRects().length&&(o="border-box"===mt.css(t,"boxSizing",!1,i),(r=l in t)&&(s=t[l])),(s=parseFloat(s)||0)+M(t,e,n||(o?"border":"content"),r,i,s)+"px"}function I(t,e,n,i,o){return new I.prototype.init(t,e,n,i,o)}function j(){xe&&(!1===ut.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(j):t.setTimeout(j,mt.fx.interval),mt.fx.tick())}function H(){return t.setTimeout(function(){be=void 0}),be=Date.now()}function z(t,e){var n,i=0,o={height:t};for(e=e?1:0;i<4;i+=2-e)o["margin"+(n=qt[i])]=o["padding"+n]=t;return e&&(o.opacity=o.width=t),o}function Y(t,e,n){for(var i,o=(B.tweeners[e]||[]).concat(B.tweeners["*"]),a=0,r=o.length;a<r;a++)if(i=o[a].call(n,e,t))return i}function B(t,e,n){var i,o,a=0,r=B.prefilters.length,s=mt.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=be||H(),n=Math.max(0,c.startTime+c.duration-e),i=1-(n/c.duration||0),a=0,r=c.tweens.length;a<r;a++)c.tweens[a].run(i);return s.notifyWith(t,[c,i,n]),i<1&&r?n:(r||s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c]),!1)},c=s.promise({elem:t,props:mt.extend({},e),opts:mt.extend(!0,{specialEasing:{},easing:mt.easing._default},n),originalProperties:e,originalOptions:n,startTime:be||H(),duration:n.duration,tweens:[],createTween:function(e,n){var i=mt.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(o)return this;for(o=!0;n<i;n++)c.tweens[n].run(1);return e?(s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c,e])):s.rejectWith(t,[c,e]),this}}),d=c.props;for((!function(t,e){var n,i,o,a,r;for(n in t)if(o=e[i=f(n)],a=t[n],Array.isArray(a)&&(o=a[1],a=t[n]=a[0]),n!==i&&(t[i]=a,delete t[n]),(r=mt.cssHooks[i])&&"expand"in r)for(n in a=r.expand(a),delete t[i],a)n in t||(t[n]=a[n],e[n]=o);else e[i]=o}(d,c.opts.specialEasing));a<r;a++)if(i=B.prefilters[a].call(c,t,d,c.opts))return ct(i.stop)&&(mt._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return mt.map(d,Y,c),ct(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),mt.fx.timer(mt.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}function U(t){return(t.match(Ot)||[]).join(" ")}function q(t){return t.getAttribute&&t.getAttribute("class")||""}function W(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(Ot)||[]}function V(t,e,n,o){var a;if(Array.isArray(e))mt.each(e,function(e,i){n||Re.test(t)?o(t,i):V(t+"["+("object"==typeof i&&null!=i?e:"")+"]",i,n,o)});else if(n||"object"!==i(e))o(t,e);else for(a in e)V(t+"["+a+"]",e[a],n,o)}function G(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,o=0,a=e.toLowerCase().match(Ot)||[];if(ct(n))for(;i=a[o++];)"+"===i[0]?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function X(t,e,n,i){function o(s){var l;return a[s]=!0,mt.each(t[s]||[],function(t,s){var c=s(e,n,i);return"string"!=typeof c||r||a[c]?r?!(l=c):void 0:(e.dataTypes.unshift(c),o(c),!1)}),l}var a={},r=t===We;return o(e.dataTypes[0])||!a["*"]&&o("*")}function Q(t,e){var n,i,o=mt.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((o[n]?t:i||(i={}))[n]=e[n]);return i&&mt.extend(!0,t,i),t}var K=[],Z=Object.getPrototypeOf,J=K.slice,tt=K.flat?function(t){return K.flat.call(t)}:function(t){return K.concat.apply([],t)},et=K.push,nt=K.indexOf,it={},ot=it.toString,at=it.hasOwnProperty,rt=at.toString,st=rt.call(Object),lt={},ct=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},dt=function(t){return null!=t&&t===t.window},ut=t.document,pt={type:!0,src:!0,nonce:!0,noModule:!0},ht="3.7.1",ft=/HTML$/i,mt=function(t,e){return new mt.fn.init(t,e)};mt.fn=mt.prototype={jquery:ht,constructor:mt,length:0,toArray:function(){return J.call(this)},get:function(t){return null==t?J.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=mt.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return mt.each(this,t)},map:function(t){return this.pushStack(mt.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(J.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(mt.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(mt.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(0<=n&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:et,sort:K.sort,splice:K.splice},mt.extend=mt.fn.extend=function(){var t,e,n,i,o,a,r=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[s]||{},s++),"object"==typeof r||ct(r)||(r={}),s===l&&(r=this,s--);s<l;s++)if(null!=(t=arguments[s]))for(e in t)i=t[e],"__proto__"!==e&&r!==i&&(c&&i&&(mt.isPlainObject(i)||(o=Array.isArray(i)))?(n=r[e],a=o&&!Array.isArray(n)?[]:o||mt.isPlainObject(n)?n:{},o=!1,r[e]=mt.extend(c,a,i)):void 0!==i&&(r[e]=i));return r},mt.extend({expando:"jQuery"+(ht+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==ot.call(t)||(e=Z(t))&&("function"!=typeof(n=at.call(e,"constructor")&&e.constructor)||rt.call(n)!==st))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,i){n(t,{nonce:e&&e.nonce},i)},each:function(t,e){var n,i=0;if(o(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},text:function(t){var e,n="",i=0,o=t.nodeType;if(!o)for(;e=t[i++];)n+=mt.text(e);return 1===o||11===o?t.textContent:9===o?t.documentElement.textContent:3===o||4===o?t.nodeValue:n},makeArray:function(t,e){var n=e||[];return null!=t&&(o(Object(t))?mt.merge(n,"string"==typeof t?[t]:t):et.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:nt.call(e,t,n)},isXMLDoc:function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!ft.test(e||n&&n.nodeName||"HTML")},merge:function(t,e){for(var n=+e.length,i=0,o=t.length;i<n;i++)t[o++]=e[i];return t.length=o,t},grep:function(t,e,n){for(var i=[],o=0,a=t.length,r=!n;o<a;o++)!e(t[o],o)!==r&&i.push(t[o]);return i},map:function(t,e,n){var i,a,r=0,s=[];if(o(t))for(i=t.length;r<i;r++)null!=(a=e(t[r],r,n))&&s.push(a);else for(r in t)null!=(a=e(t[r],r,n))&&s.push(a);return tt(s)},guid:1,support:lt}),"function"==typeof Symbol&&(mt.fn[Symbol.iterator]=K[Symbol.iterator]),mt.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){it["[object "+e+"]"]=e.toLowerCase()});var gt=K.pop,vt=K.sort,yt=K.splice,bt="[\\x20\\t\\r\\n\\f]",xt=new RegExp("^"+bt+"+|((?:^|[^\\\\])(?:\\\\.)*)"+bt+"+$","g");mt.contains=function(t,e){var n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(t.contains?t.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))};var wt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;mt.escapeSelector=function(t){return(t+"").replace(wt,r)};var _t=ut,kt=et;!function(){function e(t,n,i,o){var a,r,s,l,c,p,m,g=n&&n.ownerDocument,v=n?n.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==v&&9!==v&&11!==v)return i;if(!o&&(u(n),n=n||$,E)){if(11!==v&&(c=it.exec(t)))if(a=c[1]){if(9===v){if(!(s=n.getElementById(a)))return i;if(s.id===a)return O.call(i,s),i}else if(g&&(s=g.getElementById(a))&&e.contains(n,s)&&s.id===a)return O.call(i,s),i}else{if(c[2])return O.apply(i,n.getElementsByTagName(t)),i;if((a=c[3])&&n.getElementsByClassName)return O.apply(i,n.getElementsByClassName(a)),i}if(!(j[t+" "]||F&&F.test(t))){if(m=t,g=n,1===v&&(G.test(t)||V.test(t))){for((g=ot.test(t)&&d(n.parentNode)||n)==n&&lt.scope||((l=n.getAttribute("id"))?l=mt.escapeSelector(l):n.setAttribute("id",l=N)),r=(p=h(t)).length;r--;)p[r]=(l?"#"+l:":scope")+" "+f(p[r]);m=p.join(",")}try{return O.apply(i,g.querySelectorAll(m)),i}catch(e){j(t,!0)}finally{l===N&&n.removeAttribute("id")}}}return w(t.replace(xt,"$1"),n,i,o)}function n(){var t=[];return function e(n,i){return t.push(n+" ")>k.cacheLength&&delete e[t.shift()],e[n+" "]=i}}function i(t){return t[N]=!0,t}function o(t){var e=$.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function r(t){return function(e){return a(e,"input")&&e.type===t}}function s(t){return function(e){return(a(e,"input")||a(e,"button"))&&e.type===t}}function l(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&dt(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function c(t){return i(function(e){return e=+e,i(function(n,i){for(var o,a=t([],n.length,e),r=a.length;r--;)n[o=a[r]]&&(n[o]=!(i[o]=n[o]))})})}function d(t){return t&&"undefined"!=typeof t.getElementsByTagName&&t}function u(t){var n,i=t?t.ownerDocument||t:_t;return i!=$&&9===i.nodeType&&i.documentElement&&(D=($=i).documentElement,E=!mt.isXMLDoc($),A=D.matches||D.webkitMatchesSelector||D.msMatchesSelector,D.msMatchesSelector&&_t!=$&&(n=$.defaultView)&&n.top!==n&&n.addEventListener("unload",ct),lt.getById=o(function(t){return D.appendChild(t).id=mt.expando,!$.getElementsByName||!$.getElementsByName(mt.expando).length}),lt.disconnectedMatch=o(function(t){return A.call(t,"*")}),lt.scope=o(function(){return $.querySelectorAll(":scope")}),lt.cssHas=o(function(){try{return $.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}}),lt.getById?(k.filter.ID=function(t){var e=t.replace(rt,st);return function(t){return t.getAttribute("id")===e}},k.find.ID=function(t,e){if("undefined"!=typeof e.getElementById&&E){var n=e.getElementById(t);return n?[n]:[]}}):(k.filter.ID=function(t){var e=t.replace(rt,st);return function(t){var n="undefined"!=typeof t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},k.find.ID=function(t,e){if("undefined"!=typeof e.getElementById&&E){var n,i,o,a=e.getElementById(t);if(a){if((n=a.getAttributeNode("id"))&&n.value===t)return[a];for(o=e.getElementsByName(t),i=0;a=o[i++];)if((n=a.getAttributeNode("id"))&&n.value===t)return[a]}return[]}}),k.find.TAG=function(t,e){return"undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},k.find.CLASS=function(t,e){if("undefined"!=typeof e.getElementsByClassName&&E)return e.getElementsByClassName(t)},F=[],o(function(t){var e;D.appendChild(t).innerHTML="<a id='"+N+"' href='' disabled='disabled'></a><select id='"+N+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||F.push("\\["+bt+"*(?:value|"+z+")"),t.querySelectorAll("[id~="+N+"-]").length||F.push("~="),t.querySelectorAll("a#"+N+"+*").length||F.push(".#.+[+~]"),t.querySelectorAll(":checked").length||F.push(":checked"),(e=$.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),D.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&F.push(":enabled",":disabled"),(e=$.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||F.push("\\["+bt+"*name"+bt+"*="+bt+"*(?:''|\"\")")}),lt.cssHas||F.push(":has"),F=F.length&&new RegExp(F.join("|")),H=function(t,n){if(t===n)return T=!0,0;var i=!t.compareDocumentPosition-!n.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(n.ownerDocument||n)?t.compareDocumentPosition(n):1)||!lt.sortDetached&&n.compareDocumentPosition(t)===i?t===$||t.ownerDocument==_t&&e.contains(_t,t)?-1:n===$||n.ownerDocument==_t&&e.contains(_t,n)?1:S?nt.call(S,t)-nt.call(S,n):0:4&i?-1:1)}),$}function p(){}function h(t,n){var i,o,a,r,s,l,c,d=P[t+" "];if(d)return n?0:d.slice(0);for(s=t,l=[],c=k.preFilter;s;){for(r in i&&!(o=W.exec(s))||(o&&(s=s.slice(o[0].length)||s),l.push(a=[])),i=!1,(o=V.exec(s))&&(i=o.shift(),a.push({value:i,type:o[0].replace(xt," ")}),s=s.slice(i.length)),k.filter)!(o=Z[r].exec(s))||c[r]&&!(o=c[r](o))||(i=o.shift(),a.push({value:i,type:r,matches:o}),s=s.slice(i.length));if(!i)break}return n?s.length:s?e.error(t):P(t,l).slice(0)}function f(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function m(t,e,n){var i=e.dir,o=e.next,r=o||i,s=n&&"parentNode"===r,l=R++;return e.first?function(e,n,o){for(;e=e[i];)if(1===e.nodeType||s)return t(e,n,o);return!1}:function(e,n,c){var d,u,p=[L,l];if(c){for(;e=e[i];)if((1===e.nodeType||s)&&t(e,n,c))return!0}else for(;e=e[i];)if(1===e.nodeType||s)if(u=e[N]||(e[N]={}),o&&a(e,o))e=e[i]||e;else{if((d=u[r])&&d[0]===L&&d[1]===l)return p[2]=d[2];if((u[r]=p)[2]=t(e,n,c))return!0}return!1}}function g(t){return 1<t.length?function(e,n,i){for(var o=t.length;o--;)if(!t[o](e,n,i))return!1;return!0}:t[0]}function v(t,e,n,i,o){for(var a,r=[],s=0,l=t.length,c=null!=e;s<l;s++)(a=t[s])&&(n&&!n(a,i,o)||(r.push(a),c&&e.push(s)));return r}function y(t,n,o,a,r,s){return a&&!a[N]&&(a=y(a)),r&&!r[N]&&(r=y(r,s)),i(function(i,s,l,c){var d,u,p,h,f=[],m=[],g=s.length,y=i||function(t,n,i){for(var o=0,a=n.length;o<a;o++)e(t,n[o],i);return i}(n||"*",l.nodeType?[l]:l,[]),b=!t||!i&&n?y:v(y,f,t,l,c);if(o?o(b,h=r||(i?t:g||a)?[]:s,l,c):h=b,a)for(d=v(h,m),a(d,[],l,c),u=d.length;u--;)(p=d[u])&&(h[m[u]]=!(b[m[u]]=p));if(i){if(r||t){if(r){for(d=[],u=h.length;u--;)(p=h[u])&&d.push(b[u]=p);r(null,h=[],d,c)}for(u=h.length;u--;)(p=h[u])&&-1<(d=r?nt.call(i,p):f[u])&&(i[d]=!(s[d]=p))}}else h=v(h===s?h.splice(g,h.length):h),r?r(null,s,h,c):O.apply(s,h)})}function b(t){for(var e,n,i,o=t.length,a=k.relative[t[0].type],r=a||k.relative[" "],s=a?1:0,l=m(function(t){return t===e},r,!0),c=m(function(t){return-1<nt.call(e,t)},r,!0),d=[function(t,n,i){var o=!a&&(i||n!=C)||((e=n).nodeType?l(t,n,i):c(t,n,i));return e=null,o}];s<o;s++)if(n=k.relative[t[s].type])d=[m(g(d),n)];else{if((n=k.filter[t[s].type].apply(null,t[s].matches))[N]){for(i=++s;i<o&&!k.relative[t[i].type];i++);return y(1<s&&g(d),1<s&&f(t.slice(0,s-1).concat({value:" "===t[s-2].type?"*":""})).replace(xt,"$1"),n,s<i&&b(t.slice(s,i)),i<o&&b(t=t.slice(i)),i<o&&f(t))}d.push(n)}return g(d)}function x(t,e){var n,o,a,r,s,l,c=[],d=[],p=I[t+" "];if(!p){for(e||(e=h(t)),n=e.length;n--;)(p=b(e[n]))[N]?c.push(p):d.push(p);(p=I(t,(o=d,r=0<(a=c).length,s=0<o.length,l=function(t,e,n,i,l){var c,d,p,h=0,f="0",m=t&&[],g=[],y=C,b=t||s&&k.find.TAG("*",l),x=L+=null==y?1:Math.random()||.1,w=b.length;for(l&&(C=e==$||e||l);f!==w&&null!=(c=b[f]);f++){if(s&&c){for(d=0,e||c.ownerDocument==$||(u(c),n=!E);p=o[d++];)if(p(c,e||$,n)){O.call(i,c);break}l&&(L=x)}r&&((c=!p&&c)&&h--,t&&m.push(c))}if(h+=f,r&&f!==h){for(d=0;p=a[d++];)p(m,g,e,n);if(t){if(0<h)for(;f--;)m[f]||g[f]||(g[f]=gt.call(i));g=v(g)}O.apply(i,g),l&&!t&&0<g.length&&1<h+a.length&&mt.uniqueSort(i)}return l&&(L=x,C=y),m},r?i(l):l))).selector=t}return p}function w(t,e,n,i){var o,a,r,s,l,c="function"==typeof t&&t,u=!i&&h(t=c.selector||t);if(n=n||[],1===u.length){if(2<(a=u[0]=u[0].slice(0)).length&&"ID"===(r=a[0]).type&&9===e.nodeType&&E&&k.relative[a[1].type]){if(!(e=(k.find.ID(r.matches[0].replace(rt,st),e)||[])[0]))return n;c&&(e=e.parentNode),t=t.slice(a.shift().value.length)}for(o=Z.needsContext.test(t)?0:a.length;o--&&(r=a[o],!k.relative[s=r.type]);)if((l=k.find[s])&&(i=l(r.matches[0].replace(rt,st),ot.test(a[0].type)&&d(e.parentNode)||e))){if(a.splice(o,1),!(t=i.length&&f(a)))return O.apply(n,i),n;break}}return(c||x(t,u))(i,e,!E,n,!e||ot.test(t)&&d(e.parentNode)||e),n}var _,k,C,S,T,$,D,E,F,A,O=kt,N=mt.expando,L=0,R=0,M=n(),P=n(),I=n(),j=n(),H=function(t,e){return t===e&&(T=!0),0},z="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",Y="(?:\\\\[\\da-fA-F]{1,6}"+bt+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",B="\\["+bt+"*("+Y+")(?:"+bt+"*([*^$|!~]?=)"+bt+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+Y+"))|)"+bt+"*\\]",U=":("+Y+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+B+")*)|.*)\\)|)",q=new RegExp(bt+"+","g"),W=new RegExp("^"+bt+"*,"+bt+"*"),V=new RegExp("^"+bt+"*([>+~]|"+bt+")"+bt+"*"),G=new RegExp(bt+"|>"),X=new RegExp(U),Q=new RegExp("^"+Y+"$"),Z={ID:new RegExp("^#("+Y+")"),CLASS:new RegExp("^\\.("+Y+")"),TAG:new RegExp("^("+Y+"|[*])"),ATTR:new RegExp("^"+B),PSEUDO:new RegExp("^"+U),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+bt+"*(even|odd|(([+-]|)(\\d*)n|)"+bt+"*(?:([+-]|)"+bt+"*(\\d+)|))"+bt+"*\\)|)","i"),bool:new RegExp("^(?:"+z+")$","i"),needsContext:new RegExp("^"+bt+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+bt+"*((?:-\\d)?\\d*)"+bt+"*\\)|)(?=[^-]|$)","i")},tt=/^(?:input|select|textarea|button)$/i,et=/^h\d$/i,it=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ot=/[+~]/,rt=new RegExp("\\\\[\\da-fA-F]{1,6}"+bt+"?|\\\\([^\\r\\n\\f])","g"),st=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},ct=function(){u()},dt=m(function(t){return!0===t.disabled&&a(t,"fieldset")},{dir:"parentNode",next:"legend"});try{O.apply(K=J.call(_t.childNodes),_t.childNodes),K[_t.childNodes.length].nodeType}catch(t){O={apply:function(t,e){kt.apply(t,J.call(e))},call:function(t){kt.apply(t,J.call(arguments,1))}}}for(_ in e.matches=function(t,n){return e(t,null,null,n)},e.matchesSelector=function(t,n){if(u(t),E&&!j[n+" "]&&(!F||!F.test(n)))try{var i=A.call(t,n);if(i||lt.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){j(n,!0)}return 0<e(n,$,null,[t]).length},e.contains=function(t,e){return(t.ownerDocument||t)!=$&&u(t),mt.contains(t,e)},e.attr=function(t,e){(t.ownerDocument||t)!=$&&u(t);var n=k.attrHandle[e.toLowerCase()],i=n&&at.call(k.attrHandle,e.toLowerCase())?n(t,e,!E):void 0;return void 0!==i?i:t.getAttribute(e)},e.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},mt.uniqueSort=function(t){var e,n=[],i=0,o=0;if(T=!lt.sortStable,S=!lt.sortStable&&J.call(t,0),vt.call(t,H),T){for(;e=t[o++];)e===t[o]&&(i=n.push(o));for(;i--;)yt.call(t,n[i],1)}return S=null,t},mt.fn.uniqueSort=function(){return this.pushStack(mt.uniqueSort(J.apply(this)))},(k=mt.expr={cacheLength:50,createPseudo:i,match:Z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(rt,st),t[3]=(t[3]||t[4]||t[5]||"").replace(rt,st),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return Z.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&X.test(n)&&(e=h(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(rt,st).toLowerCase();return"*"===t?function(){return!0}:function(t){return a(t,e)}},CLASS:function(t){var e=M[t+" "];return e||(e=new RegExp("(^|"+bt+")"+t+"("+bt+"|$)"))&&M(t,function(t){return e.test("string"==typeof t.className&&t.className||"undefined"!=typeof t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(o){var a=e.attr(o,t);return null==a?"!="===n:!n||(a+="","="===n?a===i:"!="===n?a!==i:"^="===n?i&&0===a.indexOf(i):"*="===n?i&&-1<a.indexOf(i):"$="===n?i&&a.slice(-i.length)===i:"~="===n?-1<(" "+a.replace(q," ")+" ").indexOf(i):"|="===n&&(a===i||a.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,n,i,o){var r="nth"!==t.slice(0,3),s="last"!==t.slice(-4),l="of-type"===e;return 1===i&&0===o?function(t){return!!t.parentNode}:function(e,n,c){var d,u,p,h,f,m=r!==s?"nextSibling":"previousSibling",g=e.parentNode,v=l&&e.nodeName.toLowerCase(),y=!c&&!l,b=!1;if(g){if(r){for(;m;){for(p=e;p=p[m];)if(l?a(p,v):1===p.nodeType)return!1;f=m="only"===t&&!f&&"nextSibling"}return!0}if(f=[s?g.firstChild:g.lastChild],s&&y){for(b=(h=(d=(u=g[N]||(g[N]={}))[t]||[])[0]===L&&d[1])&&d[2],p=h&&g.childNodes[h];p=++h&&p&&p[m]||(b=h=0)||f.pop();)if(1===p.nodeType&&++b&&p===e){u[t]=[L,h,b];break}}else if(y&&(b=h=(d=(u=e[N]||(e[N]={}))[t]||[])[0]===L&&d[1]),!1===b)for(;(p=++h&&p&&p[m]||(b=h=0)||f.pop())&&((l?!a(p,v):1!==p.nodeType)||!++b||(y&&((u=p[N]||(p[N]={}))[t]=[L,b]),p!==e)););return(b-=o)===i||b%i==0&&0<=b/i}}},PSEUDO:function(t,n){var o,a=k.pseudos[t]||k.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return a[N]?a(n):1<a.length?(o=[t,t,"",n],k.setFilters.hasOwnProperty(t.toLowerCase())?i(function(t,e){for(var i,o=a(t,n),r=o.length;r--;)t[i=nt.call(t,o[r])]=!(e[i]=o[r])}):function(t){return a(t,0,o)}):a}},pseudos:{not:i(function(t){var e=[],n=[],o=x(t.replace(xt,"$1"));return o[N]?i(function(t,e,n,i){for(var a,r=o(t,null,i,[]),s=t.length;s--;)(a=r[s])&&(t[s]=!(e[s]=a))}):function(t,i,a){return e[0]=t,o(e,null,a,n),e[0]=null,!n.pop()}}),has:i(function(t){return function(n){return 0<e(t,n).length}}),contains:i(function(t){return t=t.replace(rt,st),function(e){return-1<(e.textContent||mt.text(e)).indexOf(t)}}),lang:i(function(t){return Q.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(rt,st).toLowerCase(),function(e){var n;do if(n=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-");while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===D},focus:function(t){return t===function(){try{return $.activeElement}catch(t){}}()&&$.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:l(!1),disabled:l(!0),checked:function(t){return a(t,"input")&&!!t.checked||a(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!k.pseudos.empty(t)},header:function(t){return et.test(t.nodeName)},input:function(t){return tt.test(t.nodeName)},button:function(t){return a(t,"input")&&"button"===t.type||a(t,"button")},text:function(t){var e;return a(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:c(function(){return[0]}),last:c(function(t,e){return[e-1]}),eq:c(function(t,e,n){return[n<0?n+e:n]}),even:c(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:c(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:c(function(t,e,n){var i;for(i=n<0?n+e:e<n?e:n;0<=--i;)t.push(i);return t}),gt:c(function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t})}}).pseudos.nth=k.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})k.pseudos[_]=r(_);for(_ in{submit:!0,reset:!0})k.pseudos[_]=s(_);p.prototype=k.filters=k.pseudos,k.setFilters=new p,lt.sortStable=N.split("").sort(H).join("")===N,u(),lt.sortDetached=o(function(t){return 1&t.compareDocumentPosition($.createElement("fieldset"))}),mt.find=e,mt.expr[":"]=mt.expr.pseudos,mt.unique=mt.uniqueSort,e.compile=x,e.select=w,e.setDocument=u,e.tokenize=h,e.escape=mt.escapeSelector,e.getText=mt.text,e.isXML=mt.isXMLDoc,e.selectors=mt.expr,e.support=mt.support,e.uniqueSort=mt.uniqueSort}();var Ct=function(t,e,n){for(var i=[],o=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&mt(t).is(n))break;i.push(t)}return i},St=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},Tt=mt.expr.match.needsContext,$t=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;mt.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?mt.find.matchesSelector(i,t)?[i]:[]:mt.find.matches(t,mt.grep(e,function(t){return 1===t.nodeType}))},mt.fn.extend({find:function(t){var e,n,i=this.length,o=this;if("string"!=typeof t)return this.pushStack(mt(t).filter(function(){for(e=0;e<i;e++)if(mt.contains(o[e],this))return!0}));for(n=this.pushStack([]),e=0;e<i;e++)mt.find(t,o[e],n);return 1<i?mt.uniqueSort(n):n},filter:function(t){return this.pushStack(s(this,t||[],!1))},not:function(t){return this.pushStack(s(this,t||[],!0));
},is:function(t){return!!s(this,"string"==typeof t&&Tt.test(t)?mt(t):t||[],!1).length}});var Dt,Et=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(mt.fn.init=function(t,e,n){var i,o;if(!t)return this;if(n=n||Dt,"string"==typeof t){if(!(i="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:Et.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof mt?e[0]:e,mt.merge(this,mt.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:ut,!0)),$t.test(i[1])&&mt.isPlainObject(e))for(i in e)ct(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}return(o=ut.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):ct(t)?void 0!==n.ready?n.ready(t):t(mt):mt.makeArray(t,this)}).prototype=mt.fn,Dt=mt(ut);var Ft=/^(?:parents|prev(?:Until|All))/,At={children:!0,contents:!0,next:!0,prev:!0};mt.fn.extend({has:function(t){var e=mt(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(mt.contains(this,e[t]))return!0})},closest:function(t,e){var n,i=0,o=this.length,a=[],r="string"!=typeof t&&mt(t);if(!Tt.test(t))for(;i<o;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(r?-1<r.index(n):1===n.nodeType&&mt.find.matchesSelector(n,t))){a.push(n);break}return this.pushStack(1<a.length?mt.uniqueSort(a):a)},index:function(t){return t?"string"==typeof t?nt.call(mt(t),this[0]):nt.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(mt.uniqueSort(mt.merge(this.get(),mt(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),mt.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return Ct(t,"parentNode")},parentsUntil:function(t,e,n){return Ct(t,"parentNode",n)},next:function(t){return l(t,"nextSibling")},prev:function(t){return l(t,"previousSibling")},nextAll:function(t){return Ct(t,"nextSibling")},prevAll:function(t){return Ct(t,"previousSibling")},nextUntil:function(t,e,n){return Ct(t,"nextSibling",n)},prevUntil:function(t,e,n){return Ct(t,"previousSibling",n)},siblings:function(t){return St((t.parentNode||{}).firstChild,t)},children:function(t){return St(t.firstChild)},contents:function(t){return null!=t.contentDocument&&Z(t.contentDocument)?t.contentDocument:(a(t,"template")&&(t=t.content||t),mt.merge([],t.childNodes))}},function(t,e){mt.fn[t]=function(n,i){var o=mt.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(o=mt.filter(i,o)),1<this.length&&(At[t]||mt.uniqueSort(o),Ft.test(t)&&o.reverse()),this.pushStack(o)}});var Ot=/[^\x20\t\r\n\f]+/g;mt.Callbacks=function(t){var e,n;t="string"==typeof t?(e=t,n={},mt.each(e.match(Ot)||[],function(t,e){n[e]=!0}),n):mt.extend({},t);var o,a,r,s,l=[],c=[],d=-1,u=function(){for(s=s||t.once,r=o=!0;c.length;d=-1)for(a=c.shift();++d<l.length;)!1===l[d].apply(a[0],a[1])&&t.stopOnFalse&&(d=l.length,a=!1);t.memory||(a=!1),o=!1,s&&(l=a?[]:"")},p={add:function(){return l&&(a&&!o&&(d=l.length-1,c.push(a)),function e(n){mt.each(n,function(n,o){ct(o)?t.unique&&p.has(o)||l.push(o):o&&o.length&&"string"!==i(o)&&e(o)})}(arguments),a&&!o&&u()),this},remove:function(){return mt.each(arguments,function(t,e){for(var n;-1<(n=mt.inArray(e,l,n));)l.splice(n,1),n<=d&&d--}),this},has:function(t){return t?-1<mt.inArray(t,l):0<l.length},empty:function(){return l&&(l=[]),this},disable:function(){return s=c=[],l=a="",this},disabled:function(){return!l},lock:function(){return s=c=[],a||o||(l=a=""),this},locked:function(){return!!s},fireWith:function(t,e){return s||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),o||u()),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!r}};return p},mt.extend({Deferred:function(e){var n=[["notify","progress",mt.Callbacks("memory"),mt.Callbacks("memory"),2],["resolve","done",mt.Callbacks("once memory"),mt.Callbacks("once memory"),0,"resolved"],["reject","fail",mt.Callbacks("once memory"),mt.Callbacks("once memory"),1,"rejected"]],i="pending",o={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return mt.Deferred(function(e){mt.each(n,function(n,i){var o=ct(t[i[4]])&&t[i[4]];a[i[1]](function(){var t=o&&o.apply(this,arguments);t&&ct(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[i[0]+"With"](this,o?[t]:arguments)})}),t=null}).promise()},then:function(e,i,o){function a(e,n,i,o){return function(){var s=this,l=arguments,u=function(){var t,u;if(!(e<r)){if((t=i.apply(s,l))===n.promise())throw new TypeError("Thenable self-resolution");u=t&&("object"==typeof t||"function"==typeof t)&&t.then,ct(u)?o?u.call(t,a(r,n,c,o),a(r,n,d,o)):(r++,u.call(t,a(r,n,c,o),a(r,n,d,o),a(r,n,c,n.notifyWith))):(i!==c&&(s=void 0,l=[t]),(o||n.resolveWith)(s,l))}},p=o?u:function(){try{u()}catch(t){mt.Deferred.exceptionHook&&mt.Deferred.exceptionHook(t,p.error),r<=e+1&&(i!==d&&(s=void 0,l=[t]),n.rejectWith(s,l))}};e?p():(mt.Deferred.getErrorHook?p.error=mt.Deferred.getErrorHook():mt.Deferred.getStackHook&&(p.error=mt.Deferred.getStackHook()),t.setTimeout(p))}}var r=0;return mt.Deferred(function(t){n[0][3].add(a(0,t,ct(o)?o:c,t.notifyWith)),n[1][3].add(a(0,t,ct(e)?e:c)),n[2][3].add(a(0,t,ct(i)?i:d))}).promise()},promise:function(t){return null!=t?mt.extend(t,o):o}},a={};return mt.each(n,function(t,e){var r=e[2],s=e[5];o[e[1]]=r.add,s&&r.add(function(){i=s},n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),r.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=r.fireWith}),o.promise(a),e&&e.call(a,a),a},when:function(t){var e=arguments.length,n=e,i=Array(n),o=J.call(arguments),a=mt.Deferred(),r=function(t){return function(n){i[t]=this,o[t]=1<arguments.length?J.call(arguments):n,--e||a.resolveWith(i,o)}};if(e<=1&&(u(t,a.done(r(n)).resolve,a.reject,!e),"pending"===a.state()||ct(o[n]&&o[n].then)))return a.then();for(;n--;)u(o[n],r(n),a.reject);return a.promise()}});var Nt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;mt.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&Nt.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},mt.readyException=function(e){t.setTimeout(function(){throw e})};var Lt=mt.Deferred();mt.fn.ready=function(t){return Lt.then(t).catch(function(t){mt.readyException(t)}),this},mt.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--mt.readyWait:mt.isReady)||(mt.isReady=!0)!==t&&0<--mt.readyWait||Lt.resolveWith(ut,[mt])}}),mt.ready.then=Lt.then,"complete"===ut.readyState||"loading"!==ut.readyState&&!ut.documentElement.doScroll?t.setTimeout(mt.ready):(ut.addEventListener("DOMContentLoaded",p),t.addEventListener("load",p));var Rt=function(t,e,n,o,a,r,s){var l=0,c=t.length,d=null==n;if("object"===i(n))for(l in a=!0,n)Rt(t,e,l,n[l],!0,r,s);else if(void 0!==o&&(a=!0,ct(o)||(s=!0),d&&(s?(e.call(t,o),e=null):(d=e,e=function(t,e,n){return d.call(mt(t),n)})),e))for(;l<c;l++)e(t[l],n,s?o:o.call(t[l],l,e(t[l],n)));return a?t:d?e.call(t):c?e(t[0],n):r},Mt=/^-ms-/,Pt=/-([a-z])/g,It=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};m.uid=1,m.prototype={cache:function(t){var e=t[this.expando];return e||(e={},It(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var i,o=this.cache(t);if("string"==typeof e)o[f(e)]=n;else for(i in e)o[f(i)]=e[i];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][f(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,i=t[this.expando];if(void 0!==i){if(void 0!==e){n=(e=Array.isArray(e)?e.map(f):(e=f(e))in i?[e]:e.match(Ot)||[]).length;for(;n--;)delete i[e[n]]}(void 0===e||mt.isEmptyObject(i))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!mt.isEmptyObject(e)}};var jt=new m,Ht=new m,zt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Yt=/[A-Z]/g;mt.extend({hasData:function(t){return Ht.hasData(t)||jt.hasData(t)},data:function(t,e,n){return Ht.access(t,e,n)},removeData:function(t,e){Ht.remove(t,e)},_data:function(t,e,n){return jt.access(t,e,n)},_removeData:function(t,e){jt.remove(t,e)}}),mt.fn.extend({data:function(t,e){var n,i,o,a=this[0],r=a&&a.attributes;if(void 0===t){if(this.length&&(o=Ht.get(a),1===a.nodeType&&!jt.get(a,"hasDataAttrs"))){for(n=r.length;n--;)r[n]&&0===(i=r[n].name).indexOf("data-")&&(i=f(i.slice(5)),g(a,i,o[i]));jt.set(a,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each(function(){Ht.set(this,t)}):Rt(this,function(e){var n;return a&&void 0===e?void 0!==(n=Ht.get(a,t))?n:void 0!==(n=g(a,t))?n:void 0:void this.each(function(){Ht.set(this,t,e)})},null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each(function(){Ht.remove(this,t)})}}),mt.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=jt.get(t,e),n&&(!i||Array.isArray(n)?i=jt.access(t,e,mt.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=mt.queue(t,e),i=n.length,o=n.shift(),a=mt._queueHooks(t,e);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===e&&n.unshift("inprogress"),delete a.stop,o.call(t,function(){mt.dequeue(t,e)},a)),!i&&a&&a.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return jt.get(t,n)||jt.access(t,n,{empty:mt.Callbacks("once memory").add(function(){jt.remove(t,[e+"queue",n])})})}}),mt.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?mt.queue(this[0],t):void 0===e?this:this.each(function(){var n=mt.queue(this,t,e);mt._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&mt.dequeue(this,t)})},dequeue:function(t){return this.each(function(){mt.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,o=mt.Deferred(),a=this,r=this.length,s=function(){--i||o.resolveWith(a,[a])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(n=jt.get(a[r],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),o.promise(e)}});var Bt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ut=new RegExp("^(?:([+-])=|)("+Bt+")([a-z%]*)$","i"),qt=["Top","Right","Bottom","Left"],Wt=ut.documentElement,Vt=function(t){return mt.contains(t.ownerDocument,t)},Gt={composed:!0};Wt.getRootNode&&(Vt=function(t){return mt.contains(t.ownerDocument,t)||t.getRootNode(Gt)===t.ownerDocument});var Xt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&Vt(t)&&"none"===mt.css(t,"display")},Qt={};mt.fn.extend({show:function(){return y(this,!0)},hide:function(){return y(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){Xt(this)?mt(this).show():mt(this).hide()})}});var Kt,Zt,Jt=/^(?:checkbox|radio)$/i,te=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ee=/^$|^module$|\/(?:java|ecma)script/i;Kt=ut.createDocumentFragment().appendChild(ut.createElement("div")),(Zt=ut.createElement("input")).setAttribute("type","radio"),Zt.setAttribute("checked","checked"),Zt.setAttribute("name","t"),Kt.appendChild(Zt),lt.checkClone=Kt.cloneNode(!0).cloneNode(!0).lastChild.checked,Kt.innerHTML="<textarea>x</textarea>",lt.noCloneChecked=!!Kt.cloneNode(!0).lastChild.defaultValue,Kt.innerHTML="<option></option>",lt.option=!!Kt.lastChild;var ne={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ne.tbody=ne.tfoot=ne.colgroup=ne.caption=ne.thead,ne.th=ne.td,lt.option||(ne.optgroup=ne.option=[1,"<select multiple='multiple'>","</select>"]);var ie=/<|&#?\w+;/,oe=/^([^.]*)(?:\.(.+)|)/;mt.event={global:{},add:function(t,e,n,i,o){var a,r,s,l,c,d,u,p,h,f,m,g=jt.get(t);if(It(t))for(n.handler&&(n=(a=n).handler,o=a.selector),o&&mt.find.matchesSelector(Wt,o),n.guid||(n.guid=mt.guid++),(l=g.events)||(l=g.events=Object.create(null)),(r=g.handle)||(r=g.handle=function(e){return"undefined"!=typeof mt&&mt.event.triggered!==e.type?mt.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(Ot)||[""]).length;c--;)h=m=(s=oe.exec(e[c])||[])[1],f=(s[2]||"").split(".").sort(),h&&(u=mt.event.special[h]||{},h=(o?u.delegateType:u.bindType)||h,u=mt.event.special[h]||{},d=mt.extend({type:h,origType:m,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&mt.expr.match.needsContext.test(o),namespace:f.join(".")},a),(p=l[h])||((p=l[h]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,i,f,r)||t.addEventListener&&t.addEventListener(h,r)),u.add&&(u.add.call(t,d),d.handler.guid||(d.handler.guid=n.guid)),o?p.splice(p.delegateCount++,0,d):p.push(d),mt.event.global[h]=!0)},remove:function(t,e,n,i,o){var a,r,s,l,c,d,u,p,h,f,m,g=jt.hasData(t)&&jt.get(t);if(g&&(l=g.events)){for(c=(e=(e||"").match(Ot)||[""]).length;c--;)if(h=m=(s=oe.exec(e[c])||[])[1],f=(s[2]||"").split(".").sort(),h){for(u=mt.event.special[h]||{},p=l[h=(i?u.delegateType:u.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=a=p.length;a--;)d=p[a],!o&&m!==d.origType||n&&n.guid!==d.guid||s&&!s.test(d.namespace)||i&&i!==d.selector&&("**"!==i||!d.selector)||(p.splice(a,1),d.selector&&p.delegateCount--,u.remove&&u.remove.call(t,d));r&&!p.length&&(u.teardown&&!1!==u.teardown.call(t,f,g.handle)||mt.removeEvent(t,h,g.handle),delete l[h])}else for(h in l)mt.event.remove(t,h+e[c],n,i,!0);mt.isEmptyObject(l)&&jt.remove(t,"handle events")}},dispatch:function(t){var e,n,i,o,a,r,s=new Array(arguments.length),l=mt.event.fix(t),c=(jt.get(this,"events")||Object.create(null))[l.type]||[],d=mt.event.special[l.type]||{};for(s[0]=l,e=1;e<arguments.length;e++)s[e]=arguments[e];if(l.delegateTarget=this,!d.preDispatch||!1!==d.preDispatch.call(this,l)){for(r=mt.event.handlers.call(this,l,c),e=0;(o=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,n=0;(a=o.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==a.namespace&&!l.rnamespace.test(a.namespace)||(l.handleObj=a,l.data=a.data,void 0!==(i=((mt.event.special[a.origType]||{}).handle||a.handler).apply(o.elem,s))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return d.postDispatch&&d.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,i,o,a,r,s=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(a=[],r={},n=0;n<l;n++)void 0===r[o=(i=e[n]).selector+" "]&&(r[o]=i.needsContext?-1<mt(o,this).index(c):mt.find(o,this,null,[c]).length),r[o]&&a.push(i);a.length&&s.push({elem:c,handlers:a})}return c=this,l<e.length&&s.push({elem:c,handlers:e.slice(l)}),s},addProp:function(t,e){Object.defineProperty(mt.Event.prototype,t,{enumerable:!0,configurable:!0,get:ct(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[mt.expando]?t:new mt.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return Jt.test(e.type)&&e.click&&a(e,"input")&&S(e,"click",!0),!1},trigger:function(t){var e=this||t;return Jt.test(e.type)&&e.click&&a(e,"input")&&S(e,"click"),!0},_default:function(t){var e=t.target;return Jt.test(e.type)&&e.click&&a(e,"input")&&jt.get(e,"click")||a(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},mt.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},mt.Event=function(t,e){return this instanceof mt.Event?(t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?_:k,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&mt.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[mt.expando]=!0,void 0):new mt.Event(t,e)},mt.Event.prototype={constructor:mt.Event,isDefaultPrevented:k,isPropagationStopped:k,isImmediatePropagationStopped:k,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=_,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=_,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=_,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},mt.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},mt.event.addProp),mt.each({focus:"focusin",blur:"focusout"},function(t,e){function n(t){if(ut.documentMode){var n=jt.get(this,"handle"),i=mt.event.fix(t);i.type="focusin"===t.type?"focus":"blur",i.isSimulated=!0,n(t),i.target===i.currentTarget&&n(i)}else mt.event.simulate(e,t.target,mt.event.fix(t))}mt.event.special[t]={setup:function(){var i;return S(this,t,!0),!!ut.documentMode&&((i=jt.get(this,e))||this.addEventListener(e,n),void jt.set(this,e,(i||0)+1))},trigger:function(){return S(this,t),!0},teardown:function(){var t;return!!ut.documentMode&&void((t=jt.get(this,e)-1)?jt.set(this,e,t):(this.removeEventListener(e,n),jt.remove(this,e)))},_default:function(e){return jt.get(e.target,t)},delegateType:e},mt.event.special[e]={setup:function(){var i=this.ownerDocument||this.document||this,o=ut.documentMode?this:i,a=jt.get(o,e);a||(ut.documentMode?this.addEventListener(e,n):i.addEventListener(t,n,!0)),jt.set(o,e,(a||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,o=ut.documentMode?this:i,a=jt.get(o,e)-1;a?jt.set(o,e,a):(ut.documentMode?this.removeEventListener(e,n):i.removeEventListener(t,n,!0),jt.remove(o,e))}}}),mt.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){mt.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=t.relatedTarget,o=t.handleObj;return i&&(i===this||mt.contains(this,i))||(t.type=o.origType,n=o.handler.apply(this,arguments),t.type=e),n}}}),mt.fn.extend({on:function(t,e,n,i){return C(this,t,e,n,i)},one:function(t,e,n,i){return C(this,t,e,n,i,1)},off:function(t,e,n){var i,o;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,mt(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=k),this.each(function(){mt.event.remove(this,t,n,e)})}});var ae=/<script|<style|<link/i,re=/checked\s*(?:[^=]|=\s*.checked.)/i,se=/^\s*<!\[CDATA\[|\]\]>\s*$/g;mt.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var i,o,a,r,s,l,c,d=t.cloneNode(!0),u=Vt(t);if(!(lt.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||mt.isXMLDoc(t)))for(r=b(d),i=0,o=(a=b(t)).length;i<o;i++)s=a[i],l=r[i],"input"===(c=l.nodeName.toLowerCase())&&Jt.test(s.type)?l.checked=s.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=s.defaultValue);if(e)if(n)for(a=a||b(t),r=r||b(d),i=0,o=a.length;i<o;i++)E(a[i],r[i]);else E(t,d);return 0<(r=b(d,"script")).length&&x(r,!u&&b(t,"script")),d},cleanData:function(t){for(var e,n,i,o=mt.event.special,a=0;void 0!==(n=t[a]);a++)if(It(n)){if(e=n[jt.expando]){if(e.events)for(i in e.events)o[i]?mt.event.remove(n,i):mt.removeEvent(n,i,e.handle);n[jt.expando]=void 0}n[Ht.expando]&&(n[Ht.expando]=void 0)}}}),mt.fn.extend({detach:function(t){return A(this,t,!0)},remove:function(t){return A(this,t)},text:function(t){return Rt(this,function(t){return void 0===t?mt.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return F(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||T(this,t).appendChild(t)})},prepend:function(){return F(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=T(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return F(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return F(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(mt.cleanData(b(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return mt.clone(this,t,e)})},html:function(t){return Rt(this,function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!ae.test(t)&&!ne[(te.exec(t)||["",""])[1].toLowerCase()]){t=mt.htmlPrefilter(t);try{for(;n<i;n++)1===(e=this[n]||{}).nodeType&&(mt.cleanData(b(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return F(this,arguments,function(e){var n=this.parentNode;mt.inArray(this,t)<0&&(mt.cleanData(b(this)),n&&n.replaceChild(e,this))},t)}}),mt.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){mt.fn[t]=function(t){for(var n,i=[],o=mt(t),a=o.length-1,r=0;r<=a;r++)n=r===a?this:this.clone(!0),mt(o[r])[e](n),et.apply(i,n.get());return this.pushStack(i)}});var le=new RegExp("^("+Bt+")(?!px)[a-z%]+$","i"),ce=/^--/,de=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)},ue=function(t,e,n){var i,o,a={};for(o in e)a[o]=t.style[o],t.style[o]=e[o];for(o in i=n.call(t),e)t.style[o]=a[o];return i},pe=new RegExp(qt.join("|"),"i");!function(){function e(){if(d){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",d.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Wt.appendChild(c).appendChild(d);var e=t.getComputedStyle(d);i="1%"!==e.top,l=12===n(e.marginLeft),d.style.right="60%",r=36===n(e.right),o=36===n(e.width),d.style.position="absolute",a=12===n(d.offsetWidth/3),Wt.removeChild(c),d=null}}function n(t){return Math.round(parseFloat(t))}var i,o,a,r,s,l,c=ut.createElement("div"),d=ut.createElement("div");d.style&&(d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",lt.clearCloneStyle="content-box"===d.style.backgroundClip,mt.extend(lt,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),r},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var e,n,i,o;return null==s&&(e=ut.createElement("table"),n=ut.createElement("tr"),i=ut.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",n.style.cssText="box-sizing:content-box;border:1px solid",n.style.height="1px",i.style.height="9px",i.style.display="block",Wt.appendChild(e).appendChild(n).appendChild(i),o=t.getComputedStyle(n),s=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===n.offsetHeight,Wt.removeChild(e)),s}}))}();var he=["Webkit","Moz","ms"],fe=ut.createElement("div").style,me={},ge=/^(none|table(?!-c[ea]).+)/,ve={position:"absolute",visibility:"hidden",display:"block"},ye={letterSpacing:"0",fontWeight:"400"};mt.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=O(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,a,r,s=f(e),l=ce.test(e),c=t.style;if(l||(e=L(s)),r=mt.cssHooks[e]||mt.cssHooks[s],void 0===n)return r&&"get"in r&&void 0!==(o=r.get(t,!1,i))?o:c[e];"string"==(a=typeof n)&&(o=Ut.exec(n))&&o[1]&&(n=v(t,e,o),a="number"),null!=n&&n==n&&("number"!==a||l||(n+=o&&o[3]||(mt.cssNumber[s]?"":"px")),lt.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(n=r.set(t,n,i))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,i){var o,a,r,s=f(e);return ce.test(e)||(e=L(s)),(r=mt.cssHooks[e]||mt.cssHooks[s])&&"get"in r&&(o=r.get(t,!0,n)),void 0===o&&(o=O(t,e,i)),"normal"===o&&e in ye&&(o=ye[e]),""===n||n?(a=parseFloat(o),!0===n||isFinite(a)?a||0:o):o}}),mt.each(["height","width"],function(t,e){mt.cssHooks[e]={get:function(t,n,i){if(n)return!ge.test(mt.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?P(t,e,i):ue(t,ve,function(){return P(t,e,i)})},set:function(t,n,i){var o,a=de(t),r=!lt.scrollboxSize()&&"absolute"===a.position,s=(r||i)&&"border-box"===mt.css(t,"boxSizing",!1,a),l=i?M(t,e,i,s,a):0;return s&&r&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(a[e])-M(t,e,"border",!1,a)-.5)),l&&(o=Ut.exec(n))&&"px"!==(o[3]||"px")&&(t.style[e]=n,n=mt.css(t,e)),R(0,n,l)}}}),mt.cssHooks.marginLeft=N(lt.reliableMarginLeft,function(t,e){if(e)return(parseFloat(O(t,"marginLeft"))||t.getBoundingClientRect().left-ue(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),mt.each({margin:"",padding:"",border:"Width"},function(t,e){mt.cssHooks[t+e]={expand:function(n){for(var i=0,o={},a="string"==typeof n?n.split(" "):[n];i<4;i++)o[t+qt[i]+e]=a[i]||a[i-2]||a[0];return o}},"margin"!==t&&(mt.cssHooks[t+e].set=R)}),mt.fn.extend({css:function(t,e){return Rt(this,function(t,e,n){var i,o,a={},r=0;if(Array.isArray(e)){for(i=de(t),o=e.length;r<o;r++)a[e[r]]=mt.css(t,e[r],!1,i);return a}return void 0!==n?mt.style(t,e,n):mt.css(t,e)},t,e,1<arguments.length)}}),((mt.Tween=I).prototype={constructor:I,init:function(t,e,n,i,o,a){this.elem=t,this.prop=n,this.easing=o||mt.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=a||(mt.cssNumber[n]?"":"px")},cur:function(){var t=I.propHooks[this.prop];return t&&t.get?t.get(this):I.propHooks._default.get(this)},run:function(t){var e,n=I.propHooks[this.prop];return this.options.duration?this.pos=e=mt.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):I.propHooks._default.set(this),this}}).init.prototype=I.prototype,(I.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=mt.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){mt.fx.step[t.prop]?mt.fx.step[t.prop](t):1!==t.elem.nodeType||!mt.cssHooks[t.prop]&&null==t.elem.style[L(t.prop)]?t.elem[t.prop]=t.now:mt.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=I.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},mt.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},mt.fx=I.prototype.init,mt.fx.step={};var be,xe,we,_e,ke=/^(?:toggle|show|hide)$/,Ce=/queueHooks$/;mt.Animation=mt.extend(B,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return v(n.elem,t,Ut.exec(e),n),n}]},tweener:function(t,e){ct(t)?(e=t,t=["*"]):t=t.match(Ot);for(var n,i=0,o=t.length;i<o;i++)n=t[i],B.tweeners[n]=B.tweeners[n]||[],B.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var i,o,a,r,s,l,c,d,u="width"in e||"height"in e,p=this,h={},f=t.style,m=t.nodeType&&Xt(t),g=jt.get(t,"fxshow");for(i in n.queue||(null==(r=mt._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,s=r.empty.fire,r.empty.fire=function(){r.unqueued||s()}),r.unqueued++,p.always(function(){p.always(function(){r.unqueued--,mt.queue(t,"fx").length||r.empty.fire()})})),e)if(o=e[i],ke.test(o)){if(delete e[i],a=a||"toggle"===o,o===(m?"hide":"show")){if("show"!==o||!g||void 0===g[i])continue;m=!0}h[i]=g&&g[i]||mt.style(t,i)}if((l=!mt.isEmptyObject(e))||!mt.isEmptyObject(h))for(i in u&&1===t.nodeType&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=jt.get(t,"display")),"none"===(d=mt.css(t,"display"))&&(c?d=c:(y([t],!0),c=t.style.display||c,d=mt.css(t,"display"),y([t]))),("inline"===d||"inline-block"===d&&null!=c)&&"none"===mt.css(t,"float")&&(l||(p.done(function(){f.display=c}),null==c&&(d=f.display,c="none"===d?"":d)),f.display="inline-block")),n.overflow&&(f.overflow="hidden",p.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),l=!1,h)l||(g?"hidden"in g&&(m=g.hidden):g=jt.access(t,"fxshow",{display:c}),a&&(g.hidden=!m),m&&y([t],!0),p.done(function(){for(i in m||y([t]),jt.remove(t,"fxshow"),h)mt.style(t,i,h[i])})),l=Y(m?g[i]:0,i,p),i in g||(g[i]=l.start,m&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?B.prefilters.unshift(t):B.prefilters.push(t)}}),mt.speed=function(t,e,n){var i=t&&"object"==typeof t?mt.extend({},t):{complete:n||!n&&e||ct(t)&&t,duration:t,easing:n&&e||e&&!ct(e)&&e};return mt.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in mt.fx.speeds?i.duration=mt.fx.speeds[i.duration]:i.duration=mt.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){ct(i.old)&&i.old.call(this),i.queue&&mt.dequeue(this,i.queue)},i},mt.fn.extend({fadeTo:function(t,e,n,i){return this.filter(Xt).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var o=mt.isEmptyObject(t),a=mt.speed(e,n,i),r=function(){var e=B(this,mt.extend({},t),a);(o||jt.get(this,"finish"))&&e.stop(!0)};return r.finish=r,o||!1===a.queue?this.each(r):this.queue(a.queue,r)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,o=null!=t&&t+"queueHooks",a=mt.timers,r=jt.get(this);if(o)r[o]&&r[o].stop&&i(r[o]);else for(o in r)r[o]&&r[o].stop&&Ce.test(o)&&i(r[o]);for(o=a.length;o--;)a[o].elem!==this||null!=t&&a[o].queue!==t||(a[o].anim.stop(n),e=!1,a.splice(o,1));!e&&n||mt.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,n=jt.get(this),i=n[t+"queue"],o=n[t+"queueHooks"],a=mt.timers,r=i?i.length:0;for(n.finish=!0,mt.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===t&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<r;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish})}}),mt.each(["toggle","show","hide"],function(t,e){var n=mt.fn[e];mt.fn[e]=function(t,i,o){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(z(e,!0),t,i,o)}}),mt.each({slideDown:z("show"),slideUp:z("hide"),slideToggle:z("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){mt.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}}),mt.timers=[],mt.fx.tick=function(){var t,e=0,n=mt.timers;for(be=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||mt.fx.stop(),be=void 0},mt.fx.timer=function(t){mt.timers.push(t),mt.fx.start()},mt.fx.interval=13,mt.fx.start=function(){xe||(xe=!0,j())},mt.fx.stop=function(){xe=null},mt.fx.speeds={slow:600,
fast:200,_default:400},mt.fn.delay=function(e,n){return e=mt.fx&&mt.fx.speeds[e]||e,n=n||"fx",this.queue(n,function(n,i){var o=t.setTimeout(n,e);i.stop=function(){t.clearTimeout(o)}})},we=ut.createElement("input"),_e=ut.createElement("select").appendChild(ut.createElement("option")),we.type="checkbox",lt.checkOn=""!==we.value,lt.optSelected=_e.selected,(we=ut.createElement("input")).value="t",we.type="radio",lt.radioValue="t"===we.value;var Se,Te=mt.expr.attrHandle;mt.fn.extend({attr:function(t,e){return Rt(this,mt.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){mt.removeAttr(this,t)})}}),mt.extend({attr:function(t,e,n){var i,o,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return"undefined"==typeof t.getAttribute?mt.prop(t,e,n):(1===a&&mt.isXMLDoc(t)||(o=mt.attrHooks[e.toLowerCase()]||(mt.expr.match.bool.test(e)?Se:void 0)),void 0!==n?null===n?void mt.removeAttr(t,e):o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:(t.setAttribute(e,n+""),n):o&&"get"in o&&null!==(i=o.get(t,e))?i:null==(i=mt.find.attr(t,e))?void 0:i)},attrHooks:{type:{set:function(t,e){if(!lt.radioValue&&"radio"===e&&a(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i=0,o=e&&e.match(Ot);if(o&&1===t.nodeType)for(;n=o[i++];)t.removeAttribute(n)}}),Se={set:function(t,e,n){return!1===e?mt.removeAttr(t,n):t.setAttribute(n,n),n}},mt.each(mt.expr.match.bool.source.match(/\w+/g),function(t,e){var n=Te[e]||mt.find.attr;Te[e]=function(t,e,i){var o,a,r=e.toLowerCase();return i||(a=Te[r],Te[r]=o,o=null!=n(t,e,i)?r:null,Te[r]=a),o}});var $e=/^(?:input|select|textarea|button)$/i,De=/^(?:a|area)$/i;mt.fn.extend({prop:function(t,e){return Rt(this,mt.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[mt.propFix[t]||t]})}}),mt.extend({prop:function(t,e,n){var i,o,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return 1===a&&mt.isXMLDoc(t)||(e=mt.propFix[e]||e,o=mt.propHooks[e]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(t,n,e))?i:t[e]=n:o&&"get"in o&&null!==(i=o.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=mt.find.attr(t,"tabindex");return e?parseInt(e,10):$e.test(t.nodeName)||De.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),lt.optSelected||(mt.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),mt.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){mt.propFix[this.toLowerCase()]=this}),mt.fn.extend({addClass:function(t){var e,n,i,o,a,r;return ct(t)?this.each(function(e){mt(this).addClass(t.call(this,e,q(this)))}):(e=W(t)).length?this.each(function(){if(i=q(this),n=1===this.nodeType&&" "+U(i)+" "){for(a=0;a<e.length;a++)o=e[a],n.indexOf(" "+o+" ")<0&&(n+=o+" ");r=U(n),i!==r&&this.setAttribute("class",r)}}):this},removeClass:function(t){var e,n,i,o,a,r;return ct(t)?this.each(function(e){mt(this).removeClass(t.call(this,e,q(this)))}):arguments.length?(e=W(t)).length?this.each(function(){if(i=q(this),n=1===this.nodeType&&" "+U(i)+" "){for(a=0;a<e.length;a++)for(o=e[a];-1<n.indexOf(" "+o+" ");)n=n.replace(" "+o+" "," ");r=U(n),i!==r&&this.setAttribute("class",r)}}):this:this.attr("class","")},toggleClass:function(t,e){var n,i,o,a,r=typeof t,s="string"===r||Array.isArray(t);return ct(t)?this.each(function(n){mt(this).toggleClass(t.call(this,n,q(this),e),e)}):"boolean"==typeof e&&s?e?this.addClass(t):this.removeClass(t):(n=W(t),this.each(function(){if(s)for(a=mt(this),o=0;o<n.length;o++)i=n[o],a.hasClass(i)?a.removeClass(i):a.addClass(i);else void 0!==t&&"boolean"!==r||((i=q(this))&&jt.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===t?"":jt.get(this,"__className__")||""))}))},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+U(q(n))+" ").indexOf(e))return!0;return!1}});var Ee=/\r/g;mt.fn.extend({val:function(t){var e,n,i,o=this[0];return arguments.length?(i=ct(t),this.each(function(n){var o;1===this.nodeType&&(null==(o=i?t.call(this,n,mt(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=mt.map(o,function(t){return null==t?"":t+""})),(e=mt.valHooks[this.type]||mt.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))})):o?(e=mt.valHooks[o.type]||mt.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(Ee,""):null==n?"":n:void 0}}),mt.extend({valHooks:{option:{get:function(t){var e=mt.find.attr(t,"value");return null!=e?e:U(mt.text(t))}},select:{get:function(t){var e,n,i,o=t.options,r=t.selectedIndex,s="select-one"===t.type,l=s?null:[],c=s?r+1:o.length;for(i=r<0?c:s?r:0;i<c;i++)if(((n=o[i]).selected||i===r)&&!n.disabled&&(!n.parentNode.disabled||!a(n.parentNode,"optgroup"))){if(e=mt(n).val(),s)return e;l.push(e)}return l},set:function(t,e){for(var n,i,o=t.options,a=mt.makeArray(e),r=o.length;r--;)((i=o[r]).selected=-1<mt.inArray(mt.valHooks.option.get(i),a))&&(n=!0);return n||(t.selectedIndex=-1),a}}}}),mt.each(["radio","checkbox"],function(){mt.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<mt.inArray(mt(t).val(),e)}},lt.checkOn||(mt.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var Fe=t.location,Ae={guid:Date.now()},Oe=/\?/;mt.parseXML=function(e){var n,i;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(t){}return i=n&&n.getElementsByTagName("parsererror")[0],n&&!i||mt.error("Invalid XML: "+(i?mt.map(i.childNodes,function(t){return t.textContent}).join("\n"):e)),n};var Ne=/^(?:focusinfocus|focusoutblur)$/,Le=function(t){t.stopPropagation()};mt.extend(mt.event,{trigger:function(e,n,i,o){var a,r,s,l,c,d,u,p,h=[i||ut],f=at.call(e,"type")?e.type:e,m=at.call(e,"namespace")?e.namespace.split("."):[];if(r=p=s=i=i||ut,3!==i.nodeType&&8!==i.nodeType&&!Ne.test(f+mt.event.triggered)&&(-1<f.indexOf(".")&&(f=(m=f.split(".")).shift(),m.sort()),c=f.indexOf(":")<0&&"on"+f,(e=e[mt.expando]?e:new mt.Event(f,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=null==n?[e]:mt.makeArray(n,[e]),u=mt.event.special[f]||{},o||!u.trigger||!1!==u.trigger.apply(i,n))){if(!o&&!u.noBubble&&!dt(i)){for(l=u.delegateType||f,Ne.test(l+f)||(r=r.parentNode);r;r=r.parentNode)h.push(r),s=r;s===(i.ownerDocument||ut)&&h.push(s.defaultView||s.parentWindow||t)}for(a=0;(r=h[a++])&&!e.isPropagationStopped();)p=r,e.type=1<a?l:u.bindType||f,(d=(jt.get(r,"events")||Object.create(null))[e.type]&&jt.get(r,"handle"))&&d.apply(r,n),(d=c&&r[c])&&d.apply&&It(r)&&(e.result=d.apply(r,n),!1===e.result&&e.preventDefault());return e.type=f,o||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(h.pop(),n)||!It(i)||c&&ct(i[f])&&!dt(i)&&((s=i[c])&&(i[c]=null),mt.event.triggered=f,e.isPropagationStopped()&&p.addEventListener(f,Le),i[f](),e.isPropagationStopped()&&p.removeEventListener(f,Le),mt.event.triggered=void 0,s&&(i[c]=s)),e.result}},simulate:function(t,e,n){var i=mt.extend(new mt.Event,n,{type:t,isSimulated:!0});mt.event.trigger(i,null,e)}}),mt.fn.extend({trigger:function(t,e){return this.each(function(){mt.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return mt.event.trigger(t,e,n,!0)}});var Re=/\[\]$/,Me=/\r?\n/g,Pe=/^(?:submit|button|image|reset|file)$/i,Ie=/^(?:input|select|textarea|keygen)/i;mt.param=function(t,e){var n,i=[],o=function(t,e){var n=ct(e)?e():e;i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!mt.isPlainObject(t))mt.each(t,function(){o(this.name,this.value)});else for(n in t)V(n,t[n],e,o);return i.join("&")},mt.fn.extend({serialize:function(){return mt.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=mt.prop(this,"elements");return t?mt.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!mt(this).is(":disabled")&&Ie.test(this.nodeName)&&!Pe.test(t)&&(this.checked||!Jt.test(t))}).map(function(t,e){var n=mt(this).val();return null==n?null:Array.isArray(n)?mt.map(n,function(t){return{name:e.name,value:t.replace(Me,"\r\n")}}):{name:e.name,value:n.replace(Me,"\r\n")}}).get()}});var je=/%20/g,He=/#.*$/,ze=/([?&])_=[^&]*/,Ye=/^(.*?):[ \t]*([^\r\n]*)$/gm,Be=/^(?:GET|HEAD)$/,Ue=/^\/\//,qe={},We={},Ve="*/".concat("*"),Ge=ut.createElement("a");Ge.href=Fe.href,mt.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Fe.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Fe.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ve,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":mt.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Q(Q(t,mt.ajaxSettings),e):Q(mt.ajaxSettings,t)},ajaxPrefilter:G(qe),ajaxTransport:G(We),ajax:function(e,n){function i(e,n,i,s){var c,p,h,x,w,_=n;d||(d=!0,l&&t.clearTimeout(l),o=void 0,r=s||"",k.readyState=0<e?4:0,c=200<=e&&e<300||304===e,i&&(x=function(t,e,n){for(var i,o,a,r,s=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(o in s)if(s[o]&&s[o].test(i)){l.unshift(o);break}if(l[0]in n)a=l[0];else{for(o in n){if(!l[0]||t.converters[o+" "+l[0]]){a=o;break}r||(r=o)}a=a||r}if(a)return a!==l[0]&&l.unshift(a),n[a]}(f,k,i)),!c&&-1<mt.inArray("script",f.dataTypes)&&mt.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),x=function(t,e,n,i){var o,a,r,s,l,c={},d=t.dataTypes.slice();if(d[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(a=d.shift();a;)if(t.responseFields[a]&&(n[t.responseFields[a]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=a,a=d.shift())if("*"===a)a=l;else if("*"!==l&&l!==a){if(!(r=c[l+" "+a]||c["* "+a]))for(o in c)if((s=o.split(" "))[1]===a&&(r=c[l+" "+s[0]]||c["* "+s[0]])){!0===r?r=c[o]:!0!==c[o]&&(a=s[0],d.unshift(s[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+a}}}return{state:"success",data:e}}(f,x,k,c),c?(f.ifModified&&((w=k.getResponseHeader("Last-Modified"))&&(mt.lastModified[a]=w),(w=k.getResponseHeader("etag"))&&(mt.etag[a]=w)),204===e||"HEAD"===f.type?_="nocontent":304===e?_="notmodified":(_=x.state,p=x.data,c=!(h=x.error))):(h=_,!e&&_||(_="error",e<0&&(e=0))),k.status=e,k.statusText=(n||_)+"",c?v.resolveWith(m,[p,_,k]):v.rejectWith(m,[k,_,h]),k.statusCode(b),b=void 0,u&&g.trigger(c?"ajaxSuccess":"ajaxError",[k,f,c?p:h]),y.fireWith(m,[k,_]),u&&(g.trigger("ajaxComplete",[k,f]),--mt.active||mt.event.trigger("ajaxStop")))}"object"==typeof e&&(n=e,e=void 0),n=n||{};var o,a,r,s,l,c,d,u,p,h,f=mt.ajaxSetup({},n),m=f.context||f,g=f.context&&(m.nodeType||m.jquery)?mt(m):mt.event,v=mt.Deferred(),y=mt.Callbacks("once memory"),b=f.statusCode||{},x={},w={},_="canceled",k={readyState:0,getResponseHeader:function(t){var e;if(d){if(!s)for(s={};e=Ye.exec(r);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return d?r:null},setRequestHeader:function(t,e){return null==d&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==d&&(f.mimeType=t),this},statusCode:function(t){var e;if(t)if(d)k.always(t[k.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||_;return o&&o.abort(e),i(0,e),this}};if(v.promise(k),f.url=((e||f.url||Fe.href)+"").replace(Ue,Fe.protocol+"//"),f.type=n.method||n.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(Ot)||[""],null==f.crossDomain){c=ut.createElement("a");try{c.href=f.url,c.href=c.href,f.crossDomain=Ge.protocol+"//"+Ge.host!=c.protocol+"//"+c.host}catch(t){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=mt.param(f.data,f.traditional)),X(qe,f,n,k),d)return k;for(p in(u=mt.event&&f.global)&&0==mt.active++&&mt.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Be.test(f.type),a=f.url.replace(He,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(je,"+")):(h=f.url.slice(a.length),f.data&&(f.processData||"string"==typeof f.data)&&(a+=(Oe.test(a)?"&":"?")+f.data,delete f.data),!1===f.cache&&(a=a.replace(ze,"$1"),h=(Oe.test(a)?"&":"?")+"_="+Ae.guid++ +h),f.url=a+h),f.ifModified&&(mt.lastModified[a]&&k.setRequestHeader("If-Modified-Since",mt.lastModified[a]),mt.etag[a]&&k.setRequestHeader("If-None-Match",mt.etag[a])),(f.data&&f.hasContent&&!1!==f.contentType||n.contentType)&&k.setRequestHeader("Content-Type",f.contentType),k.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Ve+"; q=0.01":""):f.accepts["*"]),f.headers)k.setRequestHeader(p,f.headers[p]);if(f.beforeSend&&(!1===f.beforeSend.call(m,k,f)||d))return k.abort();if(_="abort",y.add(f.complete),k.done(f.success),k.fail(f.error),o=X(We,f,n,k)){if(k.readyState=1,u&&g.trigger("ajaxSend",[k,f]),d)return k;f.async&&0<f.timeout&&(l=t.setTimeout(function(){k.abort("timeout")},f.timeout));try{d=!1,o.send(x,i)}catch(t){if(d)throw t;i(-1,t)}}else i(-1,"No Transport");return k},getJSON:function(t,e,n){return mt.get(t,e,n,"json")},getScript:function(t,e){return mt.get(t,void 0,e,"script")}}),mt.each(["get","post"],function(t,e){mt[e]=function(t,n,i,o){return ct(n)&&(o=o||i,i=n,n=void 0),mt.ajax(mt.extend({url:t,type:e,dataType:o,data:n,success:i},mt.isPlainObject(t)&&t))}}),mt.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),mt._evalUrl=function(t,e,n){return mt.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){mt.globalEval(t,e,n)}})},mt.fn.extend({wrapAll:function(t){var e;return this[0]&&(ct(t)&&(t=t.call(this[0])),e=mt(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return ct(t)?this.each(function(e){mt(this).wrapInner(t.call(this,e))}):this.each(function(){var e=mt(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=ct(t);return this.each(function(n){mt(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){mt(this).replaceWith(this.childNodes)}),this}}),mt.expr.pseudos.hidden=function(t){return!mt.expr.pseudos.visible(t)},mt.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},mt.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Xe={0:200,1223:204},Qe=mt.ajaxSettings.xhr();lt.cors=!!Qe&&"withCredentials"in Qe,lt.ajax=Qe=!!Qe,mt.ajaxTransport(function(e){var n,i;if(lt.cors||Qe&&!e.crossDomain)return{send:function(o,a){var r,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)s[r]=e.xhrFields[r];for(r in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)s.setRequestHeader(r,o[r]);n=function(t){return function(){n&&(n=i=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===t?s.abort():"error"===t?"number"!=typeof s.status?a(0,"error"):a(s.status,s.statusText):a(Xe[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),i=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=i:s.onreadystatechange=function(){4===s.readyState&&t.setTimeout(function(){n&&i()})},n=n("abort");try{s.send(e.hasContent&&e.data||null)}catch(t){if(n)throw t}},abort:function(){n&&n()}}}),mt.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),mt.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return mt.globalEval(t),t}}}),mt.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),mt.ajaxTransport("script",function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(i,o){e=mt("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&o("error"===t.type?404:200,t.type)}),ut.head.appendChild(e[0])},abort:function(){n&&n()}}});var Ke,Ze=[],Je=/(=)\?(?=&|$)|\?\?/;mt.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ze.pop()||mt.expando+"_"+Ae.guid++;return this[t]=!0,t}}),mt.ajaxPrefilter("json jsonp",function(e,n,i){var o,a,r,s=!1!==e.jsonp&&(Je.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Je.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=ct(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Je,"$1"+o):!1!==e.jsonp&&(e.url+=(Oe.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return r||mt.error(o+" was not called"),r[0]},e.dataTypes[0]="json",a=t[o],t[o]=function(){r=arguments},i.always(function(){void 0===a?mt(t).removeProp(o):t[o]=a,e[o]&&(e.jsonpCallback=n.jsonpCallback,Ze.push(o)),r&&ct(a)&&a(r[0]),r=a=void 0}),"script"}),lt.createHTMLDocument=((Ke=ut.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ke.childNodes.length),mt.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(lt.createHTMLDocument?((i=(e=ut.implementation.createHTMLDocument("")).createElement("base")).href=ut.location.href,e.head.appendChild(i)):e=ut),a=!n&&[],(o=$t.exec(t))?[e.createElement(o[1])]:(o=w([t],e,a),a&&a.length&&mt(a).remove(),mt.merge([],o.childNodes)));var i,o,a},mt.fn.load=function(t,e,n){var i,o,a,r=this,s=t.indexOf(" ");return-1<s&&(i=U(t.slice(s)),t=t.slice(0,s)),ct(e)?(n=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<r.length&&mt.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){a=arguments,r.html(i?mt("<div>").append(mt.parseHTML(t)).find(i):t)}).always(n&&function(t,e){r.each(function(){n.apply(this,a||[t.responseText,e,t])})}),this},mt.expr.pseudos.animated=function(t){return mt.grep(mt.timers,function(e){return t===e.elem}).length},mt.offset={setOffset:function(t,e,n){var i,o,a,r,s,l,c=mt.css(t,"position"),d=mt(t),u={};"static"===c&&(t.style.position="relative"),s=d.offset(),a=mt.css(t,"top"),l=mt.css(t,"left"),("absolute"===c||"fixed"===c)&&-1<(a+l).indexOf("auto")?(r=(i=d.position()).top,o=i.left):(r=parseFloat(a)||0,o=parseFloat(l)||0),ct(e)&&(e=e.call(t,n,mt.extend({},s))),null!=e.top&&(u.top=e.top-s.top+r),null!=e.left&&(u.left=e.left-s.left+o),"using"in e?e.using.call(t,u):d.css(u)}},mt.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){mt.offset.setOffset(this,t,e)});var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,i=this[0],o={top:0,left:0};if("fixed"===mt.css(i,"position"))e=i.getBoundingClientRect();else{for(e=this.offset(),n=i.ownerDocument,t=i.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===mt.css(t,"position");)t=t.parentNode;t&&t!==i&&1===t.nodeType&&((o=mt(t).offset()).top+=mt.css(t,"borderTopWidth",!0),o.left+=mt.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-mt.css(i,"marginTop",!0),left:e.left-o.left-mt.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===mt.css(t,"position");)t=t.offsetParent;return t||Wt})}}),mt.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n="pageYOffset"===e;mt.fn[t]=function(i){return Rt(this,function(t,i,o){var a;return dt(t)?a=t:9===t.nodeType&&(a=t.defaultView),void 0===o?a?a[e]:t[i]:void(a?a.scrollTo(n?a.pageXOffset:o,n?o:a.pageYOffset):t[i]=o)},t,i,arguments.length)}}),mt.each(["top","left"],function(t,e){mt.cssHooks[e]=N(lt.pixelPosition,function(t,n){if(n)return n=O(t,e),le.test(n)?mt(t).position()[e]+"px":n})}),mt.each({Height:"height",Width:"width"},function(t,e){mt.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,i){mt.fn[i]=function(o,a){var r=arguments.length&&(n||"boolean"!=typeof o),s=n||(!0===o||!0===a?"margin":"border");return Rt(this,function(e,n,o){var a;return dt(e)?0===i.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(a=e.documentElement,Math.max(e.body["scroll"+t],a["scroll"+t],e.body["offset"+t],a["offset"+t],a["client"+t])):void 0===o?mt.css(e,n,s):mt.style(e,n,o,s)},e,r?o:void 0,r)}})}),mt.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){mt.fn[e]=function(t){return this.on(e,t)}}),mt.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),mt.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){mt.fn[e]=function(t,n){return 0<arguments.length?this.on(e,null,t,n):this.trigger(e)}});var tn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;mt.proxy=function(t,e){var n,i,o;if("string"==typeof e&&(n=t[e],e=t,t=n),ct(t))return i=J.call(arguments,2),(o=function(){return t.apply(e||this,i.concat(J.call(arguments)))}).guid=t.guid=t.guid||mt.guid++,o},mt.holdReady=function(t){t?mt.readyWait++:mt.ready(!0)},mt.isArray=Array.isArray,mt.parseJSON=JSON.parse,mt.nodeName=a,mt.isFunction=ct,mt.isWindow=dt,mt.camelCase=f,mt.type=i,mt.now=Date.now,mt.isNumeric=function(t){var e=mt.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},mt.trim=function(t){return null==t?"":(t+"").replace(tn,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return mt});var en=t.jQuery,nn=t.$;return mt.noConflict=function(e){return t.$===mt&&(t.$=nn),e&&t.jQuery===mt&&(t.jQuery=en),mt},"undefined"==typeof e&&(t.jQuery=t.$=mt),mt}),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(t){"use strict";var e=jQuery.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1==e[0]&&9==e[1]&&e[2]<1||3<e[0])throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4")}(),function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var n=!1,i=this;return t(this).one("bsTransitionEnd",function(){n=!0}),setTimeout(function(){n||t(i).trigger(t.support.transition.end)},e),this},t(function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var n in e)if(void 0!==t.style[n])return{end:e[n]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),function(t){"use strict";var e='[data-dismiss="alert"]',n=function(n){t(n).on("click",e,this.close)};n.VERSION="3.4.1",n.TRANSITION_DURATION=150,n.prototype.close=function(e){function i(){r.detach().trigger("closed.bs.alert").remove()}var o=t(this),a=o.attr("data-target");a||(a=(a=o.attr("href"))&&a.replace(/.*(?=#[^\s]*$)/,"")),a="#"===a?[]:a;var r=t(document).find(a);e&&e.preventDefault(),r.length||(r=o.closest(".alert")),r.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one("bsTransitionEnd",i).emulateTransitionEnd(n.TRANSITION_DURATION):i())};var i=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var i=t(this),o=i.data("bs.alert");o||i.data("bs.alert",o=new n(this)),"string"==typeof e&&o[e].call(i)})},t.fn.alert.Constructor=n,t.fn.alert.noConflict=function(){return t.fn.alert=i,this},t(document).on("click.bs.alert.data-api",e,n.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.button"),a="object"==typeof e&&e;o||i.data("bs.button",o=new n(this,a)),"toggle"==e?o.toggle():e&&o.setState(e)})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.isLoading=!1};n.VERSION="3.4.1",n.DEFAULTS={loadingText:"loading..."},n.prototype.setState=function(e){var n="disabled",i=this.$element,o=i.is("input")?"val":"html",a=i.data();e+="Text",null==a.resetText&&i.data("resetText",i[o]()),setTimeout(t.proxy(function(){i[o](null==a[e]?this.options[e]:a[e]),"loadingText"==e?(this.isLoading=!0,i.addClass(n).attr(n,n).prop(n,!0)):this.isLoading&&(this.isLoading=!1,i.removeClass(n).removeAttr(n).prop(n,!1))},this),0)},n.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var n=this.$element.find("input");"radio"==n.prop("type")?(n.prop("checked")&&(t=!1),e.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==n.prop("type")&&(n.prop("checked")!==this.$element.hasClass("active")&&(t=!1),this.$element.toggleClass("active")),n.prop("checked",this.$element.hasClass("active")),t&&n.trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")};var i=t.fn.button;t.fn.button=e,t.fn.button.Constructor=n,t.fn.button.noConflict=function(){return t.fn.button=i,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(n){var i=t(n.target).closest(".btn");e.call(i,"toggle"),t(n.target).is('input[type="radio"], input[type="checkbox"]')||(n.preventDefault(),i.is("input,button")?i.trigger("focus"):i.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){t(e.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(e.type))})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.carousel"),a=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e),r="string"==typeof e?e:a.slide;o||i.data("bs.carousel",o=new n(this,a)),"number"==typeof e?o.to(e):r?o[r]():a.interval&&o.pause().cycle()})}var n=function(e,n){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=n,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",t.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};n.VERSION="3.4.1",n.TRANSITION_DURATION=600,n.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},n.prototype.keydown=function(t){if(!/input|textarea/i.test(t.target.tagName)){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()}},n.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},n.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},n.prototype.getItemForDirection=function(t,e){var n=this.getItemIndex(e);if(("prev"==t&&0===n||"next"==t&&n==this.$items.length-1)&&!this.options.wrap)return e;var i=(n+("prev"==t?-1:1))%this.$items.length;return this.$items.eq(i)},n.prototype.to=function(t){var e=this,n=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(t>this.$items.length-1||t<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){e.to(t)}):n==t?this.pause().cycle():this.slide(n<t?"next":"prev",this.$items.eq(t))},n.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},n.prototype.next=function(){if(!this.sliding)return this.slide("next")},n.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},n.prototype.slide=function(e,i){var o=this.$element.find(".item.active"),a=i||this.getItemForDirection(e,o),r=this.interval,s="next"==e?"left":"right",l=this;if(a.hasClass("active"))return this.sliding=!1;var c=a[0],d=t.Event("slide.bs.carousel",{relatedTarget:c,direction:s});if(this.$element.trigger(d),!d.isDefaultPrevented()){if(this.sliding=!0,r&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var u=t(this.$indicators.children()[this.getItemIndex(a)]);u&&u.addClass("active")}var p=t.Event("slid.bs.carousel",{relatedTarget:c,direction:s});return t.support.transition&&this.$element.hasClass("slide")?(a.addClass(e),"object"==typeof a&&a.length&&a[0].offsetWidth,o.addClass(s),a.addClass(s),o.one("bsTransitionEnd",function(){a.removeClass([e,s].join(" ")).addClass("active"),o.removeClass(["active",s].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(p)},0)}).emulateTransitionEnd(n.TRANSITION_DURATION)):(o.removeClass("active"),a.addClass("active"),this.sliding=!1,this.$element.trigger(p)),r&&this.cycle(),this}};var i=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=n,t.fn.carousel.noConflict=function(){return t.fn.carousel=i,this};var o=function(n){var i=t(this),o=i.attr("href");o&&(o=o.replace(/.*(?=#[^\s]+$)/,""));var a=i.attr("data-target")||o,r=t(document).find(a);if(r.hasClass("carousel")){var s=t.extend({},r.data(),i.data()),l=i.attr("data-slide-to");l&&(s.interval=!1),e.call(r,s),l&&r.data("bs.carousel").to(l),n.preventDefault()}};t(document).on("click.bs.carousel.data-api","[data-slide]",o).on("click.bs.carousel.data-api","[data-slide-to]",o),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var n=t(this);e.call(n,n.data())})})}(jQuery),function(t){"use strict";function e(e){var n,i=e.attr("data-target")||(n=e.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,"");return t(document).find(i)}function n(e){return this.each(function(){var n=t(this),o=n.data("bs.collapse"),a=t.extend({},i.DEFAULTS,n.data(),"object"==typeof e&&e);!o&&a.toggle&&/show|hide/.test(e)&&(a.toggle=!1),o||n.data("bs.collapse",o=new i(this,a)),"string"==typeof e&&o[e]()})}var i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.$trigger=t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'),
this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};i.VERSION="3.4.1",i.TRANSITION_DURATION=350,i.DEFAULTS={toggle:!0},i.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},i.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var e,o=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(o&&o.length&&(e=o.data("bs.collapse"))&&e.transitioning)){var a=t.Event("show.bs.collapse");if(this.$element.trigger(a),!a.isDefaultPrevented()){o&&o.length&&(n.call(o,"hide"),e||o.data("bs.collapse",null));var r=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[r](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("collapse in")[r](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return s.call(this);var l=t.camelCase(["scroll",r].join("-"));this.$element.one("bsTransitionEnd",t.proxy(s,this)).emulateTransitionEnd(i.TRANSITION_DURATION)[r](this.$element[0][l])}}}},i.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var o=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};if(!t.support.transition)return o.call(this);this.$element[n](0).one("bsTransitionEnd",t.proxy(o,this)).emulateTransitionEnd(i.TRANSITION_DURATION)}}},i.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},i.prototype.getParent=function(){return t(document).find(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(t.proxy(function(n,i){var o=t(i);this.addAriaAndCollapsedClass(e(o),o)},this)).end()},i.prototype.addAriaAndCollapsedClass=function(t,e){var n=t.hasClass("in");t.attr("aria-expanded",n),e.toggleClass("collapsed",!n).attr("aria-expanded",n)};var o=t.fn.collapse;t.fn.collapse=n,t.fn.collapse.Constructor=i,t.fn.collapse.noConflict=function(){return t.fn.collapse=o,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(i){var o=t(this);o.attr("data-target")||i.preventDefault();var a=e(o),r=a.data("bs.collapse")?"toggle":o.data();n.call(a,r)})}(jQuery),function(t){"use strict";function e(e){var n=e.attr("data-target");n||(n=(n=e.attr("href"))&&/#[A-Za-z]/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,""));var i="#"!==n?t(document).find(n):null;return i&&i.length?i:e.parent()}function n(n){n&&3===n.which||(t(".dropdown-backdrop").remove(),t(i).each(function(){var i=t(this),o=e(i),a={relatedTarget:this};o.hasClass("open")&&(n&&"click"==n.type&&/input|textarea/i.test(n.target.tagName)&&t.contains(o[0],n.target)||(o.trigger(n=t.Event("hide.bs.dropdown",a)),n.isDefaultPrevented()||(i.attr("aria-expanded","false"),o.removeClass("open").trigger(t.Event("hidden.bs.dropdown",a)))))}))}var i='[data-toggle="dropdown"]',o=function(e){t(e).on("click.bs.dropdown",this.toggle)};o.VERSION="3.4.1",o.prototype.toggle=function(i){var o=t(this);if(!o.is(".disabled, :disabled")){var a=e(o),r=a.hasClass("open");if(n(),!r){"ontouchstart"in document.documentElement&&!a.closest(".navbar-nav").length&&t(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(t(this)).on("click",n);var s={relatedTarget:this};if(a.trigger(i=t.Event("show.bs.dropdown",s)),i.isDefaultPrevented())return;o.trigger("focus").attr("aria-expanded","true"),a.toggleClass("open").trigger(t.Event("shown.bs.dropdown",s))}return!1}},o.prototype.keydown=function(n){if(/(38|40|27|32)/.test(n.which)&&!/input|textarea/i.test(n.target.tagName)){var o=t(this);if(n.preventDefault(),n.stopPropagation(),!o.is(".disabled, :disabled")){var a=e(o),r=a.hasClass("open");if(!r&&27!=n.which||r&&27==n.which)return 27==n.which&&a.find(i).trigger("focus"),o.trigger("click");var s=a.find(".dropdown-menu li:not(.disabled):visible a");if(s.length){var l=s.index(n.target);38==n.which&&0<l&&l--,40==n.which&&l<s.length-1&&l++,~l||(l=0),s.eq(l).trigger("focus")}}}};var a=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var n=t(this),i=n.data("bs.dropdown");i||n.data("bs.dropdown",i=new o(this)),"string"==typeof e&&i[e].call(n)})},t.fn.dropdown.Constructor=o,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=a,this},t(document).on("click.bs.dropdown.data-api",n).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",i,o.prototype.toggle).on("keydown.bs.dropdown.data-api",i,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,i){return this.each(function(){var o=t(this),a=o.data("bs.modal"),r=t.extend({},n.DEFAULTS,o.data(),"object"==typeof e&&e);a||o.data("bs.modal",a=new n(this,r)),"string"==typeof e?a[e](i):r.show&&a.show(i)})}var n=function(e,n){this.options=n,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};n.VERSION="3.4.1",n.TRANSITION_DURATION=300,n.BACKDROP_TRANSITION_DURATION=150,n.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},n.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},n.prototype.show=function(e){var i=this,o=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(o),this.isShown||o.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){i.$element.one("mouseup.dismiss.bs.modal",function(e){t(e.target).is(i.$element)&&(i.ignoreBackdropClick=!0)})}),this.backdrop(function(){var o=t.support.transition&&i.$element.hasClass("fade");i.$element.parent().length||i.$element.appendTo(i.$body),i.$element.show().scrollTop(0),i.adjustDialog(),o&&i.$element[0].offsetWidth,i.$element.addClass("in"),i.enforceFocus();var a=t.Event("shown.bs.modal",{relatedTarget:e});o?i.$dialog.one("bsTransitionEnd",function(){i.$element.trigger("focus").trigger(a)}).emulateTransitionEnd(n.TRANSITION_DURATION):i.$element.trigger("focus").trigger(a)}))},n.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(n.TRANSITION_DURATION):this.hideModal())},n.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){document===t.target||this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},n.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},n.prototype.resize=function(){this.isShown?t(window).on("resize.bs.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.bs.modal")},n.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$body.removeClass("modal-open"),t.resetAdjustments(),t.resetScrollbar(),t.$element.trigger("hidden.bs.modal")})},n.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},n.prototype.backdrop=function(e){var i=this,o=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var a=t.support.transition&&o;if(this.$backdrop=t(document.createElement("div")).addClass("modal-backdrop "+o).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())},this)),a&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;a?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(n.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var r=function(){i.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",r).emulateTransitionEnd(n.BACKDROP_TRANSITION_DURATION):r()}else e&&e()},n.prototype.handleUpdate=function(){this.adjustDialog()},n.prototype.adjustDialog=function(){var t=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&t?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!t?this.scrollbarWidth:""})},n.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},n.prototype.checkScrollbar=function(){var t=window.innerWidth;if(!t){var e=document.documentElement.getBoundingClientRect();t=e.right-Math.abs(e.left)}this.bodyIsOverflowing=document.body.clientWidth<t,this.scrollbarWidth=this.measureScrollbar()},n.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";var n=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("padding-right",e+n),t(this.fixedContent).each(function(e,i){var o=i.style.paddingRight,a=t(i).css("padding-right");t(i).data("padding-right",o).css("padding-right",parseFloat(a)+n+"px")}))},n.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad),t(this.fixedContent).each(function(e,n){var i=t(n).data("padding-right");t(n).removeData("padding-right"),n.style.paddingRight=i||""})},n.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var i=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=n,t.fn.modal.noConflict=function(){return t.fn.modal=i,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(n){var i=t(this),o=i.attr("href"),a=i.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,""),r=t(document).find(a),s=r.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(o)&&o},r.data(),i.data());i.is("a")&&n.preventDefault(),r.one("show.bs.modal",function(t){t.isDefaultPrevented()||r.one("hidden.bs.modal",function(){i.is(":visible")&&i.trigger("focus")})}),e.call(r,s,this)})}(jQuery),function(t){"use strict";function e(e,n){var i=e.nodeName.toLowerCase();if(-1!==t.inArray(i,n))return-1===t.inArray(i,o)||Boolean(e.nodeValue.match(r)||e.nodeValue.match(s));for(var a=t(n).filter(function(t,e){return e instanceof RegExp}),l=0,c=a.length;l<c;l++)if(i.match(a[l]))return!0;return!1}function n(n,i,o){if(0===n.length)return n;if(o&&"function"==typeof o)return o(n);if(!document.implementation||!document.implementation.createHTMLDocument)return n;var a=document.implementation.createHTMLDocument("sanitization");a.body.innerHTML=n;for(var r=t.map(i,function(t,e){return e}),s=t(a.body).find("*"),l=0,c=s.length;l<c;l++){var d=s[l],u=d.nodeName.toLowerCase();if(-1!==t.inArray(u,r))for(var p=t.map(d.attributes,function(t){return t}),h=[].concat(i["*"]||[],i[u]||[]),f=0,m=p.length;f<m;f++)e(p[f],h)||d.removeAttribute(p[f].nodeName);else d.parentNode.removeChild(d)}return a.body.innerHTML}var i=["sanitize","whiteList","sanitizeFn"],o=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],a={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},r=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,s=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i,l=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};l.VERSION="3.4.1",l.TRANSITION_DURATION=150,l.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:a},l.prototype.init=function(e,n,i){if(this.enabled=!0,this.type=e,this.$element=t(n),this.options=this.getOptions(i),this.$viewport=this.options.viewport&&t(document).find(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var o=this.options.trigger.split(" "),a=o.length;a--;){var r=o[a];if("click"==r)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=r){var s="hover"==r?"mouseenter":"focusin",l="hover"==r?"mouseleave":"focusout";this.$element.on(s+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},l.prototype.getDefaults=function(){return l.DEFAULTS},l.prototype.getOptions=function(e){var o=this.$element.data();for(var a in o)o.hasOwnProperty(a)&&-1!==t.inArray(a,i)&&delete o[a];return(e=t.extend({},this.getDefaults(),o,e)).delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e.sanitize&&(e.template=n(e.template,e.whiteList,e.sanitizeFn)),e},l.prototype.getDelegateOptions=function(){var e={},n=this.getDefaults();return this._options&&t.each(this._options,function(t,i){n[t]!=i&&(e[t]=i)}),e},l.prototype.enter=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),e instanceof t.Event&&(n.inState["focusin"==e.type?"focus":"hover"]=!0),n.tip().hasClass("in")||"in"==n.hoverState)n.hoverState="in";else{if(clearTimeout(n.timeout),n.hoverState="in",!n.options.delay||!n.options.delay.show)return n.show();n.timeout=setTimeout(function(){"in"==n.hoverState&&n.show()},n.options.delay.show)}},l.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},l.prototype.leave=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),e instanceof t.Event&&(n.inState["focusout"==e.type?"focus":"hover"]=!1),!n.isInStateTrue()){if(clearTimeout(n.timeout),n.hoverState="out",!n.options.delay||!n.options.delay.hide)return n.hide();n.timeout=setTimeout(function(){"out"==n.hoverState&&n.hide()},n.options.delay.hide)}},l.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var n=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!n)return;var i=this,o=this.tip(),a=this.getUID(this.type);this.setContent(),o.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&o.addClass("fade");var r="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,s=/\s?auto?\s?/i,c=s.test(r);c&&(r=r.replace(s,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(r).data("bs."+this.type,this),this.options.container?o.appendTo(t(document).find(this.options.container)):o.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var d=this.getPosition(),u=o[0].offsetWidth,p=o[0].offsetHeight;if(c){var h=r,f=this.getPosition(this.$viewport);r="bottom"==r&&d.bottom+p>f.bottom?"top":"top"==r&&d.top-p<f.top?"bottom":"right"==r&&d.right+u>f.width?"left":"left"==r&&d.left-u<f.left?"right":r,o.removeClass(h).addClass(r)}var m=this.getCalculatedOffset(r,d,u,p);this.applyPlacement(m,r);var g=function(){var t=i.hoverState;i.$element.trigger("shown.bs."+i.type),i.hoverState=null,"out"==t&&i.leave(i)};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",g).emulateTransitionEnd(l.TRANSITION_DURATION):g()}},l.prototype.applyPlacement=function(e,n){var i=this.tip(),o=i[0].offsetWidth,a=i[0].offsetHeight,r=parseInt(i.css("margin-top"),10),s=parseInt(i.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(s)&&(s=0),e.top+=r,e.left+=s,t.offset.setOffset(i[0],t.extend({using:function(t){i.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),i.addClass("in");var l=i[0].offsetWidth,c=i[0].offsetHeight;"top"==n&&c!=a&&(e.top=e.top+a-c);var d=this.getViewportAdjustedDelta(n,e,l,c);d.left?e.left+=d.left:e.top+=d.top;var u=/top|bottom/.test(n),p=u?2*d.left-o+l:2*d.top-a+c,h=u?"offsetWidth":"offsetHeight";i.offset(e),this.replaceArrow(p,i[0][h],u)},l.prototype.replaceArrow=function(t,e,n){this.arrow().css(n?"left":"top",50*(1-t/e)+"%").css(n?"top":"left","")},l.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();this.options.html?(this.options.sanitize&&(e=n(e,this.options.whiteList,this.options.sanitizeFn)),t.find(".tooltip-inner").html(e)):t.find(".tooltip-inner").text(e),t.removeClass("fade in top bottom left right")},l.prototype.hide=function(e){function n(){"in"!=i.hoverState&&o.detach(),i.$element&&i.$element.removeAttr("aria-describedby").trigger("hidden.bs."+i.type),e&&e()}var i=this,o=t(this.$tip),a=t.Event("hide.bs."+this.type);if(this.$element.trigger(a),!a.isDefaultPrevented())return o.removeClass("in"),t.support.transition&&o.hasClass("fade")?o.one("bsTransitionEnd",n).emulateTransitionEnd(l.TRANSITION_DURATION):n(),this.hoverState=null,this},l.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},l.prototype.hasContent=function(){return this.getTitle()},l.prototype.getPosition=function(e){var n=(e=e||this.$element)[0],i="BODY"==n.tagName,o=n.getBoundingClientRect();null==o.width&&(o=t.extend({},o,{width:o.right-o.left,height:o.bottom-o.top}));var a=window.SVGElement&&n instanceof window.SVGElement,r=i?{top:0,left:0}:a?null:e.offset(),s={scroll:i?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},l=i?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},o,s,l,r)},l.prototype.getCalculatedOffset=function(t,e,n,i){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-n/2}:"top"==t?{top:e.top-i,left:e.left+e.width/2-n/2}:"left"==t?{top:e.top+e.height/2-i/2,left:e.left-n}:{top:e.top+e.height/2-i/2,left:e.left+e.width}},l.prototype.getViewportAdjustedDelta=function(t,e,n,i){var o={top:0,left:0};if(!this.$viewport)return o;var a=this.options.viewport&&this.options.viewport.padding||0,r=this.getPosition(this.$viewport);if(/right|left/.test(t)){var s=e.top-a-r.scroll,l=e.top+a-r.scroll+i;s<r.top?o.top=r.top-s:l>r.top+r.height&&(o.top=r.top+r.height-l)}else{var c=e.left-a,d=e.left+a+n;c<r.left?o.left=r.left-c:d>r.right&&(o.left=r.left+r.width-d)}return o},l.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},l.prototype.getUID=function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},l.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},l.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},l.prototype.enable=function(){this.enabled=!0},l.prototype.disable=function(){this.enabled=!1},l.prototype.toggleEnabled=function(){this.enabled=!this.enabled},l.prototype.toggle=function(e){var n=this;e&&((n=t(e.currentTarget).data("bs."+this.type))||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n))),e?(n.inState.click=!n.inState.click,n.isInStateTrue()?n.enter(n):n.leave(n)):n.tip().hasClass("in")?n.leave(n):n.enter(n)},l.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide(function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null,t.$element=null})},l.prototype.sanitizeHtml=function(t){return n(t,this.options.whiteList,this.options.sanitizeFn)};var c=t.fn.tooltip;t.fn.tooltip=function(e){return this.each(function(){var n=t(this),i=n.data("bs.tooltip"),o="object"==typeof e&&e;!i&&/destroy|hide/.test(e)||(i||n.data("bs.tooltip",i=new l(this,o)),"string"==typeof e&&i[e]())})},t.fn.tooltip.Constructor=l,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=c,this}}(jQuery),function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.VERSION="3.4.1",e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),((e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype)).constructor=e).prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),n=this.getContent();if(this.options.html){var i=typeof n;this.options.sanitize&&(e=this.sanitizeHtml(e),"string"===i&&(n=this.sanitizeHtml(n))),t.find(".popover-title").html(e),t.find(".popover-content").children().detach().end()["string"===i?"html":"append"](n)}else t.find(".popover-title").text(e),t.find(".popover-content").children().detach().end().text(n);t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var n=t.fn.popover;t.fn.popover=function(n){return this.each(function(){var i=t(this),o=i.data("bs.popover"),a="object"==typeof n&&n;!o&&/destroy|hide/.test(n)||(o||i.data("bs.popover",o=new e(this,a)),"string"==typeof n&&o[n]())})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=n,this}}(jQuery),function(t){"use strict";function e(n,i){this.$body=t(document.body),this.$scrollElement=t(t(n).is(document.body)?window:n),this.options=t.extend({},e.DEFAULTS,i),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",t.proxy(this.process,this)),this.refresh(),this.process()}function n(n){return this.each(function(){var i=t(this),o=i.data("bs.scrollspy"),a="object"==typeof n&&n;o||i.data("bs.scrollspy",o=new e(this,a)),"string"==typeof n&&o[n]()})}e.VERSION="3.4.1",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e=this,n="offset",i=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),t.isWindow(this.$scrollElement[0])||(n="position",i=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var e=t(this),o=e.data("target")||e.attr("href"),a=/^#./.test(o)&&t(o);return a&&a.length&&a.is(":visible")&&[[a[n]().top+i,o]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){e.offsets.push(this[0]),e.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,n=this.getScrollHeight(),i=this.options.offset+n-this.$scrollElement.height(),o=this.offsets,a=this.targets,r=this.activeTarget;if(this.scrollHeight!=n&&this.refresh(),i<=e)return r!=(t=a[a.length-1])&&this.activate(t);if(r&&e<o[0])return this.activeTarget=null,this.clear();for(t=o.length;t--;)r!=a[t]&&e>=o[t]&&(void 0===o[t+1]||e<o[t+1])&&this.activate(a[t])},e.prototype.activate=function(e){this.activeTarget=e,this.clear();var n=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',i=t(n).parents("li").addClass("active");i.parent(".dropdown-menu").length&&(i=i.closest("li.dropdown").addClass("active")),i.trigger("activate.bs.scrollspy")},e.prototype.clear=function(){t(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var i=t.fn.scrollspy;t.fn.scrollspy=n,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=i,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);n.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.tab");o||i.data("bs.tab",o=new n(this)),"string"==typeof e&&o[e]()})}var n=function(e){this.element=t(e)};n.VERSION="3.4.1",n.TRANSITION_DURATION=150,n.prototype.show=function(){var e=this.element,n=e.closest("ul:not(.dropdown-menu)"),i=e.data("target");if(i||(i=(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var o=n.find(".active:last a"),a=t.Event("hide.bs.tab",{relatedTarget:e[0]}),r=t.Event("show.bs.tab",{relatedTarget:o[0]});if(o.trigger(a),e.trigger(r),!r.isDefaultPrevented()&&!a.isDefaultPrevented()){var s=t(document).find(i);this.activate(e.closest("li"),n),this.activate(s,s.parent(),function(){o.trigger({type:"hidden.bs.tab",relatedTarget:e[0]}),e.trigger({type:"shown.bs.tab",relatedTarget:o[0]})})}}},n.prototype.activate=function(e,i,o){function a(){r.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),e.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),s?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu").length&&e.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),o&&o()}var r=i.find("> .active"),s=o&&t.support.transition&&(r.length&&r.hasClass("fade")||!!i.find("> .fade").length);r.length&&s?r.one("bsTransitionEnd",a).emulateTransitionEnd(n.TRANSITION_DURATION):a(),r.removeClass("in")};var i=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=n,t.fn.tab.noConflict=function(){return t.fn.tab=i,this};var o=function(n){n.preventDefault(),e.call(t(this),"show")};t(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',o).on("click.bs.tab.data-api",'[data-toggle="pill"]',o)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.affix"),a="object"==typeof e&&e;o||i.data("bs.affix",o=new n(this,a)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.options=t.extend({},n.DEFAULTS,i);var o=this.options.target===n.DEFAULTS.target?t(this.options.target):t(document).find(this.options.target);this.$target=o.on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};n.VERSION="3.4.1",n.RESET="affix affix-top affix-bottom",n.DEFAULTS={offset:0,target:window},n.prototype.getState=function(t,e,n,i){var o=this.$target.scrollTop(),a=this.$element.offset(),r=this.$target.height();if(null!=n&&"top"==this.affixed)return o<n&&"top";if("bottom"==this.affixed)return null!=n?!(o+this.unpin<=a.top)&&"bottom":!(o+r<=t-i)&&"bottom";var s=null==this.affixed,l=s?o:a.top;return null!=n&&o<=n?"top":null!=i&&t-i<=l+(s?r:e)&&"bottom"},n.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(n.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},n.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},n.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=this.$element.height(),i=this.options.offset,o=i.top,a=i.bottom,r=Math.max(t(document).height(),t(document.body).height());"object"!=typeof i&&(a=o=i),"function"==typeof o&&(o=i.top(this.$element)),"function"==typeof a&&(a=i.bottom(this.$element));var s=this.getState(r,e,o,a);if(this.affixed!=s){null!=this.unpin&&this.$element.css("top","");var l="affix"+(s?"-"+s:""),c=t.Event(l+".bs.affix");if(this.$element.trigger(c),c.isDefaultPrevented())return;this.affixed=s,this.unpin="bottom"==s?this.getPinnedOffset():null,this.$element.removeClass(n.RESET).addClass(l).trigger(l.replace("affix","affixed")+".bs.affix")}"bottom"==s&&this.$element.offset({top:r-e-a})}};var i=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=n,t.fn.affix.noConflict=function(){return t.fn.affix=i,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var n=t(this),i=n.data();i.offset=i.offset||{},null!=i.offsetBottom&&(i.offset.bottom=i.offsetBottom),null!=i.offsetTop&&(i.offset.top=i.offsetTop),e.call(n,i)})})}(jQuery),define("bootstrap",["jquery"],function(){}),require.config({urlArgs:"v="+requirejs.s.contexts._.config.config.site.version,packages:[{name:"moment",location:"../libs/moment",main:"moment"}],include:["css","layer","toastr","fast","frontend","frontend-init","table","form","dragsort","selectpage"],paths:{lang:"empty:",form:"require-form",table:"require-table",upload:"require-upload",dropzone:"dropzone.min",echarts:"echarts.min","echarts-theme":"echarts-theme",adminlte:"adminlte","bootstrap-table-commonsearch":"bootstrap-table-commonsearch","bootstrap-table-template":"bootstrap-table-template",jquery:"../libs/jquery/dist/jquery.min",bootstrap:"../libs/bootstrap/dist/js/bootstrap.min","bootstrap-datetimepicker":"../libs/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min","bootstrap-daterangepicker":"../libs/bootstrap-daterangepicker/daterangepicker","bootstrap-select":"../libs/bootstrap-select/dist/js/bootstrap-select.min","bootstrap-select-lang":"../libs/bootstrap-select/dist/js/i18n/defaults-zh_CN","bootstrap-table":"../libs/bootstrap-table/dist/bootstrap-table.min","bootstrap-table-export":"../libs/bootstrap-table/dist/extensions/export/bootstrap-table-export.min",
"bootstrap-table-fixed-columns":"../libs/bootstrap-table/dist/extensions/fixed-columns/bootstrap-table-fixed-columns","bootstrap-table-mobile":"../libs/bootstrap-table/dist/extensions/mobile/bootstrap-table-mobile","bootstrap-table-lang":"../libs/bootstrap-table/dist/locale/bootstrap-table-zh-CN","bootstrap-table-jumpto":"../libs/bootstrap-table/dist/extensions/page-jumpto/bootstrap-table-jumpto",tableexport:"../libs/tableExport.jquery.plugin/tableExport.min",dragsort:"../libs/fastadmin-dragsort/jquery.dragsort",sortable:"../libs/Sortable/Sortable.min",addtabs:"../libs/fastadmin-addtabs/jquery.addtabs",slimscroll:"../libs/jquery-slimscroll/jquery.slimscroll",validator:"../libs/nice-validator/dist/jquery.validator","validator-lang":"../libs/nice-validator/dist/local/zh-CN",toastr:"../libs/toastr/toastr",jstree:"../libs/jstree/dist/jstree.min",layer:"../libs/fastadmin-layer/dist/layer",cookie:"../libs/jquery.cookie/jquery.cookie",cxselect:"../libs/fastadmin-cxselect/js/jquery.cxselect",template:"../libs/art-template/dist/template-native",selectpage:"../libs/fastadmin-selectpage/selectpage",citypicker:"../libs/fastadmin-citypicker/dist/js/city-picker.min","citypicker-data":"../libs/fastadmin-citypicker/dist/js/city-picker.data"},shim:{addons:["frontend"],bootstrap:["jquery"],"bootstrap-table":{deps:["bootstrap"],exports:"$.fn.bootstrapTable"},"bootstrap-table-lang":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-export":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-fixed-columns":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-mobile":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-advancedsearch":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-commonsearch":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-template":{deps:["bootstrap-table","template"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-jumpto":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},tableexport:{deps:["jquery"],exports:"$.fn.extend"},slimscroll:{deps:["jquery"],exports:"$.fn.extend"},adminlte:{deps:["bootstrap","slimscroll"],exports:"$.AdminLTE"},"bootstrap-daterangepicker":["moment/locale/zh-cn"],"bootstrap-datetimepicker":["moment/locale/zh-cn"],"bootstrap-select-lang":["bootstrap-select"],jstree:["css!../libs/jstree/dist/themes/default/style.css"],"validator-lang":["validator"],citypicker:["citypicker-data","css!../libs/fastadmin-citypicker/dist/css/city-picker.css"]},baseUrl:requirejs.s.contexts._.config.config.site.cdnurl+"/assets/js/",map:{"*":{css:"../libs/require-css/css.min"}},waitSeconds:60,charset:"utf-8"}),require(["jquery","bootstrap"],function(t,e){var n=requirejs.s.contexts._.config.config;window.Config=n;var i={};i.lang=n.moduleurl+"/ajax/lang?callback=define&controllername="+n.controllername+"&lang="+n.language,i["frontend/"]="frontend/",require.config({paths:i}),t(function(){require(["fast"],function(t){require(["frontend","frontend-init","addons"],function(t,i){n.jsname&&require([n.jsname],function(t){t[n.actionname]!=e&&t[n.actionname]()},function(t){console.error(t)})})})})}),define("require-frontend",function(){}),define("../libs/require-css/css.min",[],function(){if("undefined"==typeof window)return{load:function(t,e,n){n()}};var t=document.getElementsByTagName("head")[0],e=window.navigator.userAgent.match(/Trident\/([^ ;]*)|AppleWebKit\/([^ ;]*)|Opera\/([^ ;]*)|rv\:([^ ;]*)(.*?)Gecko\/([^ ;]*)|MSIE\s([^ ;]*)|AndroidWebKit\/([^ ;]*)/)||0,n=!1,i=!0;e[1]||e[7]?n=parseInt(e[1])<6||parseInt(e[7])<=9:e[2]||e[8]?i=!1:e[4]&&(n=parseInt(e[4])<18);var o={};o.pluginBuilder="./css-builder";var a,r,s,l=function(){a=document.createElement("style"),t.appendChild(a),r=a.styleSheet||a.sheet},c=0,d=[],u=function(t){r.addImport(t),a.onload=function(){p()},c++,31==c&&(l(),c=0)},p=function(){s();var t=d.shift();return t?(s=t[1],void u(t[0])):void(s=null)},h=function(t,e){if(r&&r.addImport||l(),r&&r.addImport)s?d.push([t,e]):(u(t),s=e);else{a.textContent='@import "'+t+'";';var n=setInterval(function(){try{a.sheet.cssRules,clearInterval(n),e()}catch(t){}},10)}},f=function(e,n){var o=document.createElement("link");if(o.type="text/css",o.rel="stylesheet",i)o.onload=function(){o.onload=function(){},setTimeout(n,7)};else var a=setInterval(function(){for(var t=0;t<document.styleSheets.length;t++){var e=document.styleSheets[t];if(e.href==o.href)return clearInterval(a),n()}},10);o.href=e,t.appendChild(o)};return o.normalize=function(t,e){return".css"==t.substr(t.length-4,4)&&(t=t.substr(0,t.length-4)),e(t)},o.load=function(t,e,i,o){(n?h:f)(e.toUrl(t+".css"),i)},o}),!function(t,e){"use strict";var n,i,o=t.layui&&layui.define,a={getPath:function(){var e=document.currentScript?document.currentScript.src:function(){for(var t,e=document.scripts,n=e.length-1,i=n;i>0;i--)if("interactive"===e[i].readyState){t=e[i].src;break}return t||e[n].src}(),n=t.LAYUI_GLOBAL||{};return n.layer_dir||e.substring(0,e.lastIndexOf("/")+1)}(),config:{},end:{},minIndex:0,minLeft:[],btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],type:["dialog","page","iframe","loading","tips"],getStyle:function(e,n){var i=e.currentStyle?e.currentStyle:t.getComputedStyle(e,null);return i[i.getPropertyValue?"getPropertyValue":"getAttribute"](n)},link:function(e,n,i){if(r.path){var o=document.getElementsByTagName("head")[0],s=document.createElement("link");"string"==typeof n&&(i=n);var l=(i||e).replace(/\.|\//g,""),c="layuicss-"+l,d="creating",u=0;s.rel="stylesheet",s.href=r.path+e,s.id=c,document.getElementById(c)||o.appendChild(s),"function"==typeof n&&!function e(i){var o=100,r=document.getElementById(c);return++u>1e4/o?t.console&&console.error(l+".css: Invalid"):void(1989===parseInt(a.getStyle(r,"width"))?(i===d&&r.removeAttribute("lay-status"),r.getAttribute("lay-status")===d?setTimeout(e,o):n()):(r.setAttribute("lay-status",d),setTimeout(function(){e(d)},o)))}()}}},r={v:"3.5.2",ie:function(){var e=navigator.userAgent.toLowerCase();return!!(t.ActiveXObject||"ActiveXObject"in t)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")}(),index:t.layer&&t.layer.v?1e5:0,path:a.getPath,config:function(t,e){return t=t||{},c=r.cache=a.config=n.extend({},a.config,t),r.path=a.config.path||r.path,"string"==typeof t.extend&&(t.extend=[t.extend]),a.config.path&&r.ready(),t.extend?(o?layui.addcss("modules/layer/"+t.extend):a.link("theme/"+t.extend),this):this},ready:function(t){var e="layer",n="",i=(o?"modules/layer/":"theme/")+"default/layer.css?v="+r.v+n;return o?layui.addcss(i,t,e):a.link(i,t,e),this},alert:function(t,e,i){var o="function"==typeof e;return o&&(i=e),r.open(n.extend({content:t,yes:i},o?{}:e))},confirm:function(t,e,i,o){var s="function"==typeof e;return s&&(o=i,i=e),r.open(n.extend({content:t,btn:a.btn,yes:i,btn2:o},s?{}:e))},msg:function(t,i,o){var s="function"==typeof i,c=a.config.skin,d=(c?c+" "+c+"-msg":"")||"layui-layer-msg",u=l.anim.length-1;return s&&(o=i),r.open(n.extend({content:t,time:3e3,shade:!1,skin:d,title:!1,closeBtn:!1,btn:!1,resize:!1,end:o},s&&!a.config.skin?{skin:d+" layui-layer-hui",anim:u}:function(){return i=i||{},i.icon!==-1&&i.icon!==e||(i.skin=d+" "+(i.skin||"layui-layer-hui")),i}()))},load:function(t,e){return r.open(n.extend({type:3,icon:t||0,resize:!1,shade:.01},e))},tips:function(t,e,i){return r.open(n.extend({type:4,content:[t,e],closeBtn:!1,time:3e3,shade:!1,resize:!1,fixed:!1,maxWidth:260},i))}},s=function(t){var e=this,i=function(){e.creat()};e.index=++r.index,e.config=n.extend({},e.config,a.config,t),document.body?i():setTimeout(function(){i()},30)};s.pt=s.prototype;var l=["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"];l.anim=["layer-anim-00","layer-anim-01","layer-anim-02","layer-anim-03","layer-anim-04","layer-anim-05","layer-anim-06"],l.SHADE="layui-layer-shade",l.MOVE="layui-layer-move",s.pt.config={type:0,shade:.3,fixed:!0,move:l[1],title:"&#x4FE1;&#x606F;",offset:"auto",area:"auto",closeBtn:1,time:0,zIndex:19891014,maxWidth:360,anim:0,isOutAnim:!0,minStack:!0,focusBtn:0,icon:-1,moveType:1,resize:!0,scrollbar:!0,tips:2},s.pt.vessel=function(t,e){var i=this,o=i.index,r=i.config,s=r.zIndex+o,c="object"==typeof r.title,d=r.maxmin&&(1===r.type||2===r.type),u=r.title?'<div class="layui-layer-title" style="'+(c?r.title[1]:"")+'">'+(c?r.title[0]:r.title)+"</div>":"";return r.zIndex=s,e([r.shade?'<div class="'+l.SHADE+'" id="'+l.SHADE+o+'" times="'+o+'" style="'+("z-index:"+(s-1)+"; ")+'"></div>':"",'<div class="'+l[0]+(" layui-layer-"+a.type[r.type])+(0!=r.type&&2!=r.type||r.shade?"":" layui-layer-border")+" "+(r.skin||"")+'" id="'+l[0]+o+'" type="'+a.type[r.type]+'" times="'+o+'" showtime="'+r.time+'" conType="'+(t?"object":"string")+'" style="z-index: '+s+"; width:"+r.area[0]+";height:"+r.area[1]+";position:"+(r.fixed?"fixed;":"absolute;")+'">'+(t&&2!=r.type?"":u)+'<div id="'+(r.id||"")+'" class="layui-layer-content'+(0==r.type&&r.icon!==-1?" layui-layer-padding":"")+(3==r.type?" layui-layer-loading"+r.icon:"")+'">'+(0==r.type&&r.icon!==-1?'<i class="layui-layer-ico layui-layer-ico'+r.icon+'"></i>':"")+(1==r.type&&t?"":r.content||"")+'</div><span class="layui-layer-setwin">'+function(){var t=d?'<a class="layui-layer-min" href="javascript:;"><cite></cite></a><a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>':"";return r.closeBtn&&(t+='<a class="layui-layer-ico '+l[7]+" "+l[7]+(r.title?r.closeBtn:4==r.type?"1":"2")+'" href="javascript:;"></a>'),t}()+"</span>"+(r.btn?function(){var t="";"string"==typeof r.btn&&(r.btn=[r.btn]);for(var e=0,n=r.btn.length;e<n;e++)t+='<a class="'+l[6]+e+'" href="javascript:">'+r.btn[e]+"</a>";return'<div class="'+l[6]+" layui-layer-btn-"+(r.btnAlign||"")+'">'+t+"</div>"}():"")+(r.resize?'<span class="layui-layer-resize"></span>':"")+"</div>"],u,n('<div class="'+l.MOVE+'" id="'+l.MOVE+'"></div>')),i},s.pt.creat=function(){var t=this,e=t.config,o=t.index,s=e.content,c="object"==typeof s,d=n("body");if(!e.id||!n("#"+e.id)[0]){switch("string"==typeof e.area&&(e.area="auto"===e.area?["",""]:[e.area,""]),e.shift&&(e.anim=e.shift),6==r.ie&&(e.fixed=!1),e.type){case 0:e.btn="btn"in e?e.btn:a.btn[0],r.closeAll("dialog");break;case 2:var s=e.content=c?e.content:[e.content||"","auto"];e.content='<iframe scrolling="'+(e.content[1]||"auto")+'" allowtransparency="true" id="'+l[4]+o+'" name="'+l[4]+o+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+e.content[0]+'"></iframe>';break;case 3:delete e.title,delete e.closeBtn,e.icon===-1&&0===e.icon,r.closeAll("loading");break;case 4:c||(e.content=[e.content,"body"]),e.follow=e.content[1],e.content=e.content[0]+'<i class="layui-layer-TipsG"></i>',delete e.title,e.tips="object"==typeof e.tips?e.tips:[e.tips,!0],e.tipsMore||r.closeAll("tips")}if(t.vessel(c,function(i,r,u){d.append(i[0]),c?function(){2==e.type||4==e.type?function(){n("body").append(i[1])}():function(){s.parents("."+l[0])[0]||(s.data("display",s.css("display")).show().addClass("layui-layer-wrap").wrap(i[1]),n("#"+l[0]+o).find("."+l[5]).before(r))}()}():d.append(i[1]),n("#"+l.MOVE)[0]||d.append(a.moveElem=u),t.layero=n("#"+l[0]+o),t.shadeo=n("#"+l.SHADE+o),e.scrollbar||l.html.css("overflow","hidden").attr("layer-full",o)}).auto(o),t.shadeo.css({"background-color":e.shade[1]||"#000",opacity:e.shade[0]||e.shade}),2==e.type&&6==r.ie&&t.layero.find("iframe").attr("src",s[0]),4==e.type?t.tips():function(){t.offset(),parseInt(a.getStyle(document.getElementById(l.MOVE),"z-index"))||function(){t.layero.css("visibility","hidden"),r.ready(function(){t.offset(),t.layero.css("visibility","visible")})}()}(),e.fixed&&i.on("resize",function(){t.offset(),(/^\d+%$/.test(e.area[0])||/^\d+%$/.test(e.area[1]))&&t.auto(o),4==e.type&&t.tips()}),e.time<=0||setTimeout(function(){r.close(t.index)},e.time),t.move().callback(),l.anim[e.anim]){var u="layer-anim "+l.anim[e.anim];t.layero.addClass(u).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){n(this).removeClass(u)})}e.isOutAnim&&t.layero.data("isOutAnim",!0)}},s.pt.auto=function(t){var e=this,o=e.config,a=n("#"+l[0]+t);""===o.area[0]&&o.maxWidth>0&&(r.ie&&r.ie<8&&o.btn&&a.width(a.innerWidth()),a.outerWidth()>o.maxWidth&&a.width(o.maxWidth));var s=[a.innerWidth(),a.innerHeight()],c=a.find(l[1]).outerHeight()||0,d=a.find("."+l[6]).outerHeight()||0,u=function(t){t=a.find(t),t.height(s[1]-c-d-2*(0|parseFloat(t.css("padding-top"))))};switch(o.type){case 2:u("iframe");break;default:""===o.area[1]?o.maxHeight>0&&a.outerHeight()>o.maxHeight?(s[1]=o.maxHeight,u("."+l[5])):o.fixed&&s[1]>=i.height()&&(s[1]=i.height(),u("."+l[5])):u("."+l[5])}return e},s.pt.offset=function(){var t=this,e=t.config,n=t.layero,o=[n.outerWidth(),n.outerHeight()],a="object"==typeof e.offset;t.offsetTop=(i.height()-o[1])/2,t.offsetLeft=(i.width()-o[0])/2,a?(t.offsetTop=e.offset[0],t.offsetLeft=e.offset[1]||t.offsetLeft):"auto"!==e.offset&&("t"===e.offset?t.offsetTop=0:"r"===e.offset?t.offsetLeft=i.width()-o[0]:"b"===e.offset?t.offsetTop=i.height()-o[1]:"l"===e.offset?t.offsetLeft=0:"lt"===e.offset?(t.offsetTop=0,t.offsetLeft=0):"lb"===e.offset?(t.offsetTop=i.height()-o[1],t.offsetLeft=0):"rt"===e.offset?(t.offsetTop=0,t.offsetLeft=i.width()-o[0]):"rb"===e.offset?(t.offsetTop=i.height()-o[1],t.offsetLeft=i.width()-o[0]):t.offsetTop=e.offset),e.fixed||(t.offsetTop=/%$/.test(t.offsetTop)?i.height()*parseFloat(t.offsetTop)/100:parseFloat(t.offsetTop),t.offsetLeft=/%$/.test(t.offsetLeft)?i.width()*parseFloat(t.offsetLeft)/100:parseFloat(t.offsetLeft),t.offsetTop+=i.scrollTop(),t.offsetLeft+=i.scrollLeft()),n.attr("minLeft")&&(t.offsetTop=i.height()-(n.find(l[1]).outerHeight()||0),t.offsetLeft=n.css("left")),n.css({top:t.offsetTop,left:t.offsetLeft})},s.pt.tips=function(){var t=this,e=t.config,o=t.layero,a=[o.outerWidth(),o.outerHeight()],r=n(e.follow);r[0]||(r=n("body"));var s={width:r.outerWidth(),height:r.outerHeight(),top:r.offset().top,left:r.offset().left},c=o.find(".layui-layer-TipsG"),d=e.tips[0];e.tips[1]||c.remove(),s.autoLeft=function(){s.left+a[0]-i.width()>0?(s.tipLeft=s.left+s.width-a[0],c.css({right:12,left:"auto"})):s.tipLeft=s.left},s.where=[function(){s.autoLeft(),s.tipTop=s.top-a[1]-10,c.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",e.tips[1])},function(){s.tipLeft=s.left+s.width+10,s.tipTop=s.top,c.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",e.tips[1])},function(){s.autoLeft(),s.tipTop=s.top+s.height+10,c.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",e.tips[1])},function(){s.tipLeft=s.left-a[0]-10,s.tipTop=s.top,c.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",e.tips[1])}],s.where[d-1](),1===d?s.top-(i.scrollTop()+a[1]+16)<0&&s.where[2]():2===d?i.width()-(s.left+s.width+a[0]+16)>0||s.where[3]():3===d?s.top-i.scrollTop()+s.height+a[1]+16-i.height()>0&&s.where[0]():4===d&&a[0]+16-s.left>0&&s.where[1](),o.find("."+l[5]).css({"background-color":e.tips[1],"padding-right":e.closeBtn?"30px":""}),o.css({left:s.tipLeft-(e.fixed?i.scrollLeft():0),top:s.tipTop-(e.fixed?i.scrollTop():0)})},s.pt.move=function(){var t=this,e=t.config,o=n(document),s=t.layero,l=s.find(e.move),c=s.find(".layui-layer-resize"),d={};return e.move&&l.css("cursor","move"),l.on("mousedown",function(t){t.preventDefault(),e.move&&(d.moveStart=!0,d.offset=[t.clientX-parseFloat(s.css("left")),t.clientY-parseFloat(s.css("top"))],a.moveElem.css("cursor","move").show())}),c.on("mousedown",function(t){t.preventDefault(),d.resizeStart=!0,d.offset=[t.clientX,t.clientY],d.area=[s.outerWidth(),s.outerHeight()],a.moveElem.css("cursor","se-resize").show()}),o.on("mousemove",function(n){if(d.moveStart){var o=n.clientX-d.offset[0],a=n.clientY-d.offset[1],l="fixed"===s.css("position");if(n.preventDefault(),d.stX=l?0:i.scrollLeft(),d.stY=l?0:i.scrollTop(),!e.moveOut){var c=i.width()-s.outerWidth()+d.stX,u=i.height()-s.outerHeight()+d.stY;o<d.stX&&(o=d.stX),o>c&&(o=c),a<d.stY&&(a=d.stY),a>u&&(a=u)}s.css({left:o,top:a})}if(e.resize&&d.resizeStart){var o=n.clientX-d.offset[0],a=n.clientY-d.offset[1];n.preventDefault(),r.style(t.index,{width:d.area[0]+o,height:d.area[1]+a}),d.isResize=!0,e.resizing&&e.resizing(s)}}).on("mouseup",function(t){d.moveStart&&(delete d.moveStart,a.moveElem.hide(),e.moveEnd&&e.moveEnd(s)),d.resizeStart&&(delete d.resizeStart,a.moveElem.hide())}),t},s.pt.callback=function(){function t(){var t=o.cancel&&o.cancel(e.index,i);t===!1||r.close(e.index)}var e=this,i=e.layero,o=e.config;if(e.openLayer(),o.success&&(2==o.type?i.find("iframe").on("load",function(){o.success(i,e.index)}):o.success(i,e.index)),6==r.ie&&e.IE6(i),i.find("."+l[6]).children("a").on("click",function(){var t=n(this).index();if(0===t)o.yes?o.yes(e.index,i):o.btn1?o.btn1(e.index,i):r.close(e.index);else{var a=o["btn"+(t+1)]&&o["btn"+(t+1)](e.index,i);a===!1||r.close(e.index)}}),"number"==typeof o.focusBtn){i.find("."+l[6]).children("a").each(function(){var t=n(this),e=n("<button type='button' />").addClass("layui-layer-confirm");t.css("position","relative").attr("tabindex",-1).append(e),e.click(function(){return t.trigger("click"),!1})});var s=i.find("."+l[6]).find("button.layui-layer-confirm").eq(o.focusBtn);s.length>0&&s.focus()}i.find("."+l[7]).on("click",t),o.shadeClose&&e.shadeo.on("click",function(){r.close(e.index)}),i.find(".layui-layer-min").on("click",function(){var t=o.min&&o.min(i,e.index);t===!1||r.min(e.index,o)}),i.find(".layui-layer-max").on("click",function(){n(this).hasClass("layui-layer-maxmin")?(r.restore(e.index),o.restore&&o.restore(i,e.index)):(r.full(e.index,o),setTimeout(function(){o.full&&o.full(i,e.index)},100))}),o.end&&(a.end[e.index]=o.end)},a.reselect=function(){n.each(n("select"),function(t,e){var i=n(this);i.parents("."+l[0])[0]||1==i.attr("layer")&&n("."+l[0]).length<1&&i.removeAttr("layer").show(),i=null})},s.pt.IE6=function(t){n("select").each(function(t,e){var i=n(this);i.parents("."+l[0])[0]||"none"===i.css("display")||i.attr({layer:"1"}).hide(),i=null})},s.pt.openLayer=function(){var t=this;r.zIndex=t.config.zIndex,r.setTop=function(t){var e=function(){r.zIndex++,t.css("z-index",r.zIndex+1)};return r.zIndex=parseInt(t[0].style.zIndex),t.on("mousedown",e),r.zIndex}},a.record=function(t){var e=[t.width(),t.height(),t.position().top,t.position().left+parseFloat(t.css("margin-left"))];t.find(".layui-layer-max").addClass("layui-layer-maxmin"),t.attr({area:e})},a.rescollbar=function(t){l.html.attr("layer-full")==t&&(l.html[0].style.removeProperty?l.html[0].style.removeProperty("overflow"):l.html[0].style.removeAttribute("overflow"),l.html.removeAttr("layer-full"))},t.layer=r,r.getChildFrame=function(t,e){return e=e||n("."+l[4]).attr("times"),n("#"+l[0]+e).find("iframe").contents().find(t)},r.getFrameIndex=function(t){return n("#"+t).parents("."+l[4]).attr("times")},r.iframeAuto=function(t){if(t){var e=r.getChildFrame("html",t).outerHeight(),i=n("#"+l[0]+t),o=i.find(l[1]).outerHeight()||0,a=i.find("."+l[6]).outerHeight()||0;i.css({height:e+o+a}),i.find("iframe").css({height:e})}},r.iframeSrc=function(t,e){n("#"+l[0]+t).find("iframe").attr("src",e)},r.style=function(t,e,i){var o=n("#"+l[0]+t),r=o.find(".layui-layer-content"),s=o.attr("type"),c=o.find(l[1]).outerHeight()||0,d=o.find("."+l[6]).outerHeight()||0;if(o.attr("minLeft"),s!==a.type[3]&&s!==a.type[4])if(i||(parseFloat(e.width)<=260&&(e.width=260),parseFloat(e.height)-c-d<=64&&(e.height=64+c+d)),o.css(e),d=o.find("."+l[6]).outerHeight()||0,s===a.type[2])o.find("iframe").css({height:parseFloat(e.height)-c-d});else{var u="border-box"==r.css("box-sizing");r.css({height:parseFloat(e.height)-c-d-parseFloat(u?0:r.css("padding-top"))-parseFloat(u?0:r.css("padding-bottom"))})}},r.min=function(t,e){e=e||{};var o=n("#"+l[0]+t),s=n("#"+l.SHADE+t),c=o.find(l[1]).outerHeight()||0,d=o.attr("minLeft")||181*a.minIndex+"px",u=o.css("position"),p={width:180,height:c,position:"fixed",overflow:"hidden"};a.record(o),a.minLeft[0]&&(d=a.minLeft[0],a.minLeft.shift()),e.minStack&&(p.left=d,p.top=i.height()-c,o.attr("minLeft")||a.minIndex++,o.attr("minLeft",d)),o.attr("position",u),r.style(t,p,!0),o.find(".layui-layer-min").hide(),"page"===o.attr("type")&&o.find(l[4]).hide(),a.rescollbar(t),s.hide()},r.restore=function(t){var e=n("#"+l[0]+t),i=n("#"+l.SHADE+t),o=e.attr("area").split(",");e.attr("type"),r.style(t,{width:parseFloat(o[0]),height:parseFloat(o[1]),top:parseFloat(o[2]),left:parseFloat(o[3]),position:e.attr("position"),overflow:"visible"},!0),e.find(".layui-layer-max").removeClass("layui-layer-maxmin"),e.find(".layui-layer-min").show(),"page"===e.attr("type")&&e.find(l[4]).show(),a.rescollbar(t),i.show()},r.full=function(t){var e,o=n("#"+l[0]+t);a.record(o),l.html.attr("layer-full")||l.html.css("overflow","hidden").attr("layer-full",t),clearTimeout(e),e=setTimeout(function(){var e="fixed"===o.css("position");r.style(t,{top:e?0:i.scrollTop(),left:e?0:i.scrollLeft(),width:i.width(),height:i.height()},!0),o.find(".layui-layer-min").hide()},100)},r.title=function(t,e){var i=n("#"+l[0]+(e||r.index)).find(l[1]);i.html(t)},r.close=function(t,e){var i=n("#"+l[0]+t),o=i.attr("type"),s="layer-anim-close";if(i[0]){var c="layui-layer-wrap",d=function(){if(o===a.type[1]&&"object"===i.attr("conType")){i.children(":not(."+l[5]+")").remove();for(var r=i.find("."+c),s=0;s<2;s++)r.unwrap();r.css("display",r.data("display")).removeClass(c)}else{if(o===a.type[2])try{var d=n("#"+l[4]+t)[0];d.contentWindow.document.write(""),d.contentWindow.close(),i.find("."+l[5])[0].removeChild(d)}catch(t){}i[0].innerHTML="",i.remove()}"function"==typeof a.end[t]&&a.end[t](),delete a.end[t],"function"==typeof e&&e()};i.data("isOutAnim")&&i.addClass("layer-anim "+s),n("#layui-layer-moves, #"+l.SHADE+t).remove(),6==r.ie&&a.reselect(),a.rescollbar(t),i.attr("minLeft")&&(a.minIndex--,a.minLeft.push(i.attr("minLeft"))),r.ie&&r.ie<10||!i.data("isOutAnim")?d():setTimeout(function(){d()},200)}},r.closeAll=function(t,e){"function"==typeof t&&(e=t,t=null);var i=n("."+l[0]);n.each(i,function(o){var a=n(this),s=t?a.attr("type")===t:1;s&&r.close(a.attr("times"),o===i.length-1?e:null),s=null}),0===i.length&&"function"==typeof e&&e()};var c=r.cache||{},d=function(t){return c.skin?" "+c.skin+" "+c.skin+"-"+t:""};r.prompt=function(t,e){var o="";if(t=t||{},"function"==typeof t&&(e=t),t.area){var a=t.area;o='style="width: '+a[0]+"; height: "+a[1]+';"',delete t.area}var s,l=2==t.formType?'<textarea class="layui-layer-input"'+o+"></textarea>":function(){return'<input type="'+(1==t.formType?"password":"text")+'" class="layui-layer-input">'}(),c=t.success;return delete t.success,r.open(n.extend({type:1,btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],content:l,skin:"layui-layer-prompt"+d("prompt"),maxWidth:i.width(),success:function(e){s=e.find(".layui-layer-input"),s.val(t.value||"").focus(),"function"==typeof c&&c(e)},resize:!1,yes:function(n){var i=s.val();""===i?s.focus():i.length>(t.maxlength||500)?r.tips("&#x6700;&#x591A;&#x8F93;&#x5165;"+(t.maxlength||500)+"&#x4E2A;&#x5B57;&#x6570;",s,{tips:1}):e&&e(i,n,s)}},t))},r.tab=function(t){t=t||{};var e=t.tab||{},i="layui-this",o=t.success;return delete t.success,r.open(n.extend({type:1,skin:"layui-layer-tab"+d("tab"),resize:!1,title:function(){var t=e.length,n=1,o="";if(t>0)for(o='<span class="'+i+'">'+e[0].title+"</span>";n<t;n++)o+="<span>"+e[n].title+"</span>";return o}(),content:'<ul class="layui-layer-tabmain">'+function(){var t=e.length,n=1,o="";if(t>0)for(o='<li class="layui-layer-tabli '+i+'">'+(e[0].content||"no content")+"</li>";n<t;n++)o+='<li class="layui-layer-tabli">'+(e[n].content||"no  content")+"</li>";return o}()+"</ul>",success:function(e){var a=e.find(".layui-layer-title").children(),r=e.find(".layui-layer-tabmain").children();a.on("mousedown",function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0;var o=n(this),a=o.index();o.addClass(i).siblings().removeClass(i),r.eq(a).show().siblings().hide(),"function"==typeof t.change&&t.change(a)}),"function"==typeof o&&o(e)}},t))},r.photos=function(e,i,o){function a(t,e,n){var i=new Image;return i.src=t,i.complete?e(i):(i.onload=function(){i.onload=null,e(i)},void(i.onerror=function(t){i.onerror=null,n(t)}))}var s={};if(e=e||{},e.photos){e.zoom="undefined"==typeof e.zoom||!!e.zoom;var l=!("string"==typeof e.photos||e.photos instanceof n),c=l?e.photos:{},u=c.data||[],p=c.start||0;s.imgIndex=(0|p)+1,e.img=e.img||"img";var h=e.success;if(delete e.success,l){if(0===u.length)return r.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")}else{var f=n(e.photos),m=function(){u=[],f.find(e.img).each(function(t){var e=n(this);e.attr("layer-index",t),u.push({alt:e.attr("alt"),pid:e.attr("layer-pid"),src:e.attr("layer-src")||e.attr("src"),thumb:e.attr("src")})})};if(m(),0===u.length)return;if(i||f.on("click",e.img,function(){m();var t=n(this),i=t.attr("layer-index");r.photos(n.extend(e,{photos:{start:i,data:u,tab:e.tab},full:e.full}),!0)}),!i)return}s.imgprev=function(t){s.imgIndex--,s.imgIndex<1&&(s.imgIndex=u.length),s.tabimg(t)},s.imgnext=function(t,e){s.imgIndex++,s.imgIndex>u.length&&(s.imgIndex=1,e)||s.tabimg(t)},s.keyup=function(t){if(!s.end){var e=t.keyCode;t.preventDefault(),37===e?s.imgprev(!0):39===e?s.imgnext(!0):27===e&&r.close(s.index)}},s.tabimg=function(t){if(!(u.length<=1))return c.start=s.imgIndex-1,r.close(s.index),r.photos(e,!0,t)},s.event=function(){s.bigimg.find(".layui-layer-imgprev").on("click",function(t){t.preventDefault(),s.imgprev(!0)}),s.bigimg.find(".layui-layer-imgnext").on("click",function(t){t.preventDefault(),s.imgnext(!0)}),n(document).on("keyup",s.keyup),e.zoom&&s.bigimg.on("wheel mousewheel",n(">img",s.bigimg),function(t){var e=n(this).offset(),i=t.originalEvent.wheelDelta>0,o=i?"+=":"-=",a=24,r=Math.floor(a/2);return!(!i&&(n(this).width()<50||n(this).height()<50)||(n(this).width(o+a).height(o+a).offset({left:i?e.left-r:e.left+r,top:i?e.top-r:e.top+r}),1))})},s.loadi=r.load(1,{shade:!("shade"in e)&&.9,scrollbar:!1}),a(u[p].src,function(i){r.close(s.loadi),o&&(e.anim=-1),s.index=r.open(n.extend({type:1,id:"layui-layer-photos",area:function(){var o=[i.width,i.height],a=[n(t).width()-100,n(t).height()-100];if(!e.full&&(o[0]>a[0]||o[1]>a[1])){var r=[o[0]/a[0],o[1]/a[1]];r[0]>r[1]?(o[0]=o[0]/r[0],o[1]=o[1]/r[0]):r[0]<r[1]&&(o[0]=o[0]/r[1],o[1]=o[1]/r[1])}return[o[0]+"px",o[1]+"px"]}(),title:!1,shade:.9,shadeClose:!0,closeBtn:!1,move:".layui-layer-phimg img",moveType:1,scrollbar:!1,moveOut:!0,anim:5,isOutAnim:!1,skin:"layui-layer-photos"+d("photos"),content:'<div class="layui-layer-phimg"><img src="'+u[p].src+'" alt="'+(u[p].alt||"")+'" layer-pid="'+u[p].pid+'">'+function(){return u.length>1?'<div class="layui-layer-imgsee"><span class="layui-layer-imguide"><a href="javascript:;" class="layui-layer-iconext layui-layer-imgprev"></a><a href="javascript:;" class="layui-layer-iconext layui-layer-imgnext"></a></span><div class="layui-layer-imgbar" style="display:'+(o?"block":"")+'"><span class="layui-layer-imgtit"><a href="javascript:;">'+(u[p].alt||"")+"</a><em>"+s.imgIndex+" / "+u.length+"</em></span></div></div>":""}()+"</div>",success:function(t,n){s.bigimg=t.find(".layui-layer-phimg"),s.imgsee=t.find(".layui-layer-imgbar"),s.event(t),e.tab&&e.tab(u[p],t),"function"==typeof h&&h(t)},end:function(){s.end=!0,n(document).off("keyup",s.keyup)}},e))},function(){r.close(s.loadi),r.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;",{time:3e4,btn:["&#x4E0B;&#x4E00;&#x5F20;","&#x4E0D;&#x770B;&#x4E86;"],yes:function(){u.length>1&&s.imgnext(!0,!0)}})})}},a.run=function(e){n=e,i=n(t),l.html=n("html"),r.open=function(t){var e=new s(t);return e.index}},t.layui&&layui.define?(r.ready(),layui.define("jquery",function(e){r.path=layui.cache.dir,a.run(layui.$),t.layer=r,e("layer",r)})):"function"==typeof define&&define.amd?define("layer",["jquery"],function(){return a.run(t.jQuery),r}):function(){r.ready(),a.run(t.jQuery)}()}(window),function(t){t("toastr",["jquery"],function(t){return function(){function e(t,e,n){return f({type:w.error,iconClass:m().iconClasses.error,message:t,optionsOverride:n,title:e})}function n(e,n){return e||(e=m()),v=t("#"+e.containerId),v.length?v:(n&&(v=u(e)),v)}function i(t,e,n){return f({type:w.info,iconClass:m().iconClasses.info,message:t,optionsOverride:n,title:e})}function o(t){y=t}function a(t,e,n){return f({type:w.success,iconClass:m().iconClasses.success,message:t,optionsOverride:n,title:e})}function r(t,e,n){return f({type:w.warning,iconClass:m().iconClasses.warning,message:t,optionsOverride:n,title:e})}function s(t,e){var i=m();v||n(i),d(t,i,e)||c(i)}function l(e){var i=m();return v||n(i),e&&0===t(":focus",e).length?void g(e):void(v.children().length&&v.remove())}function c(e){for(var n=v.children(),i=n.length-1;i>=0;i--)d(t(n[i]),e)}function d(e,n,i){var o=!(!i||!i.force)&&i.force;return!(!e||!o&&0!==t(":focus",e).length)&&(e[n.hideMethod]({duration:n.hideDuration,easing:n.hideEasing,complete:function(){g(e)}}),!0)}function u(e){return v=t("<div/>").attr("id",e.containerId).addClass(e.positionClass),v.appendTo(t(e.target)),v}function p(){return{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1}}function h(t){y&&y(t)}function f(e){function i(t){return null==t&&(t=""),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function o(){l(),d(),u(),p(),f(),y(),c(),a()}function a(){var t="";switch(e.iconClass){case"toast-success":case"toast-info":t="polite";break;default:t="assertive"}E.attr("aria-live",t)}function r(){T.closeOnHover&&E.hover(C,k),!T.onclick&&T.tapToDismiss&&E.click(_),T.closeButton&&N&&N.click(function(t){t.stopPropagation?t.stopPropagation():void 0!==t.cancelBubble&&t.cancelBubble!==!0&&(t.cancelBubble=!0),T.onCloseClick&&T.onCloseClick(t),_(!0)}),T.onclick&&E.click(function(t){T.onclick(t),_()})}function s(){E.hide(),E[T.showMethod]({duration:T.showDuration,easing:T.showEasing,complete:T.onShown}),T.timeOut>0&&(D=setTimeout(_,T.timeOut),L.maxHideTime=parseFloat(T.timeOut),L.hideEta=(new Date).getTime()+L.maxHideTime,T.progressBar&&(L.intervalId=setInterval(S,10)))}function l(){e.iconClass&&E.addClass(T.toastClass).addClass($)}function c(){T.newestOnTop?v.prepend(E):v.append(E)}function d(){if(e.title){var t=e.title;T.escapeHtml&&(t=i(e.title)),F.append(t).addClass(T.titleClass),E.append(F)}}function u(){if(e.message){var t=e.message;T.escapeHtml&&(t=i(e.message)),A.append(t).addClass(T.messageClass),E.append(A)}}function p(){T.closeButton&&(N.addClass(T.closeClass).attr("role","button"),E.prepend(N))}function f(){T.progressBar&&(O.addClass(T.progressClass),E.prepend(O))}function y(){T.rtl&&E.addClass("rtl")}function w(t,e){if(t.preventDuplicates){if(e.message===b)return!0;b=e.message}return!1}function _(e){var n=e&&T.closeMethod!==!1?T.closeMethod:T.hideMethod,i=e&&T.closeDuration!==!1?T.closeDuration:T.hideDuration,o=e&&T.closeEasing!==!1?T.closeEasing:T.hideEasing;if(!t(":focus",E).length||e)return clearTimeout(L.intervalId),E[n]({duration:i,easing:o,complete:function(){g(E),clearTimeout(D),T.onHidden&&"hidden"!==R.state&&T.onHidden(),
R.state="hidden",R.endTime=new Date,h(R)}})}function k(){(T.timeOut>0||T.extendedTimeOut>0)&&(D=setTimeout(_,T.extendedTimeOut),L.maxHideTime=parseFloat(T.extendedTimeOut),L.hideEta=(new Date).getTime()+L.maxHideTime)}function C(){clearTimeout(D),L.hideEta=0,E.stop(!0,!0)[T.showMethod]({duration:T.showDuration,easing:T.showEasing})}function S(){var t=(L.hideEta-(new Date).getTime())/L.maxHideTime*100;O.width(t+"%")}var T=m(),$=e.iconClass||T.iconClass;if("undefined"!=typeof e.optionsOverride&&(T=t.extend(T,e.optionsOverride),$=e.optionsOverride.iconClass||$),!w(T,e)){x++,v=n(T,!0);var D=null,E=t("<div/>"),F=t("<div/>"),A=t("<div/>"),O=t("<div/>"),N=t(T.closeHtml),L={intervalId:null,hideEta:null,maxHideTime:null},R={toastId:x,state:"visible",startTime:new Date,options:T,map:e};return o(),s(),r(),h(R),T.debug&&console&&console.log(R),E}}function m(){return t.extend({},p(),_.options)}function g(t){v||(v=n()),t.is(":visible")||(t.remove(),t=null,0===v.children().length&&(v.remove(),b=void 0))}var v,y,b,x=0,w={error:"error",info:"info",success:"success",warning:"warning"},_={clear:s,remove:l,error:e,getContainer:n,info:i,options:{},subscribe:o,success:a,version:"2.1.3",warning:r};return _}()})}("function"==typeof define&&define.amd?define:function(t,e){"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):window.toastr=e(window.jQuery)}),define("fast",["jquery","bootstrap","toastr","layer","lang"],function(t,e,n,i,o){var a={config:{toastr:{closeButton:!0,debug:!1,newestOnTop:!1,progressBar:!1,positionClass:"toast-top-center",preventDuplicates:!1,onclick:null,showDuration:"300",hideDuration:"1000",timeOut:"5000",extendedTimeOut:"1000",showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"}},events:{onAjaxSuccess:function(t,e){var i="undefined"!=typeof t.data?t.data:null,o="undefined"!=typeof t.msg&&t.msg?t.msg:__("Operation completed");if("function"==typeof e){var a=e.call(this,i,t);if(a===!1)return}n.success(o)},onAjaxError:function(t,e){var i="undefined"!=typeof t.data?t.data:null;if("function"==typeof e){var o=e.call(this,i,t);if(o===!1)return}n.error(t.msg)},onAjaxResponse:function(e){try{var n="object"==typeof e?e:JSON.parse(e);n.hasOwnProperty("code")||t.extend(n,{code:-2,msg:e,data:null})}catch(t){var n={code:-1,msg:t.message,data:null}}return n}},api:{ajax:function(e,n,o){e="string"==typeof e?{url:e}:e;var r;return("undefined"==typeof e.loading||e.loading)&&(r=i.load(e.loading||0)),e=t.extend({type:"POST",dataType:"json",xhrFields:{withCredentials:!0},success:function(t){r&&i.close(r),t=a.events.onAjaxResponse(t),1===t.code?a.events.onAjaxSuccess(t,n):a.events.onAjaxError(t,o)},error:function(t){r&&i.close(r);var e={code:t.status,msg:t.statusText,data:null};a.events.onAjaxError(e,o)}},e),t.ajax(e)},fixurl:function(t){if("/"!==t.substr(0,1)){var e=new RegExp("^(?:[a-z]+:)?//","i");e.test(t)||(t=Config.moduleurl+"/"+t)}else"/addons/"===t.substr(0,8)&&(t=Config.__PUBLIC__.replace(/(\/*$)/g,"")+t);return t},cdnurl:function(t,e){var n=new RegExp("^((?:[a-z]+:)?\\/\\/|data:image\\/)","i"),i=Config.upload.cdnurl;return"undefined"!=typeof e&&e!==!0&&0!==i.indexOf("/")||(t=n.test(t)||i&&0===t.indexOf(i)?t:i+t),e&&!n.test(t)&&(e="string"==typeof e?e:location.origin,t=e+t),t},query:function(t,e){if(e||(e=window.location.href),!t)return"";t=t.replace(/[\[\]]/g,"\\$&");var n=new RegExp("[?&/]"+t+"([=/]([^&#/?]*)|&|#|$)"),i=n.exec(e);return i?i[2]?decodeURIComponent(i[2].replace(/\+/g," ")):"":null},open:function(n,o,r){o=r&&r.title?r.title:o?o:"",n=a.api.fixurl(n),n=n+(n.indexOf("?")>-1?"&":"?")+"dialog=1";var s=a.config.openArea!=e?a.config.openArea:[t(window).width()>800?"800px":"95%",t(window).height()>600?"600px":"95%"],l=r&&"function"==typeof r.success?r.success:t.noop;return r&&"function"==typeof r.success&&delete r.success,r=t.extend({type:2,title:o,shadeClose:!0,shade:!1,maxmin:!0,moveOut:!0,area:s,content:n,zIndex:i.zIndex,success:function(e,n){var o=this;t(e).data("callback",o.callback),i.setTop(e);try{var r=i.getChildFrame("html",n),s=r.find(".layer-footer");if(a.api.layerfooter(e,n,o),s.length>0){var c=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;if(c){var d=s[0],u=new c(function(t){a.api.layerfooter(e,n,o),t.forEach(function(t){})}),p={attributes:!0,childList:!0,characterData:!0,subtree:!0};u.observe(d,p)}}}catch(t){}t(e).height()>t(window).height()&&i.style(n,{top:0,height:t(window).height()}),l.call(this,e,n)}},r?r:{}),(t(window).width()<480||/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&top.$(".tab-pane.active").length>0)&&(top.$(".tab-pane.active").length>0?(r.area=[top.$(".tab-pane.active").width()+"px",top.$(".tab-pane.active").height()+"px"],r.offset=[top.$(".tab-pane.active").scrollTop()+"px","0px"]):(r.area=[t(window).width()+"px",t(window).height()+"px"],r.offset=["0px","0px"])),i.open(r)},close:function(t){var n=parent.Layer.getFrameIndex(window.name),i=parent.$("#layui-layer"+n).data("callback");parent.Layer.close(n),"function"==typeof i&&i.call(e,t)},layerfooter:function(e,n,o){var a=i.getChildFrame("html",n),r=a.find(".layer-footer");if(r.length>0){t(".layui-layer-footer",e).remove();var s=t("<div />").addClass("layui-layer-btn layui-layer-footer");s.html(r.html()),0===t(".row",s).length&&t(">",s).wrapAll("<div class='row'></div>"),s.insertAfter(e.find(".layui-layer-content")),s.on("click",".btn",function(){if(!t(this).hasClass("disabled")&&!t(this).parent().hasClass("disabled")){var e=s.find(".btn").index(this);t(".btn:eq("+e+")",r).trigger("click")}});var l=e.find(".layui-layer-title").outerHeight()||0,c=e.find(".layui-layer-btn").outerHeight()||0;t("iframe",e).height(e.height()-l-c)}if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var l=e.find(".layui-layer-title").outerHeight()||0,c=e.find(".layui-layer-btn").outerHeight()||0;t("iframe",e).parent().css("height",e.height()-l-c),t("iframe",e).css("height","100%")}},success:function(e,n){var o="function"==typeof e;return o&&(n=e),i.msg(__("Operation completed"),t.extend({offset:0,icon:1},o?{}:e),n)},error:function(e,n){var o="function"==typeof e;return o&&(n=e),i.msg(__("Operation failed"),t.extend({offset:0,icon:2},o?{}:e),n)},msg:function(t,e){var n="function"==typeof e?e:function(){"undefined"!=typeof e&&e&&(location.href=e)};i.msg(t,{time:2e3},n)},escape:function(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t},toastr:n,layer:i},lang:function(){var t=arguments,e=t[0],n=1;if(e=e.toLowerCase(),"undefined"!=typeof o&&"undefined"!=typeof o[e]){if("object"==typeof o[e])return o[e];e=o[e]}else if(e.indexOf(".")!==-1,1)e=t[0];else{for(var i=e.split("."),a=o[i[0]],n=1;n<i.length&&(a="undefined"!=typeof a[i[n]]?a[i[n]]:"","object"==typeof a);n++);if("object"==typeof a)return a;e=a}return e.replace(/%((%)|s|d)/g,function(e){var i=null;if(e[2])i=e[2];else{switch(i=t[n],e){case"%d":i=parseFloat(i),isNaN(i)&&(i=0)}n++}return i})},init:function(){t.fn.extend({size:function(){return t(this).length}}),t.ajaxSetup({beforeSend:function(t,e){e.url=a.api.fixurl(e.url)}}),i.config({skin:"layui-layer-fast"}),t(window).keyup(function(e){if(27==e.keyCode&&t(".layui-layer").length>0){var n=0;t(".layui-layer").each(function(){n=Math.max(n,parseInt(t(this).attr("times")))}),n&&i.close(n)}}),n.options=a.config.toastr}};return window.Layer=i,window.Toastr=n,window.__=a.lang,window.Fast=a,a.init(),a}),!function(){function t(t){return t.replace(b,"").replace(x,",").replace(w,"").replace(_,"").replace(k,"").split(C)}function e(t){return"'"+t.replace(/('|\\)/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n")+"'"}function n(n,i){function o(t){return p+=t.split(/\n/).length-1,d&&(t=t.replace(/\s+/g," ").replace(/<!--[\w\W]*?-->/g,"")),t&&(t=y[1]+e(t)+y[2]+"\n"),t}function a(e){var n=p;if(c?e=c(e,i):r&&(e=e.replace(/\n/g,function(){return p++,"$line="+p+";"})),0===e.indexOf("=")){var o=u&&!/^=[=#]/.test(e);if(e=e.replace(/^=[=#]?|[\s;]*$/g,""),o){var a=e.replace(/\s*\([^\)]+\)/,"");h[a]||/^(include|print)$/.test(a)||(e="$escape("+e+")")}else e="$string("+e+")";e=y[1]+e+y[2]}return r&&(e="$line="+n+";"+e),v(t(e),function(t){if(t&&!m[t]){var e;e="print"===t?x:"include"===t?w:h[t]?"$utils."+t:f[t]?"$helpers."+t:"$data."+t,_+=t+"="+e+",",m[t]=!0}}),e+"\n"}var r=i.debug,s=i.openTag,l=i.closeTag,c=i.parser,d=i.compress,u=i.escape,p=1,m={$data:1,$filename:1,$utils:1,$helpers:1,$out:1,$line:1},g="".trim,y=g?["$out='';","$out+=",";","$out"]:["$out=[];","$out.push(",");","$out.join('')"],b=g?"$out+=text;return $out;":"$out.push(text);",x="function(){var text=''.concat.apply('',arguments);"+b+"}",w="function(filename,data){data=data||$data;var text=$utils.$include(filename,data,$filename);"+b+"}",_="'use strict';var $utils=this,$helpers=$utils.$helpers,"+(r?"$line=0,":""),k=y[0],C="return new String("+y[3]+");";v(n.split(s),function(t){t=t.split(l);var e=t[0],n=t[1];1===t.length?k+=o(e):(k+=a(e),n&&(k+=o(n)))});var S=_+k+C;r&&(S="try{"+S+"}catch(e){throw {filename:$filename,name:'Render Error',message:e.message,line:$line,source:"+e(n)+".split(/\\n/)[$line-1].replace(/^\\s+/,'')};}");try{var T=new Function("$data","$filename",S);return T.prototype=h,T}catch(t){throw t.temp="function anonymous($data,$filename) {"+S+"}",t}}var i=function(t,e){return"string"==typeof e?g(e,{filename:t}):r(t,e)};i.version="3.0.0",i.config=function(t,e){o[t]=e};var o=i.defaults={openTag:"<%",closeTag:"%>",escape:!0,cache:!0,compress:!1,parser:null},a=i.cache={};i.render=function(t,e){return g(t)(e)};var r=i.renderFile=function(t,e){var n=i.get(t)||m({filename:t,name:"Render Error",message:"Template not found"});return e?n(e):n};i.get=function(t){var e;if(a[t])e=a[t];else if("object"==typeof document){var n=document.getElementById(t);if(n){var i=(n.value||n.innerHTML).replace(/^\s*|\s*$/g,"");e=g(i,{filename:t})}}return e};var s=function(t,e){return"string"!=typeof t&&(e=typeof t,"number"===e?t+="":t="function"===e?s(t.call(t)):""),t},l={"<":"&#60;",">":"&#62;",'"':"&#34;","'":"&#39;","&":"&#38;"},c=function(t){return l[t]},d=function(t){return s(t).replace(/&(?![\w#]+;)|[<>"']/g,c)},u=Array.isArray||function(t){return"[object Array]"==={}.toString.call(t)},p=function(t,e){var n,i;if(u(t))for(n=0,i=t.length;n<i;n++)e.call(t,t[n],n,t);else for(n in t)e.call(t,t[n],n)},h=i.utils={$helpers:{},$include:r,$string:s,$escape:d,$each:p};i.helper=function(t,e){f[t]=e};var f=i.helpers=h.$helpers;i.onerror=function(t){var e="Template Error\n\n";for(var n in t)e+="<"+n+">\n"+t[n]+"\n\n";"object"==typeof console&&console.error(e)};var m=function(t){return i.onerror(t),function(){return"{Template Error}"}},g=i.compile=function(t,e){function i(n){try{return new l(n,s)+""}catch(i){return e.debug?m(i)():(e.debug=!0,g(t,e)(n))}}e=e||{};for(var r in o)void 0===e[r]&&(e[r]=o[r]);var s=e.filename;try{var l=n(t,e)}catch(t){return t.filename=s||"anonymous",t.name="Syntax Error",m(t)}return i.prototype=l.prototype,i.toString=function(){return l.toString()},s&&e.cache&&(a[s]=i),i},v=h.$each,y="break,case,catch,continue,debugger,default,delete,do,else,false,finally,for,function,if,in,instanceof,new,null,return,switch,this,throw,true,try,typeof,var,void,while,with,abstract,boolean,byte,char,class,const,double,enum,export,extends,final,float,goto,implements,import,int,interface,long,native,package,private,protected,public,short,static,super,synchronized,throws,transient,volatile,arguments,let,yield,undefined",b=/\/\*[\w\W]*?\*\/|\/\/[^\n]*\n|\/\/[^\n]*$|"(?:[^"\\]|\\[\w\W])*"|'(?:[^'\\]|\\[\w\W])*'|\s*\.\s*[$\w\.]+/g,x=/[^\w$]+/g,w=new RegExp(["\\b"+y.replace(/,/g,"\\b|\\b")+"\\b"].join("|"),"g"),_=/^\d[^,]*|,\d[^,]*/g,k=/^,+|,+$/g,C=/^$|,+/;"object"==typeof exports&&"undefined"!=typeof module?module.exports=i:"function"==typeof define?define("template",[],function(){return i}):this.template=i}(),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("moment/moment",e):t.moment=e()}(this,function(){"use strict";function t(){return eo.apply(null,arguments)}function e(t){eo=t}function n(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function i(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function o(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function a(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;var e;for(e in t)if(o(t,e))return!1;return!0}function r(t){return void 0===t}function s(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function l(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function c(t,e){var n,i=[],o=t.length;for(n=0;n<o;++n)i.push(e(t[n],n));return i}function d(t,e){for(var n in e)o(e,n)&&(t[n]=e[n]);return o(e,"toString")&&(t.toString=e.toString),o(e,"valueOf")&&(t.valueOf=e.valueOf),t}function u(t,e,n,i){return Te(t,e,n,i,!0).utc()}function p(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function h(t){return null==t._pf&&(t._pf=p()),t._pf}function f(t){if(null==t._isValid){var e=h(t),n=no.call(e.parsedDateParts,function(t){return null!=t}),i=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidEra&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&n);if(t._strict&&(i=i&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return i;t._isValid=i}return t._isValid}function m(t){var e=u(NaN);return null!=t?d(h(e),t):h(e).userInvalidated=!0,e}function g(t,e){var n,i,o,a=io.length;if(r(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),r(e._i)||(t._i=e._i),r(e._f)||(t._f=e._f),r(e._l)||(t._l=e._l),r(e._strict)||(t._strict=e._strict),r(e._tzm)||(t._tzm=e._tzm),r(e._isUTC)||(t._isUTC=e._isUTC),r(e._offset)||(t._offset=e._offset),r(e._pf)||(t._pf=h(e)),r(e._locale)||(t._locale=e._locale),a>0)for(n=0;n<a;n++)i=io[n],o=e[i],r(o)||(t[i]=o);return t}function v(e){g(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),oo===!1&&(oo=!0,t.updateOffset(this),oo=!1)}function y(t){return t instanceof v||null!=t&&null!=t._isAMomentObject}function b(e){t.suppressDeprecationWarnings===!1&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function x(e,n){var i=!0;return d(function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),i){var a,r,s,l=[],c=arguments.length;for(r=0;r<c;r++){if(a="","object"==typeof arguments[r]){a+="\n["+r+"] ";for(s in arguments[0])o(arguments[0],s)&&(a+=s+": "+arguments[0][s]+", ");a=a.slice(0,-2)}else a=arguments[r];l.push(a)}b(e+"\nArguments: "+Array.prototype.slice.call(l).join("")+"\n"+(new Error).stack),i=!1}return n.apply(this,arguments)},n)}function w(e,n){null!=t.deprecationHandler&&t.deprecationHandler(e,n),ao[e]||(b(n),ao[e]=!0)}function _(t){return"undefined"!=typeof Function&&t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function k(t){var e,n;for(n in t)o(t,n)&&(e=t[n],_(e)?this[n]=e:this["_"+n]=e);this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function C(t,e){var n,a=d({},t);for(n in e)o(e,n)&&(i(t[n])&&i(e[n])?(a[n]={},d(a[n],t[n]),d(a[n],e[n])):null!=e[n]?a[n]=e[n]:delete a[n]);for(n in t)o(t,n)&&!o(e,n)&&i(t[n])&&(a[n]=d({},a[n]));return a}function S(t){null!=t&&this.set(t)}function T(t,e,n){var i=this._calendar[t]||this._calendar.sameElse;return _(i)?i.call(e,n):i}function $(t,e,n){var i=""+Math.abs(t),o=e-i.length,a=t>=0;return(a?n?"+":"":"-")+Math.pow(10,Math.max(0,o)).toString().substr(1)+i}function D(t,e,n,i){var o=i;"string"==typeof i&&(o=function(){return this[i]()}),t&&(ho[t]=o),e&&(ho[e[0]]=function(){return $(o.apply(this,arguments),e[1],e[2])}),n&&(ho[n]=function(){return this.localeData().ordinal(o.apply(this,arguments),t)})}function E(t){return t.match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"")}function F(t){var e,n,i=t.match(co);for(e=0,n=i.length;e<n;e++)ho[i[e]]?i[e]=ho[i[e]]:i[e]=E(i[e]);return function(e){var o,a="";for(o=0;o<n;o++)a+=_(i[o])?i[o].call(e,t):i[o];return a}}function A(t,e){return t.isValid()?(e=O(e,t.localeData()),po[e]=po[e]||F(e),po[e](t)):t.localeData().invalidDate()}function O(t,e){function n(t){return e.longDateFormat(t)||t}var i=5;for(uo.lastIndex=0;i>=0&&uo.test(t);)t=t.replace(uo,n),uo.lastIndex=0,i-=1;return t}function N(t){var e=this._longDateFormat[t],n=this._longDateFormat[t.toUpperCase()];return e||!n?e:(this._longDateFormat[t]=n.match(co).map(function(t){return"MMMM"===t||"MM"===t||"DD"===t||"dddd"===t?t.slice(1):t}).join(""),this._longDateFormat[t])}function L(){return this._invalidDate}function R(t){return this._ordinal.replace("%d",t)}function M(t,e,n,i){var o=this._relativeTime[n];return _(o)?o(t,e,n,i):o.replace(/%d/i,t)}function P(t,e){var n=this._relativeTime[t>0?"future":"past"];return _(n)?n(e):n.replace(/%s/i,e)}function I(t,e){var n=t.toLowerCase();bo[n]=bo[n+"s"]=bo[e]=t}function j(t){return"string"==typeof t?bo[t]||bo[t.toLowerCase()]:void 0}function H(t){var e,n,i={};for(n in t)o(t,n)&&(e=j(n),e&&(i[e]=t[n]));return i}function z(t,e){xo[t]=e}function Y(t){var e,n=[];for(e in t)o(t,e)&&n.push({unit:e,priority:xo[e]});return n.sort(function(t,e){return t.priority-e.priority}),n}function B(t){return t%4===0&&t%100!==0||t%400===0}function U(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function q(t){var e=+t,n=0;return 0!==e&&isFinite(e)&&(n=U(e)),n}function W(e,n){return function(i){return null!=i?(G(this,e,i),t.updateOffset(this,n),this):V(this,e)}}function V(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function G(t,e,n){t.isValid()&&!isNaN(n)&&("FullYear"===e&&B(t.year())&&1===t.month()&&29===t.date()?(n=q(n),t._d["set"+(t._isUTC?"UTC":"")+e](n,t.month(),at(n,t.month()))):t._d["set"+(t._isUTC?"UTC":"")+e](n))}function X(t){return t=j(t),_(this[t])?this[t]():this}function Q(t,e){if("object"==typeof t){t=H(t);var n,i=Y(t),o=i.length;for(n=0;n<o;n++)this[i[n].unit](t[i[n].unit])}else if(t=j(t),_(this[t]))return this[t](e);return this}function K(t,e,n){so[t]=_(e)?e:function(t,i){return t&&n?n:e}}function Z(t,e){return o(so,t)?so[t](e._strict,e._locale):new RegExp(J(t))}function J(t){return tt(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,n,i,o){return e||n||i||o}))}function tt(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function et(t,e){var n,i,o=e;for("string"==typeof t&&(t=[t]),s(e)&&(o=function(t,n){n[e]=q(t)}),i=t.length,n=0;n<i;n++)jo[t[n]]=o}function nt(t,e){et(t,function(t,n,i,o){i._w=i._w||{},e(t,i._w,i,o)})}function it(t,e,n){null!=e&&o(jo,t)&&jo[t](e,n._a,n,t)}function ot(t,e){return(t%e+e)%e}function at(t,e){if(isNaN(t)||isNaN(e))return NaN;var n=ot(e,12);return t+=(e-n)/12,1===n?B(t)?29:28:31-n%7%2}function rt(t,e){return t?n(this._months)?this._months[t.month()]:this._months[(this._months.isFormat||Ko).test(e)?"format":"standalone"][t.month()]:n(this._months)?this._months:this._months.standalone}function st(t,e){return t?n(this._monthsShort)?this._monthsShort[t.month()]:this._monthsShort[Ko.test(e)?"format":"standalone"][t.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function lt(t,e,n){var i,o,a,r=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],i=0;i<12;++i)a=u([2e3,i]),this._shortMonthsParse[i]=this.monthsShort(a,"").toLocaleLowerCase(),this._longMonthsParse[i]=this.months(a,"").toLocaleLowerCase();return n?"MMM"===e?(o=Io.call(this._shortMonthsParse,r),o!==-1?o:null):(o=Io.call(this._longMonthsParse,r),o!==-1?o:null):"MMM"===e?(o=Io.call(this._shortMonthsParse,r),o!==-1?o:(o=Io.call(this._longMonthsParse,r),o!==-1?o:null)):(o=Io.call(this._longMonthsParse,r),o!==-1?o:(o=Io.call(this._shortMonthsParse,r),o!==-1?o:null))}function ct(t,e,n){var i,o,a;if(this._monthsParseExact)return lt.call(this,t,e,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),i=0;i<12;i++){if(o=u([2e3,i]),n&&!this._longMonthsParse[i]&&(this._longMonthsParse[i]=new RegExp("^"+this.months(o,"").replace(".","")+"$","i"),this._shortMonthsParse[i]=new RegExp("^"+this.monthsShort(o,"").replace(".","")+"$","i")),n||this._monthsParse[i]||(a="^"+this.months(o,"")+"|^"+this.monthsShort(o,""),this._monthsParse[i]=new RegExp(a.replace(".",""),"i")),n&&"MMMM"===e&&this._longMonthsParse[i].test(t))return i;if(n&&"MMM"===e&&this._shortMonthsParse[i].test(t))return i;if(!n&&this._monthsParse[i].test(t))return i}}function dt(t,e){var n;if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=q(e);else if(e=t.localeData().monthsParse(e),!s(e))return t;return n=Math.min(t.date(),at(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,n),t}function ut(e){return null!=e?(dt(this,e),t.updateOffset(this,!0),this):V(this,"Month")}function pt(){return at(this.year(),this.month())}function ht(t){return this._monthsParseExact?(o(this,"_monthsRegex")||mt.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=Zo),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)}function ft(t){return this._monthsParseExact?(o(this,"_monthsRegex")||mt.call(this),t?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=Jo),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)}function mt(){function t(t,e){return e.length-t.length}var e,n,i=[],o=[],a=[];for(e=0;e<12;e++)n=u([2e3,e]),i.push(this.monthsShort(n,"")),o.push(this.months(n,"")),a.push(this.months(n,"")),a.push(this.monthsShort(n,""));for(i.sort(t),o.sort(t),a.sort(t),e=0;e<12;e++)i[e]=tt(i[e]),o[e]=tt(o[e]);for(e=0;e<24;e++)a[e]=tt(a[e]);this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+i.join("|")+")","i")}function gt(t){return B(t)?366:365}function vt(){return B(this.year())}function yt(t,e,n,i,o,a,r){var s;return t<100&&t>=0?(s=new Date(t+400,e,n,i,o,a,r),isFinite(s.getFullYear())&&s.setFullYear(t)):s=new Date(t,e,n,i,o,a,r),s}function bt(t){var e,n;return t<100&&t>=0?(n=Array.prototype.slice.call(arguments),n[0]=t+400,e=new Date(Date.UTC.apply(null,n)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function xt(t,e,n){var i=7+e-n,o=(7+bt(t,0,i).getUTCDay()-e)%7;return-o+i-1}function wt(t,e,n,i,o){var a,r,s=(7+n-i)%7,l=xt(t,i,o),c=1+7*(e-1)+s+l;return c<=0?(a=t-1,r=gt(a)+c):c>gt(t)?(a=t+1,r=c-gt(t)):(a=t,r=c),{year:a,dayOfYear:r}}function _t(t,e,n){var i,o,a=xt(t.year(),e,n),r=Math.floor((t.dayOfYear()-a-1)/7)+1;return r<1?(o=t.year()-1,i=r+kt(o,e,n)):r>kt(t.year(),e,n)?(i=r-kt(t.year(),e,n),o=t.year()+1):(o=t.year(),i=r),{week:i,year:o}}function kt(t,e,n){var i=xt(t,e,n),o=xt(t+1,e,n);return(gt(t)-i+o)/7}function Ct(t){return _t(t,this._week.dow,this._week.doy).week}function St(){return this._week.dow}function Tt(){return this._week.doy}function $t(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")}function Dt(t){var e=_t(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")}function Et(t,e){return"string"!=typeof t?t:isNaN(t)?(t=e.weekdaysParse(t),"number"==typeof t?t:null):parseInt(t,10)}function Ft(t,e){return"string"==typeof t?e.weekdaysParse(t)%7||7:isNaN(t)?null:t}function At(t,e){return t.slice(e,7).concat(t.slice(0,e))}function Ot(t,e){var i=n(this._weekdays)?this._weekdays:this._weekdays[t&&t!==!0&&this._weekdays.isFormat.test(e)?"format":"standalone"];return t===!0?At(i,this._week.dow):t?i[t.day()]:i}function Nt(t){return t===!0?At(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort}function Lt(t){return t===!0?At(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin}function Rt(t,e,n){var i,o,a,r=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],i=0;i<7;++i)a=u([2e3,1]).day(i),this._minWeekdaysParse[i]=this.weekdaysMin(a,"").toLocaleLowerCase(),this._shortWeekdaysParse[i]=this.weekdaysShort(a,"").toLocaleLowerCase(),this._weekdaysParse[i]=this.weekdays(a,"").toLocaleLowerCase();return n?"dddd"===e?(o=Io.call(this._weekdaysParse,r),o!==-1?o:null):"ddd"===e?(o=Io.call(this._shortWeekdaysParse,r),o!==-1?o:null):(o=Io.call(this._minWeekdaysParse,r),o!==-1?o:null):"dddd"===e?(o=Io.call(this._weekdaysParse,r),o!==-1?o:(o=Io.call(this._shortWeekdaysParse,r),o!==-1?o:(o=Io.call(this._minWeekdaysParse,r),o!==-1?o:null))):"ddd"===e?(o=Io.call(this._shortWeekdaysParse,r),o!==-1?o:(o=Io.call(this._weekdaysParse,r),o!==-1?o:(o=Io.call(this._minWeekdaysParse,r),o!==-1?o:null))):(o=Io.call(this._minWeekdaysParse,r),o!==-1?o:(o=Io.call(this._weekdaysParse,r),o!==-1?o:(o=Io.call(this._shortWeekdaysParse,r),o!==-1?o:null)))}function Mt(t,e,n){var i,o,a;if(this._weekdaysParseExact)return Rt.call(this,t,e,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),i=0;i<7;i++){if(o=u([2e3,1]).day(i),n&&!this._fullWeekdaysParse[i]&&(this._fullWeekdaysParse[i]=new RegExp("^"+this.weekdays(o,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[i]=new RegExp("^"+this.weekdaysShort(o,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[i]=new RegExp("^"+this.weekdaysMin(o,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[i]||(a="^"+this.weekdays(o,"")+"|^"+this.weekdaysShort(o,"")+"|^"+this.weekdaysMin(o,""),this._weekdaysParse[i]=new RegExp(a.replace(".",""),"i")),n&&"dddd"===e&&this._fullWeekdaysParse[i].test(t))return i;if(n&&"ddd"===e&&this._shortWeekdaysParse[i].test(t))return i;if(n&&"dd"===e&&this._minWeekdaysParse[i].test(t))return i;if(!n&&this._weekdaysParse[i].test(t))return i}}function Pt(t){if(!this.isValid())return null!=t?this:NaN;var e=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(t=Et(t,this.localeData()),this.add(t-e,"d")):e}function It(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")}function jt(t){if(!this.isValid())return null!=t?this:NaN;if(null!=t){var e=Ft(t,this.localeData());return this.day(this.day()%7?e:e-7)}return this.day()||7}function Ht(t){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Bt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=aa),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)}function zt(t){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Bt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=ra),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function Yt(t){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Bt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=sa),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Bt(){function t(t,e){return e.length-t.length}var e,n,i,o,a,r=[],s=[],l=[],c=[];for(e=0;e<7;e++)n=u([2e3,1]).day(e),i=tt(this.weekdaysMin(n,"")),o=tt(this.weekdaysShort(n,"")),a=tt(this.weekdays(n,"")),r.push(i),s.push(o),l.push(a),c.push(i),c.push(o),c.push(a);r.sort(t),s.sort(t),l.sort(t),c.sort(t),this._weekdaysRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Ut(){return this.hours()%12||12}function qt(){return this.hours()||24}function Wt(t,e){D(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function Vt(t,e){return e._meridiemParse}function Gt(t){return"p"===(t+"").toLowerCase().charAt(0)}function Xt(t,e,n){return t>11?n?"pm":"PM":n?"am":"AM"}function Qt(t,e){var n,i=Math.min(t.length,e.length);for(n=0;n<i;n+=1)if(t[n]!==e[n])return n;return i}function Kt(t){return t?t.toLowerCase().replace("_","-"):t}function Zt(t){for(var e,n,i,o,a=0;a<t.length;){for(o=Kt(t[a]).split("-"),e=o.length,n=Kt(t[a+1]),n=n?n.split("-"):null;e>0;){if(i=te(o.slice(0,e).join("-")))return i;if(n&&n.length>=e&&Qt(o,n)>=e-1)break;e--}a++}return la}function Jt(t){return null!=t.match("^[^/\\\\]*$")}function te(t){var e,n=null;if(void 0===pa[t]&&"undefined"!=typeof module&&module&&module.exports&&Jt(t))try{n=la._abbr,e=require,e("./locale/"+t),ee(n)}catch(e){pa[t]=null}return pa[t]}function ee(t,e){var n;return t&&(n=r(e)?oe(t):ne(t,e),n?la=n:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),la._abbr}function ne(t,e){if(null!==e){var n,i=ua;if(e.abbr=t,null!=pa[t])w("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=pa[t]._config;else if(null!=e.parentLocale)if(null!=pa[e.parentLocale])i=pa[e.parentLocale]._config;else{if(n=te(e.parentLocale),null==n)return ha[e.parentLocale]||(ha[e.parentLocale]=[]),ha[e.parentLocale].push({name:t,config:e}),null;i=n._config}return pa[t]=new S(C(i,e)),ha[t]&&ha[t].forEach(function(t){ne(t.name,t.config)}),ee(t),pa[t]}return delete pa[t],null}function ie(t,e){if(null!=e){var n,i,o=ua;null!=pa[t]&&null!=pa[t].parentLocale?pa[t].set(C(pa[t]._config,e)):(i=te(t),null!=i&&(o=i._config),e=C(o,e),null==i&&(e.abbr=t),n=new S(e),n.parentLocale=pa[t],pa[t]=n),ee(t)}else null!=pa[t]&&(null!=pa[t].parentLocale?(pa[t]=pa[t].parentLocale,t===ee()&&ee(t)):null!=pa[t]&&delete pa[t]);return pa[t]}function oe(t){var e;if(t&&t._locale&&t._locale._abbr&&(t=t._locale._abbr),!t)return la;if(!n(t)){if(e=te(t))return e;t=[t]}return Zt(t)}function ae(){return ro(pa)}function re(t){var e,n=t._a;return n&&h(t).overflow===-2&&(e=n[zo]<0||n[zo]>11?zo:n[Yo]<1||n[Yo]>at(n[Ho],n[zo])?Yo:n[Bo]<0||n[Bo]>24||24===n[Bo]&&(0!==n[Uo]||0!==n[qo]||0!==n[Wo])?Bo:n[Uo]<0||n[Uo]>59?Uo:n[qo]<0||n[qo]>59?qo:n[Wo]<0||n[Wo]>999?Wo:-1,h(t)._overflowDayOfYear&&(e<Ho||e>Yo)&&(e=Yo),h(t)._overflowWeeks&&e===-1&&(e=Vo),h(t)._overflowWeekday&&e===-1&&(e=Go),h(t).overflow=e),t}function se(t){var e,n,i,o,a,r,s=t._i,l=fa.exec(s)||ma.exec(s),c=va.length,d=ya.length;if(l){for(h(t).iso=!0,e=0,n=c;e<n;e++)if(va[e][1].exec(l[1])){o=va[e][0],i=va[e][2]!==!1;break}if(null==o)return void(t._isValid=!1);if(l[3]){for(e=0,n=d;e<n;e++)if(ya[e][1].exec(l[3])){a=(l[2]||" ")+ya[e][0];break}if(null==a)return void(t._isValid=!1)}if(!i&&null!=a)return void(t._isValid=!1);if(l[4]){if(!ga.exec(l[4]))return void(t._isValid=!1);r="Z"}t._f=o+(a||"")+(r||""),be(t)}else t._isValid=!1}function le(t,e,n,i,o,a){var r=[ce(t),Qo.indexOf(e),parseInt(n,10),parseInt(i,10),parseInt(o,10)];return a&&r.push(parseInt(a,10)),r}function ce(t){var e=parseInt(t,10);return e<=49?2e3+e:e<=999?1900+e:e}function de(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function ue(t,e,n){if(t){var i=ia.indexOf(t),o=new Date(e[0],e[1],e[2]).getDay();
if(i!==o)return h(n).weekdayMismatch=!0,n._isValid=!1,!1}return!0}function pe(t,e,n){if(t)return wa[t];if(e)return 0;var i=parseInt(n,10),o=i%100,a=(i-o)/100;return 60*a+o}function he(t){var e,n=xa.exec(de(t._i));if(n){if(e=le(n[4],n[3],n[2],n[5],n[6],n[7]),!ue(n[1],e,t))return;t._a=e,t._tzm=pe(n[8],n[9],n[10]),t._d=bt.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),h(t).rfc2822=!0}else t._isValid=!1}function fe(e){var n=ba.exec(e._i);return null!==n?void(e._d=new Date(+n[1])):(se(e),void(e._isValid===!1&&(delete e._isValid,he(e),e._isValid===!1&&(delete e._isValid,e._strict?e._isValid=!1:t.createFromInputFallback(e)))))}function me(t,e,n){return null!=t?t:null!=e?e:n}function ge(e){var n=new Date(t.now());return e._useUTC?[n.getUTCFullYear(),n.getUTCMonth(),n.getUTCDate()]:[n.getFullYear(),n.getMonth(),n.getDate()]}function ve(t){var e,n,i,o,a,r=[];if(!t._d){for(i=ge(t),t._w&&null==t._a[Yo]&&null==t._a[zo]&&ye(t),null!=t._dayOfYear&&(a=me(t._a[Ho],i[Ho]),(t._dayOfYear>gt(a)||0===t._dayOfYear)&&(h(t)._overflowDayOfYear=!0),n=bt(a,0,t._dayOfYear),t._a[zo]=n.getUTCMonth(),t._a[Yo]=n.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=r[e]=i[e];for(;e<7;e++)t._a[e]=r[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[Bo]&&0===t._a[Uo]&&0===t._a[qo]&&0===t._a[Wo]&&(t._nextDay=!0,t._a[Bo]=0),t._d=(t._useUTC?bt:yt).apply(null,r),o=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[Bo]=24),t._w&&"undefined"!=typeof t._w.d&&t._w.d!==o&&(h(t).weekdayMismatch=!0)}}function ye(t){var e,n,i,o,a,r,s,l,c;e=t._w,null!=e.GG||null!=e.W||null!=e.E?(a=1,r=4,n=me(e.GG,t._a[Ho],_t($e(),1,4).year),i=me(e.W,1),o=me(e.E,1),(o<1||o>7)&&(l=!0)):(a=t._locale._week.dow,r=t._locale._week.doy,c=_t($e(),a,r),n=me(e.gg,t._a[Ho],c.year),i=me(e.w,c.week),null!=e.d?(o=e.d,(o<0||o>6)&&(l=!0)):null!=e.e?(o=e.e+a,(e.e<0||e.e>6)&&(l=!0)):o=a),i<1||i>kt(n,a,r)?h(t)._overflowWeeks=!0:null!=l?h(t)._overflowWeekday=!0:(s=wt(n,i,o,a,r),t._a[Ho]=s.year,t._dayOfYear=s.dayOfYear)}function be(e){if(e._f===t.ISO_8601)return void se(e);if(e._f===t.RFC_2822)return void he(e);e._a=[],h(e).empty=!0;var n,i,o,a,r,s,l,c=""+e._i,d=c.length,u=0;for(o=O(e._f,e._locale).match(co)||[],l=o.length,n=0;n<l;n++)a=o[n],i=(c.match(Z(a,e))||[])[0],i&&(r=c.substr(0,c.indexOf(i)),r.length>0&&h(e).unusedInput.push(r),c=c.slice(c.indexOf(i)+i.length),u+=i.length),ho[a]?(i?h(e).empty=!1:h(e).unusedTokens.push(a),it(a,i,e)):e._strict&&!i&&h(e).unusedTokens.push(a);h(e).charsLeftOver=d-u,c.length>0&&h(e).unusedInput.push(c),e._a[Bo]<=12&&h(e).bigHour===!0&&e._a[Bo]>0&&(h(e).bigHour=void 0),h(e).parsedDateParts=e._a.slice(0),h(e).meridiem=e._meridiem,e._a[Bo]=xe(e._locale,e._a[Bo],e._meridiem),s=h(e).era,null!==s&&(e._a[Ho]=e._locale.erasConvertYear(s,e._a[Ho])),ve(e),re(e)}function xe(t,e,n){var i;return null==n?e:null!=t.meridiemHour?t.meridiemHour(e,n):null!=t.isPM?(i=t.isPM(n),i&&e<12&&(e+=12),i||12!==e||(e=0),e):e}function we(t){var e,n,i,o,a,r,s=!1,l=t._f.length;if(0===l)return h(t).invalidFormat=!0,void(t._d=new Date(NaN));for(o=0;o<l;o++)a=0,r=!1,e=g({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[o],be(e),f(e)&&(r=!0),a+=h(e).charsLeftOver,a+=10*h(e).unusedTokens.length,h(e).score=a,s?a<i&&(i=a,n=e):(null==i||a<i||r)&&(i=a,n=e,r&&(s=!0));d(t,n||e)}function _e(t){if(!t._d){var e=H(t._i),n=void 0===e.day?e.date:e.day;t._a=c([e.year,e.month,n,e.hour,e.minute,e.second,e.millisecond],function(t){return t&&parseInt(t,10)}),ve(t)}}function ke(t){var e=new v(re(Ce(t)));return e._nextDay&&(e.add(1,"d"),e._nextDay=void 0),e}function Ce(t){var e=t._i,i=t._f;return t._locale=t._locale||oe(t._l),null===e||void 0===i&&""===e?m({nullInput:!0}):("string"==typeof e&&(t._i=e=t._locale.preparse(e)),y(e)?new v(re(e)):(l(e)?t._d=e:n(i)?we(t):i?be(t):Se(t),f(t)||(t._d=null),t))}function Se(e){var o=e._i;r(o)?e._d=new Date(t.now()):l(o)?e._d=new Date(o.valueOf()):"string"==typeof o?fe(e):n(o)?(e._a=c(o.slice(0),function(t){return parseInt(t,10)}),ve(e)):i(o)?_e(e):s(o)?e._d=new Date(o):t.createFromInputFallback(e)}function Te(t,e,o,r,s){var l={};return e!==!0&&e!==!1||(r=e,e=void 0),o!==!0&&o!==!1||(r=o,o=void 0),(i(t)&&a(t)||n(t)&&0===t.length)&&(t=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=s,l._l=o,l._i=t,l._f=e,l._strict=r,ke(l)}function $e(t,e,n,i){return Te(t,e,n,i,!1)}function De(t,e){var i,o;if(1===e.length&&n(e[0])&&(e=e[0]),!e.length)return $e();for(i=e[0],o=1;o<e.length;++o)e[o].isValid()&&!e[o][t](i)||(i=e[o]);return i}function Ee(){var t=[].slice.call(arguments,0);return De("isBefore",t)}function Fe(){var t=[].slice.call(arguments,0);return De("isAfter",t)}function Ae(t){var e,n,i=!1,a=Sa.length;for(e in t)if(o(t,e)&&(Io.call(Sa,e)===-1||null!=t[e]&&isNaN(t[e])))return!1;for(n=0;n<a;++n)if(t[Sa[n]]){if(i)return!1;parseFloat(t[Sa[n]])!==q(t[Sa[n]])&&(i=!0)}return!0}function Oe(){return this._isValid}function Ne(){return Je(NaN)}function Le(t){var e=H(t),n=e.year||0,i=e.quarter||0,o=e.month||0,a=e.week||e.isoWeek||0,r=e.day||0,s=e.hour||0,l=e.minute||0,c=e.second||0,d=e.millisecond||0;this._isValid=Ae(e),this._milliseconds=+d+1e3*c+6e4*l+1e3*s*60*60,this._days=+r+7*a,this._months=+o+3*i+12*n,this._data={},this._locale=oe(),this._bubble()}function Re(t){return t instanceof Le}function Me(t){return t<0?Math.round(-1*t)*-1:Math.round(t)}function Pe(t,e,n){var i,o=Math.min(t.length,e.length),a=Math.abs(t.length-e.length),r=0;for(i=0;i<o;i++)(n&&t[i]!==e[i]||!n&&q(t[i])!==q(e[i]))&&r++;return r+a}function Ie(t,e){D(t,0,0,function(){var t=this.utcOffset(),n="+";return t<0&&(t=-t,n="-"),n+$(~~(t/60),2)+e+$(~~t%60,2)})}function je(t,e){var n,i,o,a=(e||"").match(t);return null===a?null:(n=a[a.length-1]||[],i=(n+"").match(Ta)||["-",0,0],o=+(60*i[1])+q(i[2]),0===o?0:"+"===i[0]?o:-o)}function He(e,n){var i,o;return n._isUTC?(i=n.clone(),o=(y(e)||l(e)?e.valueOf():$e(e).valueOf())-i.valueOf(),i._d.setTime(i._d.valueOf()+o),t.updateOffset(i,!1),i):$e(e).local()}function ze(t){return-Math.round(t._d.getTimezoneOffset())}function Ye(e,n,i){var o,a=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(e=je(Ro,e),null===e)return this}else Math.abs(e)<16&&!i&&(e=60*e);return!this._isUTC&&n&&(o=ze(this)),this._offset=e,this._isUTC=!0,null!=o&&this.add(o,"m"),a!==e&&(!n||this._changeInProgress?an(this,Je(e-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?a:ze(this)}function Be(t,e){return null!=t?("string"!=typeof t&&(t=-t),this.utcOffset(t,e),this):-this.utcOffset()}function Ue(t){return this.utcOffset(0,t)}function qe(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(ze(this),"m")),this}function We(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var t=je(Lo,this._i);null!=t?this.utcOffset(t):this.utcOffset(0,!0)}return this}function Ve(t){return!!this.isValid()&&(t=t?$e(t).utcOffset():0,(this.utcOffset()-t)%60===0)}function Ge(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Xe(){if(!r(this._isDSTShifted))return this._isDSTShifted;var t,e={};return g(e,this),e=Ce(e),e._a?(t=e._isUTC?u(e._a):$e(e._a),this._isDSTShifted=this.isValid()&&Pe(e._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function Qe(){return!!this.isValid()&&!this._isUTC}function Ke(){return!!this.isValid()&&this._isUTC}function Ze(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}function Je(t,e){var n,i,a,r=t,l=null;return Re(t)?r={ms:t._milliseconds,d:t._days,M:t._months}:s(t)||!isNaN(+t)?(r={},e?r[e]=+t:r.milliseconds=+t):(l=$a.exec(t))?(n="-"===l[1]?-1:1,r={y:0,d:q(l[Yo])*n,h:q(l[Bo])*n,m:q(l[Uo])*n,s:q(l[qo])*n,ms:q(Me(1e3*l[Wo]))*n}):(l=Da.exec(t))?(n="-"===l[1]?-1:1,r={y:tn(l[2],n),M:tn(l[3],n),w:tn(l[4],n),d:tn(l[5],n),h:tn(l[6],n),m:tn(l[7],n),s:tn(l[8],n)}):null==r?r={}:"object"==typeof r&&("from"in r||"to"in r)&&(a=nn($e(r.from),$e(r.to)),r={},r.ms=a.milliseconds,r.M=a.months),i=new Le(r),Re(t)&&o(t,"_locale")&&(i._locale=t._locale),Re(t)&&o(t,"_isValid")&&(i._isValid=t._isValid),i}function tn(t,e){var n=t&&parseFloat(t.replace(",","."));return(isNaN(n)?0:n)*e}function en(t,e){var n={};return n.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(n.months,"M").isAfter(e)&&--n.months,n.milliseconds=+e-+t.clone().add(n.months,"M"),n}function nn(t,e){var n;return t.isValid()&&e.isValid()?(e=He(e,t),t.isBefore(e)?n=en(t,e):(n=en(e,t),n.milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}function on(t,e){return function(n,i){var o,a;return null===i||isNaN(+i)||(w(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),a=n,n=i,i=a),o=Je(n,i),an(this,o,t),this}}function an(e,n,i,o){var a=n._milliseconds,r=Me(n._days),s=Me(n._months);e.isValid()&&(o=null==o||o,s&&dt(e,V(e,"Month")+s*i),r&&G(e,"Date",V(e,"Date")+r*i),a&&e._d.setTime(e._d.valueOf()+a*i),o&&t.updateOffset(e,r||s))}function rn(t){return"string"==typeof t||t instanceof String}function sn(t){return y(t)||l(t)||rn(t)||s(t)||cn(t)||ln(t)||null===t||void 0===t}function ln(t){var e,n,r=i(t)&&!a(t),s=!1,l=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],c=l.length;for(e=0;e<c;e+=1)n=l[e],s=s||o(t,n);return r&&s}function cn(t){var e=n(t),i=!1;return e&&(i=0===t.filter(function(e){return!s(e)&&rn(t)}).length),e&&i}function dn(t){var e,n,r=i(t)&&!a(t),s=!1,l=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(e=0;e<l.length;e+=1)n=l[e],s=s||o(t,n);return r&&s}function un(t,e){var n=t.diff(e,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"}function pn(e,n){1===arguments.length&&(arguments[0]?sn(arguments[0])?(e=arguments[0],n=void 0):dn(arguments[0])&&(n=arguments[0],e=void 0):(e=void 0,n=void 0));var i=e||$e(),o=He(i,this).startOf("day"),a=t.calendarFormat(this,o)||"sameElse",r=n&&(_(n[a])?n[a].call(this,i):n[a]);return this.format(r||this.localeData().calendar(a,this,$e(i)))}function hn(){return new v(this)}function fn(t,e){var n=y(t)?t:$e(t);return!(!this.isValid()||!n.isValid())&&(e=j(e)||"millisecond","millisecond"===e?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(e).valueOf())}function mn(t,e){var n=y(t)?t:$e(t);return!(!this.isValid()||!n.isValid())&&(e=j(e)||"millisecond","millisecond"===e?this.valueOf()<n.valueOf():this.clone().endOf(e).valueOf()<n.valueOf())}function gn(t,e,n,i){var o=y(t)?t:$e(t),a=y(e)?e:$e(e);return!!(this.isValid()&&o.isValid()&&a.isValid())&&(i=i||"()",("("===i[0]?this.isAfter(o,n):!this.isBefore(o,n))&&(")"===i[1]?this.isBefore(a,n):!this.isAfter(a,n)))}function vn(t,e){var n,i=y(t)?t:$e(t);return!(!this.isValid()||!i.isValid())&&(e=j(e)||"millisecond","millisecond"===e?this.valueOf()===i.valueOf():(n=i.valueOf(),this.clone().startOf(e).valueOf()<=n&&n<=this.clone().endOf(e).valueOf()))}function yn(t,e){return this.isSame(t,e)||this.isAfter(t,e)}function bn(t,e){return this.isSame(t,e)||this.isBefore(t,e)}function xn(t,e,n){var i,o,a;if(!this.isValid())return NaN;if(i=He(t,this),!i.isValid())return NaN;switch(o=6e4*(i.utcOffset()-this.utcOffset()),e=j(e)){case"year":a=wn(this,i)/12;break;case"month":a=wn(this,i);break;case"quarter":a=wn(this,i)/3;break;case"second":a=(this-i)/1e3;break;case"minute":a=(this-i)/6e4;break;case"hour":a=(this-i)/36e5;break;case"day":a=(this-i-o)/864e5;break;case"week":a=(this-i-o)/6048e5;break;default:a=this-i}return n?a:U(a)}function wn(t,e){if(t.date()<e.date())return-wn(e,t);var n,i,o=12*(e.year()-t.year())+(e.month()-t.month()),a=t.clone().add(o,"months");return e-a<0?(n=t.clone().add(o-1,"months"),i=(e-a)/(a-n)):(n=t.clone().add(o+1,"months"),i=(e-a)/(n-a)),-(o+i)||0}function _n(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function kn(t){if(!this.isValid())return null;var e=t!==!0,n=e?this.clone().utc():this;return n.year()<0||n.year()>9999?A(n,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):_(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",A(n,"Z")):A(n,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function Cn(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t,e,n,i,o="moment",a="";return this.isLocal()||(o=0===this.utcOffset()?"moment.utc":"moment.parseZone",a="Z"),t="["+o+'("]',e=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",n="-MM-DD[T]HH:mm:ss.SSS",i=a+'[")]',this.format(t+e+n+i)}function Sn(e){e||(e=this.isUtc()?t.defaultFormatUtc:t.defaultFormat);var n=A(this,e);return this.localeData().postformat(n)}function Tn(t,e){return this.isValid()&&(y(t)&&t.isValid()||$e(t).isValid())?Je({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function $n(t){return this.from($e(),t)}function Dn(t,e){return this.isValid()&&(y(t)&&t.isValid()||$e(t).isValid())?Je({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function En(t){return this.to($e(),t)}function Fn(t){var e;return void 0===t?this._locale._abbr:(e=oe(t),null!=e&&(this._locale=e),this)}function An(){return this._locale}function On(t,e){return(t%e+e)%e}function Nn(t,e,n){return t<100&&t>=0?new Date(t+400,e,n)-Ra:new Date(t,e,n).valueOf()}function Ln(t,e,n){return t<100&&t>=0?Date.UTC(t+400,e,n)-Ra:Date.UTC(t,e,n)}function Rn(e){var n,i;if(e=j(e),void 0===e||"millisecond"===e||!this.isValid())return this;switch(i=this._isUTC?Ln:Nn,e){case"year":n=i(this.year(),0,1);break;case"quarter":n=i(this.year(),this.month()-this.month()%3,1);break;case"month":n=i(this.year(),this.month(),1);break;case"week":n=i(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":n=i(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":n=i(this.year(),this.month(),this.date());break;case"hour":n=this._d.valueOf(),n-=On(n+(this._isUTC?0:this.utcOffset()*Na),La);break;case"minute":n=this._d.valueOf(),n-=On(n,Na);break;case"second":n=this._d.valueOf(),n-=On(n,Oa)}return this._d.setTime(n),t.updateOffset(this,!0),this}function Mn(e){var n,i;if(e=j(e),void 0===e||"millisecond"===e||!this.isValid())return this;switch(i=this._isUTC?Ln:Nn,e){case"year":n=i(this.year()+1,0,1)-1;break;case"quarter":n=i(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":n=i(this.year(),this.month()+1,1)-1;break;case"week":n=i(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":n=i(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":n=i(this.year(),this.month(),this.date()+1)-1;break;case"hour":n=this._d.valueOf(),n+=La-On(n+(this._isUTC?0:this.utcOffset()*Na),La)-1;break;case"minute":n=this._d.valueOf(),n+=Na-On(n,Na)-1;break;case"second":n=this._d.valueOf(),n+=Oa-On(n,Oa)-1}return this._d.setTime(n),t.updateOffset(this,!0),this}function Pn(){return this._d.valueOf()-6e4*(this._offset||0)}function In(){return Math.floor(this.valueOf()/1e3)}function jn(){return new Date(this.valueOf())}function Hn(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]}function zn(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}}function Yn(){return this.isValid()?this.toISOString():null}function Bn(){return f(this)}function Un(){return d({},h(this))}function qn(){return h(this).overflow}function Wn(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function Vn(e,n){var i,o,a,r=this._eras||oe("en")._eras;for(i=0,o=r.length;i<o;++i){switch(typeof r[i].since){case"string":a=t(r[i].since).startOf("day"),r[i].since=a.valueOf()}switch(typeof r[i].until){case"undefined":r[i].until=+(1/0);break;case"string":a=t(r[i].until).startOf("day").valueOf(),r[i].until=a.valueOf()}}return r}function Gn(t,e,n){var i,o,a,r,s,l=this.eras();for(t=t.toUpperCase(),i=0,o=l.length;i<o;++i)if(a=l[i].name.toUpperCase(),r=l[i].abbr.toUpperCase(),s=l[i].narrow.toUpperCase(),n)switch(e){case"N":case"NN":case"NNN":if(r===t)return l[i];break;case"NNNN":if(a===t)return l[i];break;case"NNNNN":if(s===t)return l[i]}else if([a,r,s].indexOf(t)>=0)return l[i]}function Xn(e,n){var i=e.since<=e.until?1:-1;return void 0===n?t(e.since).year():t(e.since).year()+(n-e.offset)*i}function Qn(){var t,e,n,i=this.localeData().eras();for(t=0,e=i.length;t<e;++t){if(n=this.clone().startOf("day").valueOf(),i[t].since<=n&&n<=i[t].until)return i[t].name;if(i[t].until<=n&&n<=i[t].since)return i[t].name}return""}function Kn(){var t,e,n,i=this.localeData().eras();for(t=0,e=i.length;t<e;++t){if(n=this.clone().startOf("day").valueOf(),i[t].since<=n&&n<=i[t].until)return i[t].narrow;if(i[t].until<=n&&n<=i[t].since)return i[t].narrow}return""}function Zn(){var t,e,n,i=this.localeData().eras();for(t=0,e=i.length;t<e;++t){if(n=this.clone().startOf("day").valueOf(),i[t].since<=n&&n<=i[t].until)return i[t].abbr;if(i[t].until<=n&&n<=i[t].since)return i[t].abbr}return""}function Jn(){var e,n,i,o,a=this.localeData().eras();for(e=0,n=a.length;e<n;++e)if(i=a[e].since<=a[e].until?1:-1,o=this.clone().startOf("day").valueOf(),a[e].since<=o&&o<=a[e].until||a[e].until<=o&&o<=a[e].since)return(this.year()-t(a[e].since).year())*i+a[e].offset;return this.year()}function ti(t){return o(this,"_erasNameRegex")||si.call(this),t?this._erasNameRegex:this._erasRegex}function ei(t){return o(this,"_erasAbbrRegex")||si.call(this),t?this._erasAbbrRegex:this._erasRegex}function ni(t){return o(this,"_erasNarrowRegex")||si.call(this),t?this._erasNarrowRegex:this._erasRegex}function ii(t,e){return e.erasAbbrRegex(t)}function oi(t,e){return e.erasNameRegex(t)}function ai(t,e){return e.erasNarrowRegex(t)}function ri(t,e){return e._eraYearOrdinalRegex||Oo}function si(){var t,e,n=[],i=[],o=[],a=[],r=this.eras();for(t=0,e=r.length;t<e;++t)i.push(tt(r[t].name)),n.push(tt(r[t].abbr)),o.push(tt(r[t].narrow)),a.push(tt(r[t].name)),a.push(tt(r[t].abbr)),a.push(tt(r[t].narrow));this._erasRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+n.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+o.join("|")+")","i")}function li(t,e){D(0,[t,t.length],0,e)}function ci(t){return mi.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function di(t){return mi.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)}function ui(){return kt(this.year(),1,4)}function pi(){return kt(this.isoWeekYear(),1,4)}function hi(){var t=this.localeData()._week;return kt(this.year(),t.dow,t.doy)}function fi(){var t=this.localeData()._week;return kt(this.weekYear(),t.dow,t.doy)}function mi(t,e,n,i,o){var a;return null==t?_t(this,i,o).year:(a=kt(t,i,o),e>a&&(e=a),gi.call(this,t,e,n,i,o))}function gi(t,e,n,i,o){var a=wt(t,e,n,i,o),r=bt(a.year,0,a.dayOfYear);return this.year(r.getUTCFullYear()),this.month(r.getUTCMonth()),this.date(r.getUTCDate()),this}function vi(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)}function yi(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")}function bi(t,e){e[Wo]=q(1e3*("0."+t))}function xi(){return this._isUTC?"UTC":""}function wi(){return this._isUTC?"Coordinated Universal Time":""}function _i(t){return $e(1e3*t)}function ki(){return $e.apply(null,arguments).parseZone()}function Ci(t){return t}function Si(t,e,n,i){var o=oe(),a=u().set(i,e);return o[n](a,t)}function Ti(t,e,n){if(s(t)&&(e=t,t=void 0),t=t||"",null!=e)return Si(t,e,n,"month");var i,o=[];for(i=0;i<12;i++)o[i]=Si(t,i,n,"month");return o}function $i(t,e,n,i){"boolean"==typeof t?(s(e)&&(n=e,e=void 0),e=e||""):(e=t,n=e,t=!1,s(e)&&(n=e,e=void 0),e=e||"");var o,a=oe(),r=t?a._week.dow:0,l=[];if(null!=n)return Si(e,(n+r)%7,i,"day");for(o=0;o<7;o++)l[o]=Si(e,(o+r)%7,i,"day");return l}function Di(t,e){return Ti(t,e,"months")}function Ei(t,e){return Ti(t,e,"monthsShort")}function Fi(t,e,n){return $i(t,e,n,"weekdays")}function Ai(t,e,n){return $i(t,e,n,"weekdaysShort")}function Oi(t,e,n){return $i(t,e,n,"weekdaysMin")}function Ni(){var t=this._data;return this._milliseconds=Ba(this._milliseconds),this._days=Ba(this._days),this._months=Ba(this._months),t.milliseconds=Ba(t.milliseconds),t.seconds=Ba(t.seconds),t.minutes=Ba(t.minutes),t.hours=Ba(t.hours),t.months=Ba(t.months),t.years=Ba(t.years),this}function Li(t,e,n,i){var o=Je(e,n);return t._milliseconds+=i*o._milliseconds,t._days+=i*o._days,t._months+=i*o._months,t._bubble()}function Ri(t,e){return Li(this,t,e,1)}function Mi(t,e){return Li(this,t,e,-1)}function Pi(t){return t<0?Math.floor(t):Math.ceil(t)}function Ii(){var t,e,n,i,o,a=this._milliseconds,r=this._days,s=this._months,l=this._data;return a>=0&&r>=0&&s>=0||a<=0&&r<=0&&s<=0||(a+=864e5*Pi(Hi(s)+r),r=0,s=0),l.milliseconds=a%1e3,t=U(a/1e3),l.seconds=t%60,e=U(t/60),l.minutes=e%60,n=U(e/60),l.hours=n%24,r+=U(n/24),o=U(ji(r)),s+=o,r-=Pi(Hi(o)),i=U(s/12),s%=12,l.days=r,l.months=s,l.years=i,this}function ji(t){return 4800*t/146097}function Hi(t){return 146097*t/4800}function zi(t){if(!this.isValid())return NaN;var e,n,i=this._milliseconds;if(t=j(t),"month"===t||"quarter"===t||"year"===t)switch(e=this._days+i/864e5,n=this._months+ji(e),t){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(e=this._days+Math.round(Hi(this._months)),t){case"week":return e/7+i/6048e5;case"day":return e+i/864e5;case"hour":return 24*e+i/36e5;case"minute":return 1440*e+i/6e4;case"second":return 86400*e+i/1e3;case"millisecond":return Math.floor(864e5*e)+i;default:throw new Error("Unknown unit "+t)}}function Yi(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*q(this._months/12):NaN}function Bi(t){return function(){return this.as(t)}}function Ui(){return Je(this)}function qi(t){return t=j(t),this.isValid()?this[t+"s"]():NaN}function Wi(t){return function(){return this.isValid()?this._data[t]:NaN}}function Vi(){return U(this.days()/7)}function Gi(t,e,n,i,o){return o.relativeTime(e||1,!!n,t,i)}function Xi(t,e,n,i){var o=Je(t).abs(),a=rr(o.as("s")),r=rr(o.as("m")),s=rr(o.as("h")),l=rr(o.as("d")),c=rr(o.as("M")),d=rr(o.as("w")),u=rr(o.as("y")),p=a<=n.ss&&["s",a]||a<n.s&&["ss",a]||r<=1&&["m"]||r<n.m&&["mm",r]||s<=1&&["h"]||s<n.h&&["hh",s]||l<=1&&["d"]||l<n.d&&["dd",l];return null!=n.w&&(p=p||d<=1&&["w"]||d<n.w&&["ww",d]),p=p||c<=1&&["M"]||c<n.M&&["MM",c]||u<=1&&["y"]||["yy",u],p[2]=e,p[3]=+t>0,p[4]=i,Gi.apply(null,p)}function Qi(t){return void 0===t?rr:"function"==typeof t&&(rr=t,!0)}function Ki(t,e){return void 0!==sr[t]&&(void 0===e?sr[t]:(sr[t]=e,"s"===t&&(sr.ss=e-1),!0))}function Zi(t,e){if(!this.isValid())return this.localeData().invalidDate();var n,i,o=!1,a=sr;return"object"==typeof t&&(e=t,t=!1),"boolean"==typeof t&&(o=t),"object"==typeof e&&(a=Object.assign({},sr,e),null!=e.s&&null==e.ss&&(a.ss=e.s-1)),n=this.localeData(),i=Xi(this,!o,a,n),o&&(i=n.pastFuture(+this,i)),n.postformat(i)}function Ji(t){return(t>0)-(t<0)||+t}function to(){if(!this.isValid())return this.localeData().invalidDate();var t,e,n,i,o,a,r,s,l=lr(this._milliseconds)/1e3,c=lr(this._days),d=lr(this._months),u=this.asSeconds();return u?(t=U(l/60),e=U(t/60),l%=60,t%=60,n=U(d/12),d%=12,i=l?l.toFixed(3).replace(/\.?0+$/,""):"",o=u<0?"-":"",a=Ji(this._months)!==Ji(u)?"-":"",r=Ji(this._days)!==Ji(u)?"-":"",s=Ji(this._milliseconds)!==Ji(u)?"-":"",o+"P"+(n?a+n+"Y":"")+(d?a+d+"M":"")+(c?r+c+"D":"")+(e||t||l?"T":"")+(e?s+e+"H":"")+(t?s+t+"M":"")+(l?s+i+"S":"")):"P0D"}var eo,no;no=Array.prototype.some?Array.prototype.some:function(t){var e,n=Object(this),i=n.length>>>0;for(e=0;e<i;e++)if(e in n&&t.call(this,n[e],e,n))return!0;return!1};var io=t.momentProperties=[],oo=!1,ao={};t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var ro;ro=Object.keys?Object.keys:function(t){var e,n=[];for(e in t)o(t,e)&&n.push(e);return n};var so,lo={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},co=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,uo=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,po={},ho={},fo={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},mo="Invalid date",go="%d",vo=/\d{1,2}/,yo={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},bo={},xo={},wo=/\d/,_o=/\d\d/,ko=/\d{3}/,Co=/\d{4}/,So=/[+-]?\d{6}/,To=/\d\d?/,$o=/\d\d\d\d?/,Do=/\d\d\d\d\d\d?/,Eo=/\d{1,3}/,Fo=/\d{1,4}/,Ao=/[+-]?\d{1,6}/,Oo=/\d+/,No=/[+-]?\d+/,Lo=/Z|[+-]\d\d:?\d\d/gi,Ro=/Z|[+-]\d\d(?::?\d\d)?/gi,Mo=/[+-]?\d+(\.\d{1,3})?/,Po=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;so={};var Io,jo={},Ho=0,zo=1,Yo=2,Bo=3,Uo=4,qo=5,Wo=6,Vo=7,Go=8;Io=Array.prototype.indexOf?Array.prototype.indexOf:function(t){var e;for(e=0;e<this.length;++e)if(this[e]===t)return e;return-1},D("M",["MM",2],"Mo",function(){return this.month()+1}),D("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),D("MMMM",0,0,function(t){return this.localeData().months(this,t)}),I("month","M"),z("month",8),K("M",To),K("MM",To,_o),K("MMM",function(t,e){return e.monthsShortRegex(t)}),K("MMMM",function(t,e){return e.monthsRegex(t)}),et(["M","MM"],function(t,e){e[zo]=q(t)-1}),et(["MMM","MMMM"],function(t,e,n,i){var o=n._locale.monthsParse(t,i,n._strict);null!=o?e[zo]=o:h(n).invalidMonth=t});var Xo="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Qo="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ko=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Zo=Po,Jo=Po;D("Y",0,0,function(){var t=this.year();return t<=9999?$(t,4):"+"+t}),D(0,["YY",2],0,function(){return this.year()%100}),D(0,["YYYY",4],0,"year"),D(0,["YYYYY",5],0,"year"),D(0,["YYYYYY",6,!0],0,"year"),I("year","y"),z("year",1),K("Y",No),K("YY",To,_o),K("YYYY",Fo,Co),K("YYYYY",Ao,So),K("YYYYYY",Ao,So),et(["YYYYY","YYYYYY"],Ho),et("YYYY",function(e,n){n[Ho]=2===e.length?t.parseTwoDigitYear(e):q(e)}),et("YY",function(e,n){n[Ho]=t.parseTwoDigitYear(e)}),et("Y",function(t,e){e[Ho]=parseInt(t,10)}),t.parseTwoDigitYear=function(t){return q(t)+(q(t)>68?1900:2e3)};var ta=W("FullYear",!0);D("w",["ww",2],"wo","week"),D("W",["WW",2],"Wo","isoWeek"),I("week","w"),I("isoWeek","W"),z("week",5),z("isoWeek",5),K("w",To),K("ww",To,_o),K("W",To),K("WW",To,_o),nt(["w","ww","W","WW"],function(t,e,n,i){e[i.substr(0,1)]=q(t)});var ea={dow:0,doy:6};D("d",0,"do","day"),D("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),D("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),D("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),D("e",0,0,"weekday"),D("E",0,0,"isoWeekday"),I("day","d"),I("weekday","e"),I("isoWeekday","E"),z("day",11),z("weekday",11),z("isoWeekday",11),K("d",To),K("e",To),K("E",To),K("dd",function(t,e){return e.weekdaysMinRegex(t)}),K("ddd",function(t,e){return e.weekdaysShortRegex(t)}),K("dddd",function(t,e){return e.weekdaysRegex(t)}),nt(["dd","ddd","dddd"],function(t,e,n,i){var o=n._locale.weekdaysParse(t,i,n._strict);null!=o?e.d=o:h(n).invalidWeekday=t}),nt(["d","e","E"],function(t,e,n,i){e[i]=q(t)});var na="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),ia="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),oa="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),aa=Po,ra=Po,sa=Po;D("H",["HH",2],0,"hour"),D("h",["hh",2],0,Ut),D("k",["kk",2],0,qt),D("hmm",0,0,function(){return""+Ut.apply(this)+$(this.minutes(),2)}),D("hmmss",0,0,function(){return""+Ut.apply(this)+$(this.minutes(),2)+$(this.seconds(),2)}),D("Hmm",0,0,function(){return""+this.hours()+$(this.minutes(),2)}),D("Hmmss",0,0,function(){return""+this.hours()+$(this.minutes(),2)+$(this.seconds(),2)}),Wt("a",!0),Wt("A",!1),I("hour","h"),z("hour",13),K("a",Vt),K("A",Vt),K("H",To),K("h",To),K("k",To),K("HH",To,_o),K("hh",To,_o),K("kk",To,_o),K("hmm",$o),K("hmmss",Do),K("Hmm",$o),K("Hmmss",Do),et(["H","HH"],Bo),et(["k","kk"],function(t,e,n){var i=q(t);e[Bo]=24===i?0:i}),et(["a","A"],function(t,e,n){n._isPm=n._locale.isPM(t),n._meridiem=t}),et(["h","hh"],function(t,e,n){e[Bo]=q(t),h(n).bigHour=!0}),et("hmm",function(t,e,n){var i=t.length-2;e[Bo]=q(t.substr(0,i)),e[Uo]=q(t.substr(i)),h(n).bigHour=!0}),et("hmmss",function(t,e,n){var i=t.length-4,o=t.length-2;e[Bo]=q(t.substr(0,i)),e[Uo]=q(t.substr(i,2)),e[qo]=q(t.substr(o)),h(n).bigHour=!0}),et("Hmm",function(t,e,n){var i=t.length-2;e[Bo]=q(t.substr(0,i)),e[Uo]=q(t.substr(i))}),et("Hmmss",function(t,e,n){var i=t.length-4,o=t.length-2;e[Bo]=q(t.substr(0,i)),e[Uo]=q(t.substr(i,2)),e[qo]=q(t.substr(o))});var la,ca=/[ap]\.?m?\.?/i,da=W("Hours",!0),ua={calendar:lo,longDateFormat:fo,invalidDate:mo,ordinal:go,dayOfMonthOrdinalParse:vo,relativeTime:yo,months:Xo,monthsShort:Qo,week:ea,weekdays:na,weekdaysMin:oa,weekdaysShort:ia,meridiemParse:ca},pa={},ha={},fa=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ma=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ga=/Z|[+-]\d\d(?::?\d\d)?/,va=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ya=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ba=/^\/?Date\((-?\d+)/i,xa=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,wa={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};t.createFromInputFallback=x("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),t.ISO_8601=function(){},t.RFC_2822=function(){};var _a=x("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=$e.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:m()}),ka=x("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=$e.apply(null,arguments);return this.isValid()&&t.isValid()?t>this?this:t:m()}),Ca=function(){return Date.now?Date.now():+new Date},Sa=["year","quarter","month","week","day","hour","minute","second","millisecond"];Ie("Z",":"),Ie("ZZ",""),K("Z",Ro),K("ZZ",Ro),
et(["Z","ZZ"],function(t,e,n){n._useUTC=!0,n._tzm=je(Ro,t)});var Ta=/([\+\-]|\d\d)/gi;t.updateOffset=function(){};var $a=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Da=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;Je.fn=Le.prototype,Je.invalid=Ne;var Ea=on(1,"add"),Fa=on(-1,"subtract");t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var Aa=x("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)}),Oa=1e3,Na=60*Oa,La=60*Na,Ra=3506328*La;D("N",0,0,"eraAbbr"),D("NN",0,0,"eraAbbr"),D("NNN",0,0,"eraAbbr"),D("NNNN",0,0,"eraName"),D("NNNNN",0,0,"eraNarrow"),D("y",["y",1],"yo","eraYear"),D("y",["yy",2],0,"eraYear"),D("y",["yyy",3],0,"eraYear"),D("y",["yyyy",4],0,"eraYear"),K("N",ii),K("NN",ii),K("NNN",ii),K("NNNN",oi),K("NNNNN",ai),et(["N","NN","NNN","NNNN","NNNNN"],function(t,e,n,i){var o=n._locale.erasParse(t,i,n._strict);o?h(n).era=o:h(n).invalidEra=t}),K("y",Oo),K("yy",Oo),K("yyy",Oo),K("yyyy",Oo),K("yo",ri),et(["y","yy","yyy","yyyy"],Ho),et(["yo"],function(t,e,n,i){var o;n._locale._eraYearOrdinalRegex&&(o=t.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?e[Ho]=n._locale.eraYearOrdinalParse(t,o):e[Ho]=parseInt(t,10)}),D(0,["gg",2],0,function(){return this.weekYear()%100}),D(0,["GG",2],0,function(){return this.isoWeekYear()%100}),li("gggg","weekYear"),li("ggggg","weekYear"),li("GGGG","isoWeekYear"),li("GGGGG","isoWeekYear"),I("weekYear","gg"),I("isoWeekYear","GG"),z("weekYear",1),z("isoWeekYear",1),K("G",No),K("g",No),K("GG",To,_o),K("gg",To,_o),K("GGGG",Fo,Co),K("gggg",Fo,Co),K("GGGGG",Ao,So),K("ggggg",Ao,So),nt(["gggg","ggggg","GGGG","GGGGG"],function(t,e,n,i){e[i.substr(0,2)]=q(t)}),nt(["gg","GG"],function(e,n,i,o){n[o]=t.parseTwoDigitYear(e)}),D("Q",0,"Qo","quarter"),I("quarter","Q"),z("quarter",7),K("Q",wo),et("Q",function(t,e){e[zo]=3*(q(t)-1)}),D("D",["DD",2],"Do","date"),I("date","D"),z("date",9),K("D",To),K("DD",To,_o),K("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),et(["D","DD"],Yo),et("Do",function(t,e){e[Yo]=q(t.match(To)[0])});var Ma=W("Date",!0);D("DDD",["DDDD",3],"DDDo","dayOfYear"),I("dayOfYear","DDD"),z("dayOfYear",4),K("DDD",Eo),K("DDDD",ko),et(["DDD","DDDD"],function(t,e,n){n._dayOfYear=q(t)}),D("m",["mm",2],0,"minute"),I("minute","m"),z("minute",14),K("m",To),K("mm",To,_o),et(["m","mm"],Uo);var Pa=W("Minutes",!1);D("s",["ss",2],0,"second"),I("second","s"),z("second",15),K("s",To),K("ss",To,_o),et(["s","ss"],qo);var Ia=W("Seconds",!1);D("S",0,0,function(){return~~(this.millisecond()/100)}),D(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),D(0,["SSS",3],0,"millisecond"),D(0,["SSSS",4],0,function(){return 10*this.millisecond()}),D(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),D(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),D(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),D(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),D(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),I("millisecond","ms"),z("millisecond",16),K("S",Eo,wo),K("SS",Eo,_o),K("SSS",Eo,ko);var ja,Ha;for(ja="SSSS";ja.length<=9;ja+="S")K(ja,Oo);for(ja="S";ja.length<=9;ja+="S")et(ja,bi);Ha=W("Milliseconds",!1),D("z",0,0,"zoneAbbr"),D("zz",0,0,"zoneName");var za=v.prototype;za.add=Ea,za.calendar=pn,za.clone=hn,za.diff=xn,za.endOf=Mn,za.format=Sn,za.from=Tn,za.fromNow=$n,za.to=Dn,za.toNow=En,za.get=X,za.invalidAt=qn,za.isAfter=fn,za.isBefore=mn,za.isBetween=gn,za.isSame=vn,za.isSameOrAfter=yn,za.isSameOrBefore=bn,za.isValid=Bn,za.lang=Aa,za.locale=Fn,za.localeData=An,za.max=ka,za.min=_a,za.parsingFlags=Un,za.set=Q,za.startOf=Rn,za.subtract=Fa,za.toArray=Hn,za.toObject=zn,za.toDate=jn,za.toISOString=kn,za.inspect=Cn,"undefined"!=typeof Symbol&&null!=Symbol.for&&(za[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),za.toJSON=Yn,za.toString=_n,za.unix=In,za.valueOf=Pn,za.creationData=Wn,za.eraName=Qn,za.eraNarrow=Kn,za.eraAbbr=Zn,za.eraYear=Jn,za.year=ta,za.isLeapYear=vt,za.weekYear=ci,za.isoWeekYear=di,za.quarter=za.quarters=vi,za.month=ut,za.daysInMonth=pt,za.week=za.weeks=$t,za.isoWeek=za.isoWeeks=Dt,za.weeksInYear=hi,za.weeksInWeekYear=fi,za.isoWeeksInYear=ui,za.isoWeeksInISOWeekYear=pi,za.date=Ma,za.day=za.days=Pt,za.weekday=It,za.isoWeekday=jt,za.dayOfYear=yi,za.hour=za.hours=da,za.minute=za.minutes=Pa,za.second=za.seconds=Ia,za.millisecond=za.milliseconds=Ha,za.utcOffset=Ye,za.utc=Ue,za.local=qe,za.parseZone=We,za.hasAlignedHourOffset=Ve,za.isDST=Ge,za.isLocal=Qe,za.isUtcOffset=Ke,za.isUtc=Ze,za.isUTC=Ze,za.zoneAbbr=xi,za.zoneName=wi,za.dates=x("dates accessor is deprecated. Use date instead.",Ma),za.months=x("months accessor is deprecated. Use month instead",ut),za.years=x("years accessor is deprecated. Use year instead",ta),za.zone=x("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",Be),za.isDSTShifted=x("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",Xe);var Ya=S.prototype;Ya.calendar=T,Ya.longDateFormat=N,Ya.invalidDate=L,Ya.ordinal=R,Ya.preparse=Ci,Ya.postformat=Ci,Ya.relativeTime=M,Ya.pastFuture=P,Ya.set=k,Ya.eras=Vn,Ya.erasParse=Gn,Ya.erasConvertYear=Xn,Ya.erasAbbrRegex=ei,Ya.erasNameRegex=ti,Ya.erasNarrowRegex=ni,Ya.months=rt,Ya.monthsShort=st,Ya.monthsParse=ct,Ya.monthsRegex=ft,Ya.monthsShortRegex=ht,Ya.week=Ct,Ya.firstDayOfYear=Tt,Ya.firstDayOfWeek=St,Ya.weekdays=Ot,Ya.weekdaysMin=Lt,Ya.weekdaysShort=Nt,Ya.weekdaysParse=Mt,Ya.weekdaysRegex=Ht,Ya.weekdaysShortRegex=zt,Ya.weekdaysMinRegex=Yt,Ya.isPM=Gt,Ya.meridiem=Xt,ee("en",{eras:[{since:"0001-01-01",until:+(1/0),offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-(1/0),offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10,n=1===q(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th";return t+n}}),t.lang=x("moment.lang is deprecated. Use moment.locale instead.",ee),t.langData=x("moment.langData is deprecated. Use moment.localeData instead.",oe);var Ba=Math.abs,Ua=Bi("ms"),qa=Bi("s"),Wa=Bi("m"),Va=Bi("h"),Ga=Bi("d"),Xa=Bi("w"),Qa=Bi("M"),Ka=Bi("Q"),Za=Bi("y"),Ja=Wi("milliseconds"),tr=Wi("seconds"),er=Wi("minutes"),nr=Wi("hours"),ir=Wi("days"),or=Wi("months"),ar=Wi("years"),rr=Math.round,sr={ss:44,s:45,m:45,h:22,d:26,w:null,M:11},lr=Math.abs,cr=Le.prototype;return cr.isValid=Oe,cr.abs=Ni,cr.add=Ri,cr.subtract=Mi,cr.as=zi,cr.asMilliseconds=Ua,cr.asSeconds=qa,cr.asMinutes=Wa,cr.asHours=Va,cr.asDays=Ga,cr.asWeeks=Xa,cr.asMonths=Qa,cr.asQuarters=Ka,cr.asYears=Za,cr.valueOf=Yi,cr._bubble=Ii,cr.clone=Ui,cr.get=qi,cr.milliseconds=Ja,cr.seconds=tr,cr.minutes=er,cr.hours=nr,cr.days=ir,cr.weeks=Vi,cr.months=or,cr.years=ar,cr.humanize=Zi,cr.toISOString=to,cr.toString=to,cr.toJSON=to,cr.locale=Fn,cr.localeData=An,cr.toIsoString=x("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",to),cr.lang=Aa,D("X",0,0,"unix"),D("x",0,0,"valueOf"),K("x",No),K("X",Mo),et("X",function(t,e,n){n._d=new Date(1e3*parseFloat(t))}),et("x",function(t,e,n){n._d=new Date(q(t))}),t.version="2.29.4",e($e),t.fn=za,t.min=Ee,t.max=Fe,t.now=Ca,t.utc=u,t.unix=_i,t.months=Di,t.isDate=l,t.locale=ee,t.invalid=m,t.duration=Je,t.isMoment=y,t.weekdays=Fi,t.parseZone=ki,t.localeData=oe,t.isDuration=Re,t.monthsShort=Ei,t.weekdaysMin=Oi,t.defineLocale=ne,t.updateLocale=ie,t.locales=ae,t.weekdaysShort=Ai,t.normalizeUnits=j,t.relativeTimeRounding=Qi,t.relativeTimeThreshold=Ki,t.calendarFormat=un,t.prototype=za,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t}),define("moment",["moment/moment"],function(t){return t}),define("frontend",["fast","template","moment"],function(t,e,n){var i={api:t.api,init:function(){var t={};if($(document).on("click",".btn-captcha",function(e){var n=$(this).data("type")?$(this).data("type"):"mobile",o=this;if(i.api.sendcaptcha=function(e,n,o,a){$(e).addClass("disabled",!0).text("发送中..."),i.api.ajax({url:$(e).data("url"),data:o},function(i,o){clearInterval(t[n]);var r=60;t[n]=setInterval(function(){r--,r<=0?(clearInterval(t),$(e).removeClass("disabled").text("发送验证码")):$(e).addClass("disabled").text(r+"秒后可再次发送")},1e3),"function"==typeof a&&a.call(this,i,o)},function(){$(e).removeClass("disabled").text("发送验证码")})},["mobile","email"].indexOf(n)>-1){var a=$(this).data("input-id")?$("#"+$(this).data("input-id")):$("input[name='"+n+"']",$(this).closest("form")),r="email"===n?"邮箱":"手机号码";if(""===a.val())return Layer.msg(r+"不能为空！"),a.focus(),!1;if("mobile"===n&&!a.val().match(/^1[3-9]\d{9}$/))return Layer.msg("请输入正确的"+r+"！"),a.focus(),!1;if("email"===n&&!a.val().match(/^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/))return Layer.msg("请输入正确的"+r+"！"),a.focus(),!1;a.isValid(function(t){if(t){var e={event:$(o).data("event")};e[n]=a.val(),i.api.sendcaptcha(o,n,e)}else Layer.msg("请确认已经输入了正确的"+r+"！")})}else{var s={event:$(o).data("event")};i.api.sendcaptcha(o,n,s,function(t,e){Layer.open({title:!1,area:["400px","430px"],content:"<img src='"+t.image+"' width='400' height='400' /><div class='text-center panel-title'>扫一扫关注公众号获取验证码</div>",type:1})})}return!1}),"ontouchstart"in document.documentElement||$("body").tooltip({selector:'[data-toggle="tooltip"]'}),$("body").popover({selector:'[data-toggle="popover"]'}),"ontouchstart"in document.documentElement){var e,n,o,a,r,s,l;l=$("body",document),l.on("touchstart",function(t){e=t.originalEvent.changedTouches[0].pageX,n=t.originalEvent.changedTouches[0].pageY}),l.on("touchend",function(t){o=t.originalEvent.changedTouches[0].pageX,a=t.originalEvent.changedTouches[0].pageY,r=o-e,s=a-n,r>45?Math.abs(r)-Math.abs(s)>50&&l.addClass("sidebar-open"):r<-45&&Math.abs(r)-Math.abs(s)>50&&l.removeClass("sidebar-open")})}$(document).on("click",".sidebar-toggle",function(){$("body").toggleClass("sidebar-open")})}};return i.api=$.extend(t.api,i.api),window.Template=e,window.Moment=n,window.Frontend=i,i.init(),i}),define("frontend-init",["frontend"],function(t){}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?e(require("../moment")):"function"==typeof define&&define.amd?define("moment/locale/zh-cn",["../moment"],e):e(t.moment)}(this,function(t){"use strict";var e=t.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(t,e){return 12===t&&(t=0),"凌晨"===e||"早上"===e||"上午"===e?t:"下午"===e||"晚上"===e?t+12:t>=11?t:t+12},meridiem:function(t,e,n){var i=100*t+e;return i<600?"凌晨":i<900?"早上":i<1130?"上午":i<1230?"中午":i<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(t){return t.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(t){return this.week()!==t.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(t,e){switch(e){case"d":case"D":case"DDD":return t+"日";case"M":return t+"月";case"w":case"W":return t+"周";default:return t}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}});return e}),!function(t){"use strict";function e(t){var e=arguments,n=!0,i=1;return t=t.replace(/%s/g,function(){var t=e[i++];return void 0===t?(n=!1,""):t}),n?t:""}function n(e,n){var i=-1;return t.each(e,function(t,e){return e.field!==n||(i=t,!1)}),i}function i(){var e,n,i;return null===u&&(i=t("<p/>").addClass("fixed-table-scroll-inner"),(e=t("<div/>").addClass("fixed-table-scroll-outer")).append(i),t("body").append(e),n=i[0].offsetWidth,e.css("overflow","scroll"),n===(i=i[0].offsetWidth)&&(i=e[0].clientWidth),e.remove(),u=n-i),u}function o(n,i,o,a){var r,s=i;return"string"==typeof i&&(1<(r=i.split(".")).length?(s=window,t.each(r,function(t,e){s=s[e]})):s=window[i]),"object"==typeof s?s:"function"==typeof s?s.apply(n,o||[]):!s&&"string"==typeof i&&e.apply(this,[i].concat(o))?e.apply(this,[i].concat(o)):a}function a(e,n,i){var o,a=Object.getOwnPropertyNames(e),r=Object.getOwnPropertyNames(n);if(i&&a.length!==r.length)return!1;for(var s=0;s<a.length;s++)if(o=a[s],-1<t.inArray(o,r)&&e[o]!==n[o])return!1;return!0}function r(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t}function s(t){for(var e in t){var n=e.split(/(?=[A-Z])/).join("-").toLowerCase();n!==e&&(t[n]=t[e],delete t[e])}return t}function l(t,e,n){var i=t;if("string"!=typeof e||t.hasOwnProperty(e))return n?r(t[e]):t[e];var o,a=e.split(".");for(o in a)a.hasOwnProperty(o)&&(i=i&&i[a[o]]);return n?r(i):i}function c(){return!!(0<navigator.userAgent.indexOf("MSIE ")||navigator.userAgent.match(/Trident.*rv\:11\./))}function d(e,n){this.options=n,this.$el=t(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0,this.init()}var u=null,p=(d.DEFAULTS={classes:"table table-hover",sortClass:void 0,locale:void 0,height:void 0,undefinedText:"-",sortName:void 0,sortOrder:"asc",sortStable:!1,striped:!1,columns:[[]],data:[],totalField:"total",dataField:"rows",method:"get",url:void 0,ajax:void 0,cache:!0,contentType:"application/json",dataType:"json",ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},pagination:!1,onlyInfoPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",search:!1,searchOnEnterKey:!1,strictSearch:!1,searchAlign:"right",selectItemName:"btSelectItem",showHeader:!0,showFooter:!1,showColumns:!1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,buttonsAlign:"right",smartDisplay:!0,escape:!1,minimumCountColumns:1,idField:void 0,uniqueId:void 0,cardView:!1,detailView:!1,detailFormatter:function(t,e){return""},trimOnSearch:!0,clickToSelect:!1,singleSelect:!1,toolbar:void 0,toolbarAlign:"left",checkboxHeader:!0,sortable:!0,silentSort:!0,maintainSelected:!1,searchTimeOut:500,searchText:"",iconSize:void 0,buttonsClass:"default",iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggle:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus"},customSearch:t.noop,customSort:t.noop,rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},footerStyle:function(t,e){return{}},onAll:function(t,e){return!1},onClickCell:function(t,e,n,i){return!1},onDblClickCell:function(t,e,n,i){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onExpandRow:function(t,e,n){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1}},(d.LOCALES={})["en-US"]=d.LOCALES.en={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(t){return e("%s rows per page",t)},formatShowingRows:function(t,n,i){return e("Showing %s to %s of %s rows",t,n,i)},formatDetailPagination:function(t){return e("Showing %s rows",t)},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"}},t.extend(d.DEFAULTS,d.LOCALES["en-US"]),d.COLUMN_DEFAULTS={radio:!1,checkbox:!1,checkboxEnabled:!0,field:void 0,title:void 0,titleTooltip:void 0,class:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,width:void 0,sortable:!1,order:"asc",visible:!0,switchable:!0,clickToSelect:!0,formatter:void 0,footerFormatter:void 0,events:void 0,sorter:void 0,sortName:void 0,cellStyle:void 0,searchable:!0,searchFormatter:!0,cardVisible:!0,escape:!1},d.EVENTS={"all.bs.table":"onAll","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh"},d.prototype.init=function(){this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initFooter(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()},d.prototype.initLocale=function(){var e;this.options.locale&&((e=this.options.locale.split(/-|_/))[0].toLowerCase(),e[1]&&e[1].toUpperCase(),t.fn.bootstrapTable.locales[this.options.locale]?t.extend(this.options,t.fn.bootstrapTable.locales[this.options.locale]):t.fn.bootstrapTable.locales[e.join("-")]?t.extend(this.options,t.fn.bootstrapTable.locales[e.join("-")]):t.fn.bootstrapTable.locales[e[0]]&&t.extend(this.options,t.fn.bootstrapTable.locales[e[0]]))},d.prototype.initContainer=function(){this.$container=t(['<div class="bootstrap-table">','<div class="fixed-table-toolbar"></div>',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination" style="clear: both;"></div>':"",'<div class="fixed-table-container">','<div class="fixed-table-header"><table></table></div>','<div class="fixed-table-body">','<div class="fixed-table-loading">',this.options.formatLoadingMessage(),"</div>","</div>",'<div class="fixed-table-footer"><table><tr></tr></table></div>',"bottom"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination"></div>':"","</div>","</div>"].join("")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$container.find(".fixed-table-footer"),this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),-1!==t.inArray("table-no-bordered",this.options.classes.split(" "))&&this.$tableContainer.addClass("table-no-bordered")},d.prototype.initTable=function(){for(var e,n,i,o=this,a=[],r=[],l=(this.$header=this.$el.find(">thead"),this.$header.length||(this.$header=t("<thead></thead>").appendTo(this.$el)),this.$header.find("tr").each(function(){var e=[];t(this).find("th").each(function(){void 0!==t(this).data("field")&&t(this).data("field",t(this).data("field")+""),e.push(t.extend({},{title:t(this).html(),class:t(this).attr("class"),titleTooltip:t(this).attr("title"),rowspan:t(this).attr("rowspan")?+t(this).attr("rowspan"):void 0,colspan:t(this).attr("colspan")?+t(this).attr("colspan"):void 0},t(this).data()))}),a.push(e)}),t.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=t.extend(!0,[],a,this.options.columns),this.columns=[],this.options.columns),c=0,u=[],p=0;p<l[0].length;p++)c+=l[0][p].colspan||1;for(p=0;p<l.length;p++)for(u[p]=[],n=0;n<c;n++)u[p][n]=!1;for(p=0;p<l.length;p++)for(n=0;n<l[p].length;n++){var h=l[p][n],f=h.rowspan||1,m=h.colspan||1,g=t.inArray(!1,u[p]);for(1===m&&(h.fieldIndex=g,void 0===h.field)&&(h.field=g),i=0;i<f;i++)u[p+i][g]=!0;for(i=0;i<m;i++)u[p][g+i]=!0}t.each(this.options.columns,function(e,n){t.each(n,function(n,i){void 0!==(i=t.extend({},d.COLUMN_DEFAULTS,i)).fieldIndex&&(o.columns[i.fieldIndex]=i),o.options.columns[e][n]=i})}),this.options.data.length||(e=[],this.$el.find(">tbody>tr").each(function(n){var i={};i._id=t(this).attr("id"),i._class=t(this).attr("class"),i._data=s(t(this).data()),t(this).find(">td").each(function(a){for(var r,l,c=t(this),d=+c.attr("colspan")||1,u=+c.attr("rowspan")||1;e[n]&&e[n][a];a++);for(r=a;r<a+d;r++)for(l=n;l<n+u;l++)e[l]||(e[l]=[]),e[l][r]=!0;c=o.columns[a].field,i[c]=t(this).html(),i["_"+c+"_id"]=t(this).attr("id"),i["_"+c+"_class"]=t(this).attr("class"),i["_"+c+"_rowspan"]=t(this).attr("rowspan"),i["_"+c+"_colspan"]=t(this).attr("colspan"),i["_"+c+"_title"]=t(this).attr("title"),i["_"+c+"_data"]=s(t(this).data())}),r.push(i)}),(this.options.data=r).length&&(this.fromHtml=!0))},d.prototype.initHeader=function(){var n=this,i={},o=[];this.header={fields:[],styles:[],classes:[],formatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},t.each(this.options.columns,function(a,s){o.push("<tr>"),0===a&&!n.options.cardView&&n.options.detailView&&o.push(e('<th class="detail" rowspan="%s"><div class="fht-cell"></div></th>',n.options.columns.length)),t.each(s,function(t,a){var s,l,c,d="",u=e(' class="%s"',a.class),p=(n.options.sortOrder||a.order,"px"),h=a.width;if(void 0===a.width||n.options.cardView||"string"==typeof a.width&&-1!==a.width.indexOf("%")&&(p="%"),a.width&&"string"==typeof a.width&&(h=a.width.replace("%","").replace("px","")),s=e("text-align: %s; ",a.halign||a.align),l=e("text-align: %s; ",a.align),c=e("vertical-align: %s; ",a.valign),c+=e("width: %s; ",!a.checkbox&&!a.radio||h?h?h+p:void 0:"36px"),void 0!==a.fieldIndex){if(n.header.fields[a.fieldIndex]=a.field,n.header.styles[a.fieldIndex]=l+c,n.header.classes[a.fieldIndex]=u,n.header.formatters[a.fieldIndex]=a.formatter,n.header.events[a.fieldIndex]=a.events,n.header.sorters[a.fieldIndex]=a.sorter,n.header.sortNames[a.fieldIndex]=a.sortName,n.header.cellStyles[a.fieldIndex]=a.cellStyle,n.header.searchables[a.fieldIndex]=a.searchable,!a.visible)return;if(n.options.cardView&&!a.cardVisible)return;i[a.field]=a}o.push("<th"+e(' title="%s"',a.titleTooltip),a.checkbox||a.radio?e(' class="bs-checkbox %s"',a.class||""):u,e(' style="%s"',s+c),e(' rowspan="%s"',a.rowspan),e(' colspan="%s"',a.colspan),e(' data-field="%s"',a.field),">"),o.push(e('<div class="th-inner %s">',n.options.sortable&&a.sortable?"sortable both":"")),d=n.options.escape?r(a.title):a.title,a.checkbox&&(!n.options.singleSelect&&n.options.checkboxHeader&&(d='<input name="btSelectAll" type="checkbox" />'),n.header.stateField=a.field),a.radio&&(d="",n.header.stateField=a.field,n.options.singleSelect=!0),o.push(d),o.push("</div>"),o.push('<div class="fht-cell"></div>'),o.push("</div>"),o.push("</th>")}),o.push("</tr>")}),this.$header.html(o.join("")),this.$header.find("th[data-field]").each(function(e){t(this).data(i[t(this).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(e){var i=t(this);return(!n.options.detailView||i.closest(".bootstrap-table")[0]===n.$container[0])&&void(n.options.sortable&&i.parent().data().sortable&&n.onSort(e))}),this.$header.children().children().off("keypress").on("keypress",function(e){n.options.sortable&&t(this).data().sortable&&13==(e.keyCode||e.which)&&n.onSort(e)}),t(window).off("resize.bootstrap-table"),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),t(window).on("resize.bootstrap-table",t.proxy(this.resetWidth,this))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(){var e=t(this).prop("checked");n[e?"checkAll":"uncheckAll"](),n.updateSelected()})},d.prototype.initFooter=function(){!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()},d.prototype.initData=function(t,e){this.data="append"===e?this.data.concat(t):"prepend"===e?[].concat(t).concat(this.data):t||this.options.data,this.options.data="append"===e?this.options.data.concat(t):"prepend"===e?[].concat(t).concat(this.options.data):this.data,"server"!==this.options.sidePagination&&this.initSort()},d.prototype.initSort=function(){var n=this,i=this.options.sortName,a="desc"===this.options.sortOrder?-1:1,r=t.inArray(this.options.sortName,this.header.fields);this.options.customSort!==t.noop?this.options.customSort.apply(this,[this.options.sortName,this.options.sortOrder]):-1!==r&&(this.options.sortStable&&t.each(this.data,function(t,e){e.hasOwnProperty("_position")||(e._position=t)}),this.data.sort(function(e,s){n.header.sortNames[r]&&(i=n.header.sortNames[r]);var c=l(e,i,n.options.escape),d=l(s,i,n.options.escape),u=o(n.header,n.header.sorters[r],[c,d]);return void 0!==u?a*u:(null==c&&(c=""),null==d&&(d=""),n.options.sortStable&&c===d&&(c=e._position,d=s._position),t.isNumeric(c)&&t.isNumeric(d)?(c=parseFloat(c))<(d=parseFloat(d))?-1*a:a:c===d?0:-1===(c="string"!=typeof c?c.toString():c).localeCompare(d)?-1*a:a)}),void 0!==this.options.sortClass)&&(clearTimeout(0),setTimeout(function(){n.$el.removeClass(n.options.sortClass);var t=n.$header.find(e('[data-field="%s"]',n.options.sortName).index()+1);n.$el.find(e("tr td:nth-child(%s)",t)).addClass(n.options.sortClass)},250))},d.prototype.onSort=function(e){var e="keypress"===e.type?t(e.currentTarget):t(e.currentTarget).parent(),n=this.$header.find("th").eq(e.index());this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===e.data("field")?this.options.sortOrder="asc"===this.options.sortOrder?"desc":"asc":(this.options.sortName=e.data("field"),this.options.sortOrder="asc"===e.data("order")?"desc":"asc"),this.trigger("sort",this.options.sortName,this.options.sortOrder),e.add(n).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination?this.initServer(this.options.silentSort):(this.initSort(),this.initBody())},d.prototype.initToolbar=function(){var n,i=this,a=[],r=0,s=0;this.$toolbar.find(".bs-bars").children().length&&t("body").append(t(this.options.toolbar)),this.$toolbar.html(""),"string"!=typeof this.options.toolbar&&"object"!=typeof this.options.toolbar||t(e('<div class="bs-bars pull-%s"></div>',this.options.toolbarAlign)).appendTo(this.$toolbar).append(t(this.options.toolbar)),a=[e('<div class="columns columns-%s btn-group pull-%s">',this.options.buttonsAlign,this.options.buttonsAlign)],"string"==typeof this.options.icons&&(this.options.icons=o(null,this.options.icons)),this.options.showPaginationSwitch&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="paginationSwitch" aria-label="pagination Switch" title="%s">',this.options.formatPaginationSwitch()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.paginationSwitchDown),"</button>"),this.options.showRefresh&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="refresh" aria-label="refresh" title="%s">',this.options.formatRefresh()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.refresh),"</button>"),this.options.showToggle&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="toggle" aria-label="toggle" title="%s">',this.options.formatToggle()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.toggle),"</button>"),this.options.showColumns&&(a.push(e('<div class="keep-open btn-group" title="%s">',this.options.formatColumns()),'<button type="button" aria-label="columns" class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">',e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.columns),' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'),t.each(this.columns,function(t,n){var o;n.radio||n.checkbox||i.options.cardView&&!n.cardVisible||(o=n.visible?' checked="checked"':"",n.switchable&&(a.push(e('<li role="menuitem"><label><input type="checkbox" data-field="%s" value="%s"%s> %s</label></li>',n.field,t,o,n.title)),s++))}),a.push("</ul>","</div>")),a.push("</div>"),(this.showToolbar||2<a.length)&&this.$toolbar.append(a.join("")),this.options.showPaginationSwitch&&this.$toolbar.find('button[name="paginationSwitch"]').off("click").on("click",t.proxy(this.togglePagination,this)),this.options.showRefresh&&this.$toolbar.find('button[name="refresh"]').off("click").on("click",t.proxy(this.refresh,this)),this.options.showToggle&&this.$toolbar.find('button[name="toggle"]').off("click").on("click",function(){i.toggleView()}),this.options.showColumns&&(n=this.$toolbar.find(".keep-open"),s<=this.options.minimumCountColumns&&n.find("input").prop("disabled",!0),n.find("li").off("click").on("click",function(t){t.stopImmediatePropagation()}),n.find("input").off("click").on("click",function(){var e=t(this);i.toggleColumn(t(this).val(),e.prop("checked"),!1),i.trigger("column-switch",t(this).data("field"),e.prop("checked"))})),this.options.search&&((a=[]).push('<div class="pull-'+this.options.searchAlign+' search">',e('<input class="form-control'+e(" input-%s",this.options.iconSize)+'" type="text" placeholder="%s">',this.options.formatSearch()),"</div>"),this.$toolbar.append(a.join("")),(n=this.$toolbar.find(".search input")).off("keyup drop blur").on("keyup drop blur",function(e){i.options.searchOnEnterKey&&13!==e.keyCode||-1<t.inArray(e.keyCode,[37,38,39,40])||(clearTimeout(r),r=setTimeout(function(){i.onSearch(e)},i.options.searchTimeOut))}),c())&&n.off("mouseup").on("mouseup",function(t){clearTimeout(r),r=setTimeout(function(){i.onSearch(t)},i.options.searchTimeOut)})},d.prototype.onSearch=function(e){
var n=t.trim(t(e.currentTarget).val());this.options.trimOnSearch&&t(e.currentTarget).val()!==n&&t(e.currentTarget).val(n),n===this.searchText||""===n&&void 0===this.searchText||(this.searchText=n,this.options.searchText=n,this.options.pageNumber=1,this.initSearch(),this.updatePagination(),this.trigger("search",n))},d.prototype.initSearch=function(){var e,i,a=this;"server"!==this.options.sidePagination&&(this.options.customSearch!==t.noop?this.options.customSearch.apply(this,[this.searchText]):(e=this.searchText&&(this.options.escape?r(this.searchText):this.searchText).toLowerCase(),i=t.isEmptyObject(this.filterColumns)?null:this.filterColumns,this.data=i?t.grep(this.options.data,function(e,n){for(var o in i)if(t.isArray(i[o])&&-1===t.inArray(e[o],i[o])||!t.isArray(i[o])&&e[o]!==i[o])return!1;return!0}):this.options.data,this.data=e?t.grep(this.data,function(i,r){for(var s=0;s<a.header.fields.length;s++)if(a.header.searchables[s]){var l=t.isNumeric(a.header.fields[s])?parseInt(a.header.fields[s],10):a.header.fields[s],c=a.columns[n(a.columns,l)];if("string"==typeof l){for(var d=i,u=l.split("."),p=0;p<u.length;p++)d=d[u[p]];c&&c.searchFormatter&&(d=o(c,a.header.formatters[s],[d,i,r],d))}else d=i[l];if("string"==typeof d||"number"==typeof d)if(a.options.strictSearch){if((d+"").toLowerCase()===e)return!0}else if(-1!==(d+"").toLowerCase().indexOf(e))return!0}return!1}):this.data))},d.prototype.initPagination=function(){if(this.options.pagination){this.$pagination.show();var n,i,o,a,r,s,l,c=this,d=[],u=!1,p=this.getData(),h=this.options.pageList;if("server"!==this.options.sidePagination&&(this.options.totalRows=p.length),this.totalPages=0,this.options.totalRows&&(this.options.pageSize===this.options.formatAllRows()?(this.options.pageSize=this.options.totalRows,u=!0):this.options.pageSize===this.options.totalRows&&(p="string"==typeof this.options.pageList?this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.pageList,-1<t.inArray(this.options.formatAllRows().toLowerCase(),p))&&(u=!0),this.totalPages=1+~~((this.options.totalRows-1)/this.options.pageSize),this.options.totalPages=this.totalPages),0<this.totalPages&&this.options.pageNumber>this.totalPages&&(this.options.pageNumber=this.totalPages),this.pageFrom=(this.options.pageNumber-1)*this.options.pageSize+1,this.pageTo=this.options.pageNumber*this.options.pageSize,this.pageTo>this.options.totalRows&&(this.pageTo=this.options.totalRows),d.push('<div class="pull-'+this.options.paginationDetailHAlign+' pagination-detail">','<span class="pagination-info">',this.options.onlyInfoPagination?this.options.formatDetailPagination(this.options.totalRows):this.options.formatShowingRows(this.pageFrom,this.pageTo,this.options.totalRows),"</span>"),!this.options.onlyInfoPagination){d.push('<span class="page-list">');var f=[e('<span class="btn-group %s">',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?"dropdown":"dropup"),'<button type="button" class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">','<span class="page-size">',u?this.options.formatAllRows():this.options.pageSize,"</span>",' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'];for("string"==typeof this.options.pageList&&(p=this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").split(","),h=[],t.each(p,function(t,e){h.push(e.toUpperCase()===c.options.formatAllRows().toUpperCase()?c.options.formatAllRows():+e)})),t.each(h,function(t,n){(!c.options.smartDisplay||0===t||h[t-1]<c.options.totalRows)&&(t=u?n===c.options.formatAllRows()?' class="active"':"":n===c.options.pageSize?' class="active"':"",f.push(e('<li role="menuitem"%s><a href="#">%s</a></li>',t,n)))}),f.push("</ul></span>"),d.push(this.options.formatRecordsPerPage(f.join(""))),d.push("</span>"),d.push("</div>",'<div class="pull-'+this.options.paginationHAlign+' pagination">','<ul class="pagination'+e(" pagination-%s",this.options.iconSize)+'">','<li class="page-pre"><a href="#">'+this.options.paginationPreText+"</a></li>"),this.totalPages<5?(o=1,i=this.totalPages):(i=(o=this.options.pageNumber-2)+4,o<1&&(o=1,i=5),i>this.totalPages&&(o=(i=this.totalPages)-4)),6<=this.totalPages&&(3<=this.options.pageNumber&&(d.push('<li class="page-first'+(1===this.options.pageNumber?" active":"")+'">','<a href="#">',1,"</a>","</li>"),o++),4<=this.options.pageNumber)&&(4==this.options.pageNumber||6==this.totalPages||7==this.totalPages?o--:d.push('<li class="page-first-separator disabled">','<a href="#">...</a>',"</li>"),i--),7<=this.totalPages&&this.options.pageNumber>=this.totalPages-2&&o--,6==this.totalPages?this.options.pageNumber>=this.totalPages-2&&i++:7<=this.totalPages&&(7==this.totalPages||this.options.pageNumber>=this.totalPages-3)&&i++,n=o;n<=i;n++)d.push('<li class="page-number'+(n===this.options.pageNumber?" active":"")+'">','<a href="#">',n,"</a>","</li>");8<=this.totalPages&&this.options.pageNumber<=this.totalPages-4&&d.push('<li class="page-last-separator disabled">','<a href="#">...</a>',"</li>"),6<=this.totalPages&&this.options.pageNumber<=this.totalPages-3&&d.push('<li class="page-last'+(this.totalPages===this.options.pageNumber?" active":"")+'">','<a href="#">',this.totalPages,"</a>","</li>"),d.push('<li class="page-next"><a href="#">'+this.options.paginationNextText+"</a></li>","</ul>","</div>")}this.$pagination.html(d.join("")),this.options.onlyInfoPagination||(p=this.$pagination.find(".page-list a"),o=this.$pagination.find(".page-first"),a=this.$pagination.find(".page-pre"),r=this.$pagination.find(".page-next"),s=this.$pagination.find(".page-last"),l=this.$pagination.find(".page-number"),this.options.smartDisplay&&(this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),(h.length<2||this.options.totalRows<=h[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"]()),this.options.paginationLoop||(1===this.options.pageNumber&&a.addClass("disabled"),this.options.pageNumber===this.totalPages&&r.addClass("disabled")),u&&(this.options.pageSize=this.options.formatAllRows()),p.off("click").on("click",t.proxy(this.onPageListChange,this)),o.off("click").on("click",t.proxy(this.onPageFirst,this)),a.off("click").on("click",t.proxy(this.onPagePre,this)),r.off("click").on("click",t.proxy(this.onPageNext,this)),s.off("click").on("click",t.proxy(this.onPageLast,this)),l.off("click").on("click",t.proxy(this.onPageNumber,this)))}else this.$pagination.hide()},d.prototype.updatePagination=function(e){e&&t(e.currentTarget).hasClass("disabled")||(this.options.maintainSelected||this.resetRows(),this.initPagination(),"server"===this.options.sidePagination?this.initServer():this.initBody(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize))},d.prototype.onPageListChange=function(e){var n=t(e.currentTarget);return n.parent().addClass("active").siblings().removeClass("active"),this.options.pageSize=n.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+n.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(e),!1},d.prototype.onPageFirst=function(t){return this.options.pageNumber=1,this.updatePagination(t),!1},d.prototype.onPagePre=function(t){return this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1},d.prototype.onPageNext=function(t){return this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1},d.prototype.onPageLast=function(t){return this.options.pageNumber=this.totalPages,this.updatePagination(t),!1},d.prototype.onPageNumber=function(e){if(this.options.pageNumber!==+t(e.currentTarget).text())return this.options.pageNumber=+t(e.currentTarget).text(),this.updatePagination(e),!1},d.prototype.initRow=function(n,i,a,s){var c,d=this,u=[],p={},h=[],f="",m={},g=[];if(!(-1<t.inArray(n,this.hiddenRows))){if((p=o(this.options,this.options.rowStyle,[n,i],p))&&p.css)for(c in p.css)h.push(c+": "+p.css[c]);if(m=o(this.options,this.options.rowAttributes,[n,i],m))for(c in m)g.push(e('%s="%s"',c,r(m[c])));return n._data&&!t.isEmptyObject(n._data)&&t.each(n._data,function(t,n){"index"!==t&&(f+=e(' data-%s="%s"',t,n))}),u.push("<tr",e(" %s",g.join(" ")),e(' id="%s"',t.isArray(n)?void 0:n._id),e(' class="%s"',p.classes||(t.isArray(n)?void 0:n._class)),e(' data-index="%s"',i),e(' data-uniqueid="%s"',n[this.options.uniqueId]),e("%s",f),">"),this.options.cardView&&u.push(e('<td colspan="%s"><div class="card-views">',this.header.fields.length)),!this.options.cardView&&this.options.detailView&&u.push("<td>",'<a class="detail-icon" href="#">',e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.detailOpen),"</a>","</td>"),t.each(this.header.fields,function(a,s){var c="",f=l(n,s,d.options.escape),m="",g={},v="",y=d.header.classes[a],b="",x="",w="",_="",k=d.columns[a];if((!d.fromHtml||void 0!==f)&&k.visible&&(!d.options.cardView||k.cardVisible)){if(k.escape&&(f=r(f)),p=e('style="%s"',h.concat(d.header.styles[a]).join("; ")),n["_"+s+"_id"]&&(v=e(' id="%s"',n["_"+s+"_id"])),n["_"+s+"_class"]&&(y=e(' class="%s"',n["_"+s+"_class"])),n["_"+s+"_rowspan"]&&(x=e(' rowspan="%s"',n["_"+s+"_rowspan"])),n["_"+s+"_colspan"]&&(w=e(' colspan="%s"',n["_"+s+"_colspan"])),n["_"+s+"_title"]&&(_=e(' title="%s"',n["_"+s+"_title"])),(g=o(d.header,d.header.cellStyles[a],[f,n,i,s],g)).classes&&(y=e(' class="%s"',g.classes)),g.css){var C,S=[];for(C in g.css)S.push(C+": "+g.css[C]);p=e('style="%s"',S.concat(d.header.styles[a]).join("; "))}var T,$,D,E,F,m=o(k,d.header.formatters[a],[f,n,i],f);n["_"+s+"_data"]&&!t.isEmptyObject(n["_"+s+"_data"])&&t.each(n["_"+s+"_data"],function(t,n){"index"!==t&&(b+=e(' data-%s="%s"',t,n))}),k.checkbox||k.radio?(T=k.checkbox?"checkbox":"",T=k.radio?"radio":T,c=[e(d.options.cardView?'<div class="card-view %s">':'<td class="bs-checkbox %s">',k.class||""),"<input"+e(' data-index="%s"',i)+e(' name="%s"',d.options.selectItemName)+e(' type="%s"',T)+e(' value="%s"',n[d.options.idField])+e(' checked="%s"',!0===m||f||m&&m.checked?"checked":void 0)+e(' disabled="%s"',!k.checkboxEnabled||m&&m.disabled?"disabled":void 0)+" />",d.header.formatters[a]&&"string"==typeof m?m:"",d.options.cardView?"</div>":"</td>"].join(""),n[d.header.stateField]=!0===m||m&&m.checked):(m=null==m?d.options.undefinedText:m,c=(d.options.cardView?['<div class="card-view">',d.options.showHeader?e('<span class="title" %s>%s</span>',p,(T=d.columns,$="field",D="title",E=s,F="",t.each(T,function(t,e){return e[$]!==E||(F=e[D],!1)}),F)):"",e('<span class="value">%s</span>',m),"</div>"]:[e("<td%s %s %s %s %s %s %s>",v,y,p,b,x,w,_),m,"</td>"]).join(""),d.options.cardView&&d.options.smartDisplay&&""===m&&(c='<div class="card-view"></div>')),u.push(c)}}),this.options.cardView&&u.push("</div></td>"),u.push("</tr>"),u.join(" ")}},d.prototype.initBody=function(i){for(var a=this,r=this.getData(),s=(this.trigger("pre-body",r),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=t("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=r.length),t(document.createDocumentFragment())),c=this.pageFrom-1;c<this.pageTo;c++){var d=r[c],d=this.initRow(d,c,r,s),u=u||!!d;d&&!0!==d&&s.append(d)}u||s.append('<tr class="no-records-found">'+e('<td colspan="%s">%s</td>',this.$header.find("th").length,this.options.formatNoMatches())+"</tr>"),this.$body.html(s),i||this.scrollTo(0),this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(i){var o=t(this),r=o.parent(),s=a.data[r.data("index")],c=o[0].cellIndex,c=a.getVisibleFields()[a.options.detailView&&!a.options.cardView?c-1:c],d=a.columns[n(a.columns,c)],u=l(s,c,a.options.escape);o.find(".detail-icon").length||(a.trigger("click"===i.type?"click-cell":"dbl-click-cell",c,u,s,o),a.trigger("click"===i.type?"click-row":"dbl-click-row",s,r,c),"click"===i.type&&a.options.clickToSelect&&d.clickToSelect&&(u=r.find(e('[name="%s"]',a.options.selectItemName))).length&&u[0].click())}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(){var n=t(this),i=n.parent().parent(),s=i.data("index"),l=r[s];return i.next().is("tr.detail-view")?(n.find("i").attr("class",e("%s %s",a.options.iconsPrefix,a.options.icons.detailOpen)),a.trigger("collapse-row",s,l),i.next().remove()):(n.find("i").attr("class",e("%s %s",a.options.iconsPrefix,a.options.icons.detailClose)),i.after(e('<tr class="detail-view"><td colspan="%s"></td></tr>',i.find("td").length)),n=i.next().find("td"),i=o(a.options,a.options.detailFormatter,[s,l,n],""),1===n.length&&n.append(i),a.trigger("expand-row",s,l,n)),a.resetView(),!1}),this.$selectItem=this.$body.find(e('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(e){e.stopImmediatePropagation();var e=t(this),n=e.prop("checked"),i=a.data[e.data("index")];a.options.maintainSelected&&t(this).is(":radio")&&t.each(a.options.data,function(t,e){e[a.header.stateField]=!1}),i[a.header.stateField]=n,a.options.singleSelect&&(a.$selectItem.not(this).each(function(){a.data[t(this).data("index")][a.header.stateField]=!1}),a.$selectItem.filter(":checked").not(this).prop("checked",!1)),a.updateSelected(),a.trigger(n?"check":"uncheck",i,e)}),t.each(this.header.events,function(e,n){if(n){"string"==typeof n&&(n=o(null,n));var i,r=a.header.fields[e],s=t.inArray(r,a.getVisibleFields());for(i in a.options.detailView&&!a.options.cardView&&(s+=1),n)a.$body.find(">tr:not(.no-records-found)").each(function(){var e=t(this),o=e.find(a.options.cardView?".card-view":">td").eq(s),l=i.indexOf(" "),c=i.substring(0,l),l=i.substring(l+1),d=n[i];o.find(l).off(c).on(c,function(t){var n=e.data("index"),i=a.data[n],o=i[r],s=r.split(".");if(1<s.length)for(var o=i,l=0;l<s.length;l++)o=o[s[l]];d.apply(this,[t,o,i,n])})})}}),this.updateSelected(),this.resetView(),this.trigger("post-body",r)},d.prototype.initServer=function(e,n,i){var a=this,r={},s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};this.options.pagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),(i||this.options.url||this.options.ajax)&&("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination)&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize),t.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),r=o(this.options,this.options.queryParams,[s],r),t.extend(r,n||{}),!1!==r)&&(e||this.$tableLoading.show(),s=t.extend({},o(null,this.options.ajaxOptions),{type:this.options.method,url:i||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(r):r,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t){t=o(a.options,a.options.responseHandler,[t],t),a.load(t),a.trigger("load-success",t),e||a.$tableLoading.hide()},error:function(t){a.trigger("load-error",t.status,t),e||a.$tableLoading.hide()}}),this.options.ajax?o(this,this.options.ajax,[s],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=t.ajax(s)))},d.prototype.initSearchText=function(){var t;this.options.search&&""!==this.options.searchText&&((t=this.$toolbar.find(".search input")).val(this.options.searchText),this.onSearch({currentTarget:t}))},d.prototype.getCaret=function(){var e=this;t.each(this.$header.find("th"),function(n,i){t(i).find(".sortable").removeClass("desc asc").addClass(t(i).data("field")===e.options.sortName?e.options.sortOrder:"both")})},d.prototype.updateSelected=function(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e),this.$selectItem.each(function(){t(this).closest("tr")[t(this).prop("checked")?"addClass":"removeClass"]("selected")})},d.prototype.updateRows=function(){var e=this;this.$selectItem.each(function(){e.data[t(this).data("index")][e.header.stateField]=t(this).prop("checked")})},d.prototype.resetRows=function(){var e=this;t.each(this.data,function(t,n){e.$selectAll.prop("checked",!1),e.$selectItem.prop("checked",!1),e.header.stateField&&(n[e.header.stateField]=!1)}),this.initHiddenRows()},d.prototype.trigger=function(e){var n=Array.prototype.slice.call(arguments,1);this.options[d.EVENTS[e+=".bs.table"]].apply(this.options,n),this.$el.trigger(t.Event(e),n),this.options.onAll(e,n),this.$el.trigger(t.Event("all.bs.table"),[e,n])},d.prototype.resetHeader=function(){clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(t.proxy(this.fitHeader,this),this.$el.is(":hidden")?100:0)},d.prototype.fitHeader=function(){var n,o,a,r,s=this;s.$el.is(":hidden")?s.timeoutId_=setTimeout(t.proxy(s.fitHeader,s),100):(o=(o=this.$tableBody.get(0)).scrollWidth>o.clientWidth&&o.scrollHeight>o.clientHeight+this.$header.outerHeight()?i():0,this.$el.css("margin-top",-this.$header.outerHeight()),0<(n=t(":focus")).length&&0<(n=n.parents("th")).length&&void 0!==(n=n.attr("data-field"))&&0<(n=this.$header.find("[data-field='"+n+"']")).length&&n.find(":input").addClass("focus-temp"),this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css({"margin-right":o}).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),0<(n=t(".focus-temp:visible:eq(0)")).length&&(n.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(n){s.$header_.find(e('th[data-field="%s"]',t(this).data("field"))).data(t(this).data())}),a=this.getVisibleFields(),r=this.$header_.find("th"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(n){var i=t(this),o=n,n=(s.options.detailView&&!s.options.cardView&&(0===n&&s.$header_.find("th.detail").find(".fht-cell").width(i.innerWidth()),o=n-1),s.$header_.find(e('th[data-field="%s"]',a[o])));(n=1<n.length?t(r[i[0].cellIndex]):n).find(".fht-cell").width(i.innerWidth())}),this.$tableBody.off("scroll").on("scroll",function(){s.$tableHeader.scrollLeft(t(this).scrollLeft()),s.options.showFooter&&!s.options.cardView&&s.$tableFooter.scrollLeft(t(this).scrollLeft())}),s.trigger("post-header"))},d.prototype.resetFooter=function(){var n=this,i=n.getData(),a=[];this.options.showFooter&&!this.options.cardView&&(!this.options.cardView&&this.options.detailView&&a.push('<td><div class="th-inner">&nbsp;</div><div class="fht-cell"></div></td>'),t.each(this.columns,function(t,r){var s,l,c,d,u=[],p=e(' class="%s"',r.class);if(r.visible&&(!n.options.cardView||r.cardVisible)){if(l=e("text-align: %s; ",r.falign||r.align),c=e("vertical-align: %s; ",r.valign),(d=o(null,n.options.footerStyle))&&d.css)for(s in d.css)u.push(s+": "+d.css[s]);a.push("<td",p,e(' style="%s"',l+c+u.concat().join("; ")),">"),a.push('<div class="th-inner">'),a.push(o(r,r.footerFormatter,[i],"&nbsp;")||"&nbsp;"),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</td>")}}),this.$tableFooter.find("tr").html(a.join("")),this.$tableFooter.show(),clearTimeout(this.timeoutFooter_),this.timeoutFooter_=setTimeout(t.proxy(this.fitFooter,this),this.$el.is(":hidden")?100:0))},d.prototype.fitFooter=function(){var e,n,o;clearTimeout(this.timeoutFooter_),this.$el.is(":hidden")?this.timeoutFooter_=setTimeout(t.proxy(this.fitFooter,this),100):(o=(n=this.$el.css("width"))>this.$tableBody.width()?i():0,this.$tableFooter.css({"margin-right":o}).find("table").css("width",n).attr("class",this.$el.attr("class")),e=this.$tableFooter.find("td"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(n){var i=t(this);e.eq(n).find(".fht-cell").width(i.innerWidth())}))},d.prototype.toggleColumn=function(t,n,i){var o;-1!==t&&(this.columns[t].visible=n,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)&&(o=this.$toolbar.find(".keep-open input").prop("disabled",!1),i&&o.filter(e('[value="%s"]',t)).prop("checked",n),o.filter(":checked").length<=this.options.minimumCountColumns)&&o.filter(":checked").prop("disabled",!0)},d.prototype.getVisibleFields=function(){var e=this,i=[];return t.each(this.header.fields,function(t,o){e.columns[n(e.columns,o)].visible&&i.push(o)}),i},d.prototype.resetView=function(t){var e,n=0;t&&t.height&&(this.options.height=t.height),this.$selectAll.prop("checked",0<this.$selectItem.length&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.options.height&&(t=this.$toolbar.outerHeight(!0),e=this.$pagination.outerHeight(!0),t=this.options.height-t-e,this.$tableContainer.css("height",t+"px")),this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),n+=this.$header.outerHeight()):(this.$tableHeader.hide(),this.trigger("post-header")),this.options.showFooter&&(this.resetFooter(),this.options.height)&&(n+=this.$tableFooter.outerHeight()+1),this.getCaret(),this.$tableContainer.css("padding-bottom",n+"px"),this.trigger("reset-view"))},d.prototype.getData=function(e){return!this.searchText&&t.isEmptyObject(this.filterColumns)&&t.isEmptyObject(this.filterColumnsPartial)?e?this.options.data.slice(this.pageFrom-1,this.pageTo):this.options.data:e?this.data.slice(this.pageFrom-1,this.pageTo):this.data},d.prototype.load=function(e){var n=!1;"server"===this.options.sidePagination?(this.options.totalRows=e[this.options.totalField],n=e.fixedScroll,e=e[this.options.dataField]):t.isArray(e)||(n=e.fixedScroll,e=e.data),this.initData(e),this.initSearch(),this.initPagination(),this.initBody(n)},d.prototype.append=function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.prepend=function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.remove=function(e){var n,i,o=this.options.data.length;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){for(n=o-1;0<=n;n--)(i=this.options.data[n]).hasOwnProperty(e.field)&&-1!==t.inArray(i[e.field],e.values)&&(this.options.data.splice(n,1),"server"===this.options.sidePagination)&&--this.options.totalRows;o!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},d.prototype.removeAll=function(){0<this.options.data.length&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))},d.prototype.getRowByUniqueId=function(t){for(var e,n,i=this.options.uniqueId,o=null,a=this.options.data.length-1;0<=a;a--){if((e=this.options.data[a]).hasOwnProperty(i))n=e[i];else{if(!e._data.hasOwnProperty(i))continue;n=e._data[i]}if("string"==typeof n?t=t.toString():"number"==typeof n&&(Number(n)===n&&n%1==0?t=parseInt(t):n===Number(n)&&0!==n&&(t=parseFloat(t))),n===t){o=e;break}}return o},d.prototype.removeByUniqueId=function(t){var e=this.options.data.length,t=this.getRowByUniqueId(t);t&&this.options.data.splice(this.options.data.indexOf(t),1),e!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))},d.prototype.updateByUniqueId=function(e){var n=this,e=t.isArray(e)?e:[e];t.each(e,function(e,i){var o;i.hasOwnProperty("id")&&i.hasOwnProperty("row")&&-1!==(o=t.inArray(n.getRowByUniqueId(i.id),n.options.data))&&t.extend(n.options.data[o],i.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.insertRow=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))},d.prototype.updateRow=function(e){var n=this,e=t.isArray(e)?e:[e];t.each(e,function(e,i){i.hasOwnProperty("index")&&i.hasOwnProperty("row")&&t.extend(n.options.data[i.index],i.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.initHiddenRows=function(){this.hiddenRows=[]},d.prototype.showRow=function(t){this.toggleRow(t,!0)},d.prototype.hideRow=function(t){this.toggleRow(t,!1)},d.prototype.toggleRow=function(e,n){var i;e.hasOwnProperty("index")?i=this.getData()[e.index]:e.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(e.uniqueId)),i&&(e=t.inArray(i,this.hiddenRows),n||-1!==e?n&&-1<e&&this.hiddenRows.splice(e,1):this.hiddenRows.push(i),this.initBody(!0))},d.prototype.getHiddenRows=function(e){var n=this,i=this.getData(),o=[];return t.each(i,function(e,i){-1<t.inArray(i,n.hiddenRows)&&o.push(i)}),this.hiddenRows=o},d.prototype.mergeCells=function(e){var n,i,o=e.index,a=t.inArray(e.field,this.getVisibleFields()),r=e.rowspan||1,s=e.colspan||1,l=this.$body.find(">tr");if(this.options.detailView&&!this.options.cardView&&(a+=1),e=l.eq(o).find(">td").eq(a),!(o<0||a<0||o>=this.data.length)){for(n=o;n<o+r;n++)for(i=a;i<a+s;i++)l.eq(n).find(">td").eq(i).hide();e.attr("rowspan",r).attr("colspan",s).show()}},d.prototype.updateCell=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,!1!==t.reinit)&&(this.initSort(),this.initBody(!0))},d.prototype.getOptions=function(){return this.options},d.prototype.getSelections=function(){var e=this;return t.grep(this.options.data,function(t){return!0===t[e.header.stateField]})},d.prototype.getAllSelections=function(){var e=this;return t.grep(this.options.data,function(t){return t[e.header.stateField]})},d.prototype.checkAll=function(){this.checkAll_(!0)},d.prototype.uncheckAll=function(){this.checkAll_(!1)},d.prototype.checkInvert=function(){var e=this,n=e.$selectItem.filter(":enabled"),i=n.filter(":checked");n.each(function(){t(this).prop("checked",!t(this).prop("checked"))}),e.updateRows(),e.updateSelected(),e.trigger("uncheck-some",i),i=e.getSelections(),e.trigger("check-some",i)},d.prototype.checkAll_=function(t){var e;t||(e=this.getSelections()),this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),t&&(e=this.getSelections()),this.trigger(t?"check-all":"uncheck-all",e)},d.prototype.check=function(t){this.check_(!0,t)},d.prototype.uncheck=function(t){this.check_(!1,t)},d.prototype.check_=function(t,n){var i=this.$selectItem.filter(e('[data-index="%s"]',n)).prop("checked",t);this.data[n][this.header.stateField]=t,this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[n],i)},d.prototype.checkBy=function(t){this.checkBy_(!0,t)},d.prototype.uncheckBy=function(t){this.checkBy_(!1,t)},d.prototype.checkBy_=function(n,i){var o,a;i.hasOwnProperty("field")&&i.hasOwnProperty("values")&&(a=[],t.each((o=this).options.data,function(r,s){return!!s.hasOwnProperty(i.field)&&void(-1!==t.inArray(s[i.field],i.values)&&(r=o.$selectItem.filter(":enabled").filter(e('[data-index="%s"]',r)).prop("checked",n),s[o.header.stateField]=n,a.push(s),o.trigger(n?"check":"uncheck",s,r)))}),this.updateSelected(),this.trigger(n?"check-some":"uncheck-some",a))},d.prototype.destroy=function(){this.$el.insertBefore(this.$container),t(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")},d.prototype.showLoading=function(){this.$tableLoading.show()},d.prototype.hideLoading=function(){this.$tableLoading.hide()},d.prototype.togglePagination=function(){this.options.pagination=!this.options.pagination;var t=this.$toolbar.find('button[name="paginationSwitch"] i');this.options.pagination?t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchDown):t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchUp),this.updatePagination()},d.prototype.refresh=function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.initServer(t&&t.silent,t&&t.query,t&&t.url),this.trigger("refresh",t)},d.prototype.resetWidth=function(){this.options.showHeader&&this.options.height&&this.fitHeader(),this.options.showFooter&&this.fitFooter()},d.prototype.showColumn=function(t){this.toggleColumn(n(this.columns,t),!0,!0)},d.prototype.hideColumn=function(t){this.toggleColumn(n(this.columns,t),!1,!0)},d.prototype.getHiddenColumns=function(){return t.grep(this.columns,function(t){return!t.visible})},d.prototype.getVisibleColumns=function(){return t.grep(this.columns,function(t){return t.visible})},d.prototype.toggleAllColumns=function(e){var n;t.each(this.columns,function(t,n){this.columns[t].visible=e}),this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns&&(n=this.$toolbar.find(".keep-open input").prop("disabled",!1)).filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)},d.prototype.showAllColumns=function(){this.toggleAllColumns(!0)},d.prototype.hideAllColumns=function(){this.toggleAllColumns(!1)},d.prototype.filterBy=function(e){this.filterColumns=t.isEmptyObject(e)?{}:e,this.options.pageNumber=1,this.initSearch(),this.updatePagination()},d.prototype.scrollTo=function(t){if("number"==typeof(t="string"==typeof t?"bottom"===t?this.$tableBody[0].scrollHeight:0:t)&&this.$tableBody.scrollTop(t),void 0===t)return this.$tableBody.scrollTop()},d.prototype.getScrollPosition=function(){return this.scrollTo()},d.prototype.selectPage=function(t){0<t&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())},d.prototype.prevPage=function(){1<this.options.pageNumber&&(this.options.pageNumber--,this.updatePagination())},d.prototype.nextPage=function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())},d.prototype.toggleView=function(){this.options.cardView=!this.options.cardView,this.initHeader(),this.initBody(),this.trigger("toggle",this.options.cardView)},d.prototype.refreshOptions=function(e){a(this.options,e,!0)||(this.options=t.extend(this.options,e),this.trigger("refresh-options",this.options),this.destroy(),this.init())},d.prototype.resetSearch=function(t){var e=this.$toolbar.find(".search input");e.val(t||""),this.onSearch({currentTarget:e})},d.prototype.expandRow_=function(t,n){n=this.$body.find(e('> tr[data-index="%s"]',n)),n.next().is("tr.detail-view")===!t&&n.find("> td > .detail-icon").click()},d.prototype.expandRow=function(t){this.expandRow_(!0,t)},d.prototype.collapseRow=function(t){this.expandRow_(!1,t)},d.prototype.expandAllRows=function(n){if(n){var n=this.$body.find(e('> tr[data-index="%s"]',0)),i=this,o=null,a=!1,r=-1;if(n.next().is("tr.detail-view")?n.next().next().is("tr.detail-view")||(n.next().find(".detail-icon").click(),a=!0):(n.find("> td > .detail-icon").click(),a=!0),a)try{r=setInterval(function(){0<(o=i.$body.find("tr.detail-view").last().find(".detail-icon")).length?o.click():clearInterval(r)},1)}catch(t){clearInterval(r)}}else for(var s=this.$body.children(),l=0;l<s.length;l++)this.expandRow_(!0,t(s[l]).data("index"))},d.prototype.collapseAllRows=function(e){if(e)this.expandRow_(!1,0);else for(var n=this.$body.children(),i=0;i<n.length;i++)this.expandRow_(!1,t(n[i]).data("index"))},d.prototype.updateFormatText=function(t,n){this.options[e("format%s",t)]&&("string"==typeof n?this.options[e("format%s",t)]=function(){
return n}:"function"==typeof n&&(this.options[e("format%s",t)]=n)),this.initToolbar(),this.initPagination(),this.initBody()},["getOptions","getSelections","getAllSelections","getData","load","append","prepend","remove","removeAll","insertRow","updateRow","updateCell","updateByUniqueId","removeByUniqueId","getRowByUniqueId","showRow","hideRow","getHiddenRows","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","resetView","resetWidth","destroy","showLoading","hideLoading","showColumn","hideColumn","getHiddenColumns","getVisibleColumns","showAllColumns","hideAllColumns","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","togglePagination","toggleView","refreshOptions","resetSearch","expandRow","collapseRow","expandAllRows","collapseAllRows","updateFormatText"]);t.fn.bootstrapTable=function(e){var n,i=Array.prototype.slice.call(arguments,1);return this.each(function(){var o=t(this),a=o.data("bootstrap.table"),r=t.extend({},d.DEFAULTS,o.data(),"object"==typeof e&&e);if("string"==typeof e){if(t.inArray(e,p)<0)throw new Error("Unknown method: "+e);if(!a)return;n=a[e].apply(a,i),"destroy"===e&&o.removeData("bootstrap.table")}a||o.data("bootstrap.table",a=new d(this,r))}),void 0===n?this:n},t.fn.bootstrapTable.Constructor=d,t.fn.bootstrapTable.defaults=d.DEFAULTS,t.fn.bootstrapTable.columnDefaults=d.COLUMN_DEFAULTS,t.fn.bootstrapTable.locales=d.LOCALES,t.fn.bootstrapTable.methods=p,t.fn.bootstrapTable.utils={sprintf:e,getFieldIndex:n,compareObjects:a,calculateObjectValue:o,getItemField:l,objectKeys:function(){var t,e,n,i;Object.keys||(Object.keys=(t=Object.prototype.hasOwnProperty,e=!{toString:null}.propertyIsEnumerable("toString"),i=(n=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"]).length,function(o){if("object"!=typeof o&&("function"!=typeof o||null===o))throw new TypeError("Object.keys called on non-object");var a,r,s=[];for(a in o)t.call(o,a)&&s.push(a);if(e)for(r=0;r<i;r++)t.call(o,n[r])&&s.push(n[r]);return s}))},isIEBrowser:c},t(function(){t('[data-toggle="table"]').bootstrapTable()})}(jQuery),define("bootstrap-table",["bootstrap"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable}}(this)),function(t){"use strict";t.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候……"},formatRecordsPerPage:function(t){return"每页显示 "+t+" 条记录"},formatShowingRows:function(t,e,n){return"显示第 "+t+" 到第 "+e+" 条记录，总共 "+n+" 条记录"},formatDetailPagination:function(t){return"总共 "+t+" 条记录"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatColumns:function(){return"列"},formatExport:function(){return"导出数据"},formatClearFilters:function(){return"清空过滤"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-CN"])}(jQuery),define("bootstrap-table-lang",["bootstrap-table"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable.defaults}}(this)),!function(t){"use strict";var e=t.fn.bootstrapTable.utils.sprintf,n={json:"JSON",xml:"XML",png:"PNG",csv:"CSV",txt:"TXT",sql:"SQL",doc:"MS-Word",excel:"MS-Excel",xlsx:"MS-Excel (OpenXML)",powerpoint:"MS-Powerpoint",pdf:"PDF"},i=(t.extend(t.fn.bootstrapTable.defaults,{showExport:!1,exportDataType:"basic",exportTypes:["json","xml","csv","txt","sql","excel"],exportOptions:{}}),t.extend(t.fn.bootstrapTable.defaults.icons,{export:"glyphicon-export icon-share"}),t.extend(t.fn.bootstrapTable.locales,{formatExport:function(){return"Export data"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales),t.fn.bootstrapTable.Constructor),o=i.prototype.initToolbar;i.prototype.initToolbar=function(){var i,a,r,s;this.showToolbar=this.options.showExport,o.apply(this,Array.prototype.slice.apply(arguments)),this.options.showExport&&!(r=(i=this).$toolbar.find(">.btn-group")).find("div.export").length&&(a=t(['<div class="export btn-group">','<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" aria-label="export type" title="'+this.options.formatExport()+'" data-toggle="dropdown" type="button">',e('<i class="%s %s"></i> ',this.options.iconsPrefix,this.options.icons.export),'<span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">',"</ul>","</div>"].join("")).appendTo(r).find(".dropdown-menu"),s=this.options.exportTypes,"string"==typeof this.options.exportTypes&&(r=this.options.exportTypes.slice(1,-1).replace(/ /g,"").split(","),s=[],t.each(r,function(t,e){s.push(e.slice(1,-1))})),t.each(s,function(t,e){n.hasOwnProperty(e)&&a.append(['<li role="menuitem" data-type="'+e+'">','<a href="javascript:void(0)">',n[e],"</a>","</li>"].join(""))}),a.find("li").click(function(){var e=this;if("function"!=typeof require)throw new Error("RequireJS not found");require(["tableexport"],function(){function n(){i.$el.tableExport(t.extend({},i.options.exportOptions,{type:s,escape:!1}))}var o,a,r,s=t(e).data("type");"all"===i.options.exportDataType&&i.options.pagination?(i.$el.one("server"===i.options.sidePagination?"post-body.bs.table":"page-change.bs.table",function(){n(),i.togglePagination()}),i.togglePagination()):"selected"===i.options.exportDataType?(o=i.getData(),r=i.getAllSelections(),"server"===i.options.sidePagination&&((o={total:i.options.totalRows})[i.options.dataField]=i.getData(),a="function"==typeof require?require("table"):null,(r={total:i.options.totalRows})[i.options.dataField]=a&&i.options.maintainSelected?a.api.selecteddata(i.$el):i.getAllSelections()),i.load(r),n(),i.load(o)):n()})}))}}(jQuery),define("bootstrap-table-export",["bootstrap-table"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable.defaults}}(this)),function(t){"function"==typeof define&&define.amd?define("dropzone",["jquery"],t):t(jQuery)}(function(t){function e(t){"@babel/helpers - typeof";return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,n){return!n||"object"!==e(n)&&"function"!=typeof n?o(t):n}function i(t){return(i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function o(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&r(t,e)}function r(t,e){return(r=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function c(t,e,n){return e&&l(t.prototype,e),n&&l(t,n),t}function d(t,e){return"undefined"!=typeof t&&null!==t?e(t):void 0}function u(t,e,n){return"undefined"!=typeof t&&null!==t&&"function"==typeof t[e]?n(t,e):void 0}var p={exports:{}},h=function(){function t(){s(this,t)}return c(t,[{key:"on",value:function(t,e){return this._callbacks=this._callbacks||{},this._callbacks[t]||(this._callbacks[t]=[]),this._callbacks[t].push(e),this}},{key:"emit",value:function(t){this._callbacks=this._callbacks||{};var e=this._callbacks[t];if(e){for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];var a=!0,r=!1,s=void 0;try{for(var l,c=e[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){var d=l.value;d.apply(this,i)}}catch(t){r=!0,s=t}finally{try{a||null==c.return||c.return()}finally{if(r)throw s}}}return this}},{key:"off",value:function(t,e){if(!this._callbacks||0===arguments.length)return this._callbacks={},this;var n=this._callbacks[t];if(!n)return this;if(1===arguments.length)return delete this._callbacks[t],this;for(var i=0;i<n.length;i++){var o=n[i];if(o===e){n.splice(i,1);break}}return this}}]),t}(),f=function(t){function e(t,a){var r;s(this,e),r=n(this,i(e).call(this));var l,c;if(r.element=t,r.version=e.version,r.defaultOptions.previewTemplate=r.defaultOptions.previewTemplate.replace(/\n*/g,""),r.clickableElements=[],r.listeners=[],r.files=[],"string"==typeof r.element&&(r.element=document.querySelector(r.element)),!r.element||null==r.element.nodeType)throw new Error("Invalid dropzone element.");if(r.element.dropzone)throw new Error("Dropzone already attached.");e.instances.push(o(r)),r.element.dropzone=o(r);var d=null!=(c=e.optionsForElement(r.element))?c:{};if(r.options=e.extend({},r.defaultOptions,d,null!=a?a:{}),r.options.forceFallback||!e.isBrowserSupported())return n(r,r.options.fallback.call(o(r)));if(null==r.options.url&&(r.options.url=r.element.getAttribute("action")),!r.options.url)throw new Error("No URL provided.");if(r.options.acceptedFiles&&r.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(r.options.uploadMultiple&&r.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");return r.options.acceptedMimeTypes&&(r.options.acceptedFiles=r.options.acceptedMimeTypes,delete r.options.acceptedMimeTypes),null!=r.options.renameFilename&&(r.options.renameFile=function(t){return r.options.renameFilename.call(o(r),t.name,t)}),r.options.method="function"!=typeof r.options.method?r.options.method.toUpperCase():r.options.method,(l=r.getExistingFallback())&&l.parentNode&&l.parentNode.removeChild(l),r.options.previewsContainer!==!1&&(r.options.previewsContainer?r.previewsContainer=e.getElement(r.options.previewsContainer,"previewsContainer"):r.previewsContainer=r.element),r.options.clickable&&(r.options.clickable===!0?r.clickableElements=[r.element]:r.clickableElements=e.getElements(r.options.clickable,"clickable")),r.init(),r}return a(e,t),c(e,null,[{key:"initClass",value:function(){this.prototype.Emitter=h,this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"],this.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,timeout:3e4,parallelUploads:2,uploadMultiple:!1,chunking:!1,forceChunking:!1,chunkSize:2e6,parallelChunkUploads:!1,retryChunks:!1,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,autoQueue:!0,addRemoveLinks:!1,previewsContainer:null,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:!1,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function(){},params:function(t,e,n){if(n)return{dzuuid:n.file.upload.uuid,dzchunkindex:n.index,dztotalfilesize:n.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:n.file.upload.totalChunkCount,dzchunkbyteoffset:n.index*this.options.chunkSize}},accept:function(t,e){return e()},chunkSuccess:function(t,e,n){},chunksUploaded:function(t,e){e()},fallback:function(){var t;this.element.className="".concat(this.element.className," dz-browser-not-supported");var n=!0,i=!1,o=void 0;try{for(var a,r=this.element.getElementsByTagName("div")[Symbol.iterator]();!(n=(a=r.next()).done);n=!0){var s=a.value;if(/(^| )dz-message($| )/.test(s.className)){t=s,s.className="dz-message";break}}}catch(t){i=!0,o=t}finally{try{n||null==r.return||r.return()}finally{if(i)throw o}}t||(t=e.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(t));var l=t.getElementsByTagName("span")[0];return l&&(null!=l.textContent?l.textContent=this.options.dictFallbackMessage:null!=l.innerText&&(l.innerText=this.options.dictFallbackMessage)),this.element.appendChild(this.getFallbackForm())},resize:function(t,e,n,i){var o={srcX:0,srcY:0,srcWidth:t.width,srcHeight:t.height},a=t.width/t.height;null==e&&null==n?(e=o.srcWidth,n=o.srcHeight):null==e?e=n*a:null==n&&(n=e/a),e=Math.min(e,o.srcWidth),n=Math.min(n,o.srcHeight);var r=e/n;if(o.srcWidth>e||o.srcHeight>n)if("crop"===i)a>r?(o.srcHeight=t.height,o.srcWidth=o.srcHeight*r):(o.srcWidth=t.width,o.srcHeight=o.srcWidth/r);else{if("contain"!==i)throw new Error("Unknown resizeMethod '".concat(i,"'"));a>r?n=e/a:e=n*a}return o.srcX=(t.width-o.srcWidth)/2,o.srcY=(t.height-o.srcHeight)/2,o.trgWidth=e,o.trgHeight=n,o},transformFile:function(t,e){return(this.options.resizeWidth||this.options.resizeHeight)&&t.type.match(/image.*/)?this.resizeImage(t,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,e):e(t)},previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail /></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size></span></div>\n    <div class="dz-filename"><span data-dz-name></span></div>\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n  <div class="dz-success-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Check</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475" fill="#FFFFFF"></path>\n      </g>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Error</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g stroke="#747474" stroke-opacity="0.198794158" fill="#FFFFFF" fill-opacity="0.816519475">\n          <path d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>',drop:function(t){return this.element.classList.remove("dz-drag-hover")},dragstart:function(t){},dragend:function(t){return this.element.classList.remove("dz-drag-hover")},dragenter:function(t){return this.element.classList.add("dz-drag-hover")},dragover:function(t){return this.element.classList.add("dz-drag-hover")},dragleave:function(t){return this.element.classList.remove("dz-drag-hover")},paste:function(t){},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(t){var n=this;if(this.element===this.previewsContainer&&this.element.classList.add("dz-started"),this.previewsContainer){t.previewElement=e.createElement(this.options.previewTemplate.trim()),t.previewTemplate=t.previewElement,this.previewsContainer.appendChild(t.previewElement);var i=!0,o=!1,a=void 0;try{for(var r,s=t.previewElement.querySelectorAll("[data-dz-name]")[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value;l.textContent=t.name}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}var c=!0,d=!1,u=void 0;try{for(var p,h=t.previewElement.querySelectorAll("[data-dz-size]")[Symbol.iterator]();!(c=(p=h.next()).done);c=!0)l=p.value,l.innerHTML=this.filesize(t.size)}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}this.options.addRemoveLinks&&(t._removeLink=e.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'.concat(this.options.dictRemoveFile,"</a>")),t.previewElement.appendChild(t._removeLink));var f=function(i){return i.preventDefault(),i.stopPropagation(),t.status===e.UPLOADING?e.confirm(n.options.dictCancelUploadConfirmation,function(){return n.removeFile(t)}):n.options.dictRemoveFileConfirmation?e.confirm(n.options.dictRemoveFileConfirmation,function(){return n.removeFile(t)}):n.removeFile(t)},m=!0,g=!1,v=void 0;try{for(var y,b=t.previewElement.querySelectorAll("[data-dz-remove]")[Symbol.iterator]();!(m=(y=b.next()).done);m=!0){var x=y.value;x.addEventListener("click",f)}}catch(t){g=!0,v=t}finally{try{m||null==b.return||b.return()}finally{if(g)throw v}}}},removedfile:function(t){return null!=t.previewElement&&null!=t.previewElement.parentNode&&t.previewElement.parentNode.removeChild(t.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function(t,e){if(t.previewElement){t.previewElement.classList.remove("dz-file-preview");var n=!0,i=!1,o=void 0;try{for(var a,r=t.previewElement.querySelectorAll("[data-dz-thumbnail]")[Symbol.iterator]();!(n=(a=r.next()).done);n=!0){var s=a.value;s.alt=t.name,s.src=e}}catch(t){i=!0,o=t}finally{try{n||null==r.return||r.return()}finally{if(i)throw o}}return setTimeout(function(){return t.previewElement.classList.add("dz-image-preview")},1)}},error:function(t,e){if(t.previewElement){t.previewElement.classList.add("dz-error"),"String"!=typeof e&&e.error&&(e=e.error);var n=!0,i=!1,o=void 0;try{for(var a,r=t.previewElement.querySelectorAll("[data-dz-errormessage]")[Symbol.iterator]();!(n=(a=r.next()).done);n=!0){var s=a.value;s.textContent=e}}catch(t){i=!0,o=t}finally{try{n||null==r.return||r.return()}finally{if(i)throw o}}}},errormultiple:function(){},processing:function(t){if(t.previewElement&&(t.previewElement.classList.add("dz-processing"),t._removeLink))return t._removeLink.innerHTML=this.options.dictCancelUpload},processingmultiple:function(){},uploadprogress:function(t,e,n){if(t.previewElement){var i=!0,o=!1,a=void 0;try{for(var r,s=t.previewElement.querySelectorAll("[data-dz-uploadprogress]")[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value;"PROGRESS"===l.nodeName?l.value=e:l.style.width="".concat(e,"%")}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}}},totaluploadprogress:function(){},sending:function(){},sendingmultiple:function(){},success:function(t){if(t.previewElement)return t.previewElement.classList.add("dz-success")},successmultiple:function(){},canceled:function(t){return this.emit("error",t,this.options.dictUploadCanceled)},canceledmultiple:function(){},complete:function(t){if(t._removeLink&&(t._removeLink.innerHTML=this.options.dictRemoveFile),t.previewElement)return t.previewElement.classList.add("dz-complete")},completemultiple:function(){},maxfilesexceeded:function(){},maxfilesreached:function(){},queuecomplete:function(){},addedfiles:function(){}},this.prototype._thumbnailQueue=[],this.prototype._processingThumbnail=!1}},{key:"extend",value:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];for(var o=0,a=n;o<a.length;o++){var r=a[o];for(var s in r){var l=r[s];t[s]=l}}return t}}]),c(e,[{key:"getAcceptedFiles",value:function(){return this.files.filter(function(t){return t.accepted}).map(function(t){return t})}},{key:"getRejectedFiles",value:function(){return this.files.filter(function(t){return!t.accepted}).map(function(t){return t})}},{key:"getFilesWithStatus",value:function(t){return this.files.filter(function(e){return e.status===t}).map(function(t){return t})}},{key:"getQueuedFiles",value:function(){return this.getFilesWithStatus(e.QUEUED)}},{key:"getUploadingFiles",value:function(){return this.getFilesWithStatus(e.UPLOADING)}},{key:"getAddedFiles",value:function(){return this.getFilesWithStatus(e.ADDED)}},{key:"getActiveFiles",value:function(){return this.files.filter(function(t){return t.status===e.UPLOADING||t.status===e.QUEUED}).map(function(t){return t})}},{key:"init",value:function(){var t=this;if("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(e.createElement('<div class="dz-default dz-message"><button class="dz-button" type="button">'.concat(this.options.dictDefaultMessage,"</button></div>"))),this.clickableElements.length){var n=function n(){return t.hiddenFileInput&&t.hiddenFileInput.parentNode.removeChild(t.hiddenFileInput),t.hiddenFileInput=document.createElement("input"),t.hiddenFileInput.setAttribute("type","file"),(null===t.options.maxFiles||t.options.maxFiles>1)&&t.hiddenFileInput.setAttribute("multiple","multiple"),t.hiddenFileInput.className="dz-hidden-input",null!==t.options.acceptedFiles&&t.hiddenFileInput.setAttribute("accept",t.options.acceptedFiles),null!==t.options.capture&&t.hiddenFileInput.setAttribute("capture",t.options.capture),t.hiddenFileInput.style.visibility="hidden",t.hiddenFileInput.style.position="absolute",t.hiddenFileInput.style.top="0",t.hiddenFileInput.style.left="0",t.hiddenFileInput.style.height="0",t.hiddenFileInput.style.width="0",e.getElement(t.options.hiddenInputContainer,"hiddenInputContainer").appendChild(t.hiddenFileInput),t.hiddenFileInput.addEventListener("change",function(){var e=t.hiddenFileInput.files;if(e.length){var i=!0,o=!1,a=void 0;try{for(var r,s=e[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value;t.addFile(l)}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}}return t.emit("addedfiles",e),n()})};n()}this.URL=null!==window.URL?window.URL:window.webkitURL;var i=!0,o=!1,a=void 0;try{for(var r,s=this.events[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value;this.on(l,this.options[l])}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}this.on("uploadprogress",function(){return t.updateTotalUploadProgress()}),this.on("removedfile",function(){return t.updateTotalUploadProgress()}),this.on("canceled",function(e){return t.emit("complete",e)}),this.on("complete",function(e){if(0===t.getAddedFiles().length&&0===t.getUploadingFiles().length&&0===t.getQueuedFiles().length)return setTimeout(function(){return t.emit("queuecomplete")},0)});var c=function(t){return t.dataTransfer.types&&t.dataTransfer.types.some(function(t){return"Files"==t})},d=function(t){if(c(t))return t.stopPropagation(),t.preventDefault?t.preventDefault():t.returnValue=!1};return this.listeners=[{element:this.element,events:{dragstart:function(e){return t.emit("dragstart",e)},dragenter:function(e){return d(e),t.emit("dragenter",e)},dragover:function(e){var n;try{n=e.dataTransfer.effectAllowed}catch(t){}return e.dataTransfer.dropEffect="move"===n||"linkMove"===n?"move":"copy",d(e),t.emit("dragover",e)},dragleave:function(e){return t.emit("dragleave",e)},drop:function(e){return d(e),t.drop(e)},dragend:function(e){return t.emit("dragend",e)}}}],this.clickableElements.forEach(function(n){return t.listeners.push({element:n,events:{click:function(i){return(n!==t.element||i.target===t.element||e.elementInside(i.target,t.element.querySelector(".dz-message")))&&t.hiddenFileInput.click(),!0}}})}),this.enable(),this.options.init.call(this)}},{key:"destroy",value:function(){return this.disable(),this.removeAllFiles(!0),(null!=this.hiddenFileInput?this.hiddenFileInput.parentNode:void 0)&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone,e.instances.splice(e.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function(){var t,e=0,n=0,i=this.getActiveFiles();if(i.length){var o=!0,a=!1,r=void 0;try{for(var s,l=this.getActiveFiles()[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;e+=c.upload.bytesSent,n+=c.upload.total}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}t=100*e/n}else t=100;return this.emit("totaluploadprogress",t,n,e)}},{key:"_getParamName",value:function(t){return"function"==typeof this.options.paramName?this.options.paramName(t):"".concat(this.options.paramName).concat(this.options.uploadMultiple?"[".concat(t,"]"):"")}},{key:"_renameFile",value:function(t){return"function"!=typeof this.options.renameFile?t.name:this.options.renameFile(t)}},{key:"getFallbackForm",value:function(){var t,n;if(t=this.getExistingFallback())return t;var i='<div class="dz-fallback">';this.options.dictFallbackText&&(i+="<p>".concat(this.options.dictFallbackText,"</p>")),i+='<input type="file" name="'.concat(this._getParamName(0),'" ').concat(this.options.uploadMultiple?'multiple="multiple"':void 0,' /><input type="submit" value="Upload!"></div>');var o=e.createElement(i);return"FORM"!==this.element.tagName?(n=e.createElement('<form action="'.concat(this.options.url,'" enctype="multipart/form-data" method="').concat(this.options.method,'"></form>')),n.appendChild(o)):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=n?n:o}},{key:"getExistingFallback",value:function(){for(var t=function(t){var e=!0,n=!1,i=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;if(/(^| )fallback($| )/.test(r.className))return r}}catch(t){n=!0,i=t}finally{try{e||null==a.return||a.return()}finally{if(n)throw i}}},e=0,n=["div","form"];e<n.length;e++){var i,o=n[e];if(i=t(this.element.getElementsByTagName(o)))return i}}},{key:"setupEventListeners",value:function(){return this.listeners.map(function(t){return function(){var e=[];for(var n in t.events){var i=t.events[n];e.push(t.element.addEventListener(n,i,!1))}return e}()})}},{key:"removeEventListeners",value:function(){return this.listeners.map(function(t){return function(){var e=[];for(var n in t.events){var i=t.events[n];e.push(t.element.removeEventListener(n,i,!1))}return e}()})}},{key:"disable",value:function(){var t=this;return this.clickableElements.forEach(function(t){return t.classList.remove("dz-clickable")}),this.removeEventListeners(),this.disabled=!0,this.files.map(function(e){return t.cancelUpload(e)})}},{key:"enable",value:function(){return delete this.disabled,this.clickableElements.forEach(function(t){return t.classList.add("dz-clickable")}),this.setupEventListeners()}},{key:"filesize",value:function(t){var e=0,n="b";if(t>0){for(var i=["tb","gb","mb","kb","b"],o=0;o<i.length;o++){var a=i[o],r=Math.pow(this.options.filesizeBase,4-o)/10;if(t>=r){e=t/Math.pow(this.options.filesizeBase,4-o),n=a;break}}e=Math.round(10*e)/10}return"<strong>".concat(e,"</strong> ").concat(this.options.dictFileSizeUnits[n])}},{key:"_updateMaxFilesReachedClass",value:function(){return null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(this.getAcceptedFiles().length===this.options.maxFiles&&this.emit("maxfilesreached",this.files),this.element.classList.add("dz-max-files-reached")):this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function(t){if(t.dataTransfer){this.emit("drop",t);for(var e=[],n=0;n<t.dataTransfer.files.length;n++)e[n]=t.dataTransfer.files[n];if(e.length){var i=t.dataTransfer.items;i&&i.length&&null!=i[0].webkitGetAsEntry?this._addFilesFromItems(i):this.handleFiles(e)}this.emit("addedfiles",e)}}},{key:"paste",value:function(t){if(null!=d(null!=t?t.clipboardData:void 0,function(t){return t.items})){this.emit("paste",t);var e=t.clipboardData.items;return e.length?this._addFilesFromItems(e):void 0}}},{key:"handleFiles",value:function(t){var e=!0,n=!1,i=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;this.addFile(r)}}catch(t){n=!0,i=t}finally{try{e||null==a.return||a.return()}finally{if(n)throw i}}}},{key:"_addFilesFromItems",value:function(t){var e=this;return function(){var n=[],i=!0,o=!1,a=void 0;try{for(var r,s=t[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l,c=r.value;null!=c.webkitGetAsEntry&&(l=c.webkitGetAsEntry())?l.isFile?n.push(e.addFile(c.getAsFile())):l.isDirectory?n.push(e._addFilesFromDirectory(l,l.name)):n.push(void 0):null!=c.getAsFile&&(null==c.kind||"file"===c.kind)?n.push(e.addFile(c.getAsFile())):n.push(void 0)}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}return n}()}},{key:"_addFilesFromDirectory",value:function(t,e){var n=this,i=t.createReader(),o=function(t){return u(console,"log",function(e){return e.log(t)})},a=function t(){return i.readEntries(function(i){if(i.length>0){var o=!0,a=!1,r=void 0;try{for(var s,l=i[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c.isFile?c.file(function(t){if(!n.options.ignoreHiddenFiles||"."!==t.name.substring(0,1))return t.fullPath="".concat(e,"/").concat(t.name),n.addFile(t)}):c.isDirectory&&n._addFilesFromDirectory(c,"".concat(e,"/").concat(c.name))}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}t()}return null},o)};return a()}},{key:"accept",value:function(t,n){this.options.maxFilesize&&t.size>1024*this.options.maxFilesize*1024?n(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(t.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):e.isValidFile(t,this.options.acceptedFiles)?null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(n(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",t)):this.options.accept.call(this,t,n):n(this.options.dictInvalidFileType)}},{key:"addFile",value:function(t){var n=this;t.upload={uuid:e.uuidv4(),progress:0,total:t.size,bytesSent:0,filename:this._renameFile(t)
},this.files.push(t),t.status=e.ADDED,this.emit("addedfile",t),this._enqueueThumbnail(t),this.accept(t,function(e){e?(t.accepted=!1,n._errorProcessing([t],e)):(t.accepted=!0,n.options.autoQueue&&n.enqueueFile(t)),n._updateMaxFilesReachedClass()})}},{key:"enqueueFiles",value:function(t){var e=!0,n=!1,i=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;this.enqueueFile(r)}}catch(t){n=!0,i=t}finally{try{e||null==a.return||a.return()}finally{if(n)throw i}}return null}},{key:"enqueueFile",value:function(t){var n=this;if(t.status!==e.ADDED||t.accepted!==!0)throw new Error("This file can't be queued because it has already been processed or was rejected.");if(t.status=e.QUEUED,this.options.autoProcessQueue)return setTimeout(function(){return n.processQueue()},0)}},{key:"_enqueueThumbnail",value:function(t){var e=this;if(this.options.createImageThumbnails&&t.type.match(/image.*/)&&t.size<=1024*this.options.maxThumbnailFilesize*1024)return this._thumbnailQueue.push(t),setTimeout(function(){return e._processThumbnailQueue()},0)}},{key:"_processThumbnailQueue",value:function(){var t=this;if(!this._processingThumbnail&&0!==this._thumbnailQueue.length){this._processingThumbnail=!0;var e=this._thumbnailQueue.shift();return this.createThumbnail(e,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,!0,function(n){return t.emit("thumbnail",e,n),t._processingThumbnail=!1,t._processThumbnailQueue()})}}},{key:"removeFile",value:function(t){if(t.status===e.UPLOADING&&this.cancelUpload(t),this.files=m(this.files,t),this.emit("removedfile",t),0===this.files.length)return this.emit("reset")}},{key:"removeAllFiles",value:function(t){null==t&&(t=!1);var n=!0,i=!1,o=void 0;try{for(var a,r=this.files.slice()[Symbol.iterator]();!(n=(a=r.next()).done);n=!0){var s=a.value;(s.status!==e.UPLOADING||t)&&this.removeFile(s)}}catch(t){i=!0,o=t}finally{try{n||null==r.return||r.return()}finally{if(i)throw o}}return null}},{key:"resizeImage",value:function(t,n,i,o,a){var r=this;return this.createThumbnail(t,n,i,o,!0,function(n,i){if(null==i)return a(t);var o=r.options.resizeMimeType;null==o&&(o=t.type);var s=i.toDataURL(o,r.options.resizeQuality);return"image/jpeg"!==o&&"image/jpg"!==o||(s=b.restore(t.dataURL,s)),a(e.dataURItoBlob(s))})}},{key:"createThumbnail",value:function(t,e,n,i,o,a){var r=this,s=new FileReader;s.onload=function(){return t.dataURL=s.result,"image/svg+xml"===t.type?void(null!=a&&a(s.result)):void r.createThumbnailFromUrl(t,e,n,i,o,a)},s.readAsDataURL(t)}},{key:"displayExistingFile",value:function(t,e,n,i){var o=this,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];if(this.emit("addedfile",t),this.emit("complete",t),a){var r=function(e){o.emit("thumbnail",t,e),n&&n()};t.dataURL=e,this.createThumbnailFromUrl(t,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.resizeMethod,this.options.fixOrientation,r,i)}else this.emit("thumbnail",t,e),n&&n()}},{key:"createThumbnailFromUrl",value:function(t,e,n,i,o,a,r){var s=this,l=document.createElement("img");return r&&(l.crossOrigin=r),l.onload=function(){var r=function(t){return t(1)};return"undefined"!=typeof EXIF&&null!==EXIF&&o&&(r=function(t){return EXIF.getData(l,function(){return t(EXIF.getTag(this,"Orientation"))})}),r(function(o){t.width=l.width,t.height=l.height;var r=s.options.resize.call(s,t,e,n,i),c=document.createElement("canvas"),d=c.getContext("2d");switch(c.width=r.trgWidth,c.height=r.trgHeight,o>4&&(c.width=r.trgHeight,c.height=r.trgWidth),o){case 2:d.translate(c.width,0),d.scale(-1,1);break;case 3:d.translate(c.width,c.height),d.rotate(Math.PI);break;case 4:d.translate(0,c.height),d.scale(1,-1);break;case 5:d.rotate(.5*Math.PI),d.scale(1,-1);break;case 6:d.rotate(.5*Math.PI),d.translate(0,-c.width);break;case 7:d.rotate(.5*Math.PI),d.translate(c.height,-c.width),d.scale(-1,1);break;case 8:d.rotate(-.5*Math.PI),d.translate(-c.height,0)}y(d,l,null!=r.srcX?r.srcX:0,null!=r.srcY?r.srcY:0,r.srcWidth,r.srcHeight,null!=r.trgX?r.trgX:0,null!=r.trgY?r.trgY:0,r.trgWidth,r.trgHeight);var u=c.toDataURL("image/png");if(null!=a)return a(u,c)})},null!=a&&(l.onerror=a),l.src=t.dataURL}},{key:"processQueue",value:function(){var t=this.options.parallelUploads,e=this.getUploadingFiles().length,n=e;if(!(e>=t)){var i=this.getQueuedFiles();if(i.length>0){if(this.options.uploadMultiple)return this.processFiles(i.slice(0,t-e));for(;n<t;){if(!i.length)return;this.processFile(i.shift()),n++}}}}},{key:"processFile",value:function(t){return this.processFiles([t])}},{key:"processFiles",value:function(t){var n=!0,i=!1,o=void 0;try{for(var a,r=t[Symbol.iterator]();!(n=(a=r.next()).done);n=!0){var s=a.value;s.processing=!0,s.status=e.UPLOADING,this.emit("processing",s)}}catch(t){i=!0,o=t}finally{try{n||null==r.return||r.return()}finally{if(i)throw o}}return this.options.uploadMultiple&&this.emit("processingmultiple",t),this.uploadFiles(t)}},{key:"_getFilesWithXhr",value:function(t){var e;return e=this.files.filter(function(e){return e.xhr===t}).map(function(t){return t})}},{key:"cancelUpload",value:function(t){if(t.status===e.UPLOADING){var n=this._getFilesWithXhr(t.xhr),i=!0,o=!1,a=void 0;try{for(var r,s=n[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value;l.status=e.CANCELED}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}"undefined"!=typeof t.xhr&&t.xhr.abort();var c=!0,d=!1,u=void 0;try{for(var p,h=n[Symbol.iterator]();!(c=(p=h.next()).done);c=!0){var f=p.value;this.emit("canceled",f)}}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}this.options.uploadMultiple&&this.emit("canceledmultiple",n)}else t.status!==e.ADDED&&t.status!==e.QUEUED||(t.status=e.CANCELED,this.emit("canceled",t),this.options.uploadMultiple&&this.emit("canceledmultiple",[t]));if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function(t){if("function"==typeof t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return t.apply(this,n)}return t}},{key:"uploadFile",value:function(t){return this.uploadFiles([t])}},{key:"uploadFiles",value:function(t){var n=this;this._transformFiles(t,function(i){if(n.options.chunking){var o=i[0];t[0].upload.chunked=n.options.chunking&&(n.options.forceChunking||o.size>n.options.chunkSize),t[0].upload.totalChunkCount=Math.ceil(o.size/n.options.chunkSize)}if(t[0].upload.chunked){var a=t[0],r=i[0],s=0;a.upload.chunks=[];var l=function(){for(var i=0;void 0!==a.upload.chunks[i];)i++;if(!(i>=a.upload.totalChunkCount)){s++;var o=i*n.options.chunkSize,l=Math.min(o+n.options.chunkSize,a.size),c={name:n._getParamName(0),data:r.webkitSlice?r.webkitSlice(o,l):r.slice(o,l),filename:a.upload.filename,chunkIndex:i};a.upload.chunks[i]={file:a,index:i,dataBlock:c,status:e.UPLOADING,progress:0,retries:0},n._uploadData(t,[c])}};if(a.upload.finishedChunkUpload=function(i,o){var r=!0;i.status=e.SUCCESS,n.options.chunkSuccess.call(n,i,a,o),i.dataBlock=null,i.xhr=null;for(var s=0;s<a.upload.totalChunkCount;s++){if(void 0===a.upload.chunks[s])return l();a.upload.chunks[s].status!==e.SUCCESS&&(r=!1)}r&&n.options.chunksUploaded.call(n,a,function(e){n._finished(t,e||"",null)})},n.options.parallelChunkUploads)for(var c=0;c<a.upload.totalChunkCount;c++)l();else l()}else{for(var d=[],u=0;u<t.length;u++)d[u]={name:n._getParamName(u),data:i[u],filename:t[u].upload.filename};n._uploadData(t,d)}})}},{key:"_getChunk",value:function(t,e){for(var n=0;n<t.upload.totalChunkCount;n++)if(void 0!==t.upload.chunks[n]&&t.upload.chunks[n].xhr===e)return t.upload.chunks[n]}},{key:"_uploadData",value:function(t,n){var i=this,o=new XMLHttpRequest,a=!0,r=!1,s=void 0;try{for(var l,c=t[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){var d=l.value;d.xhr=o}}catch(t){r=!0,s=t}finally{try{a||null==c.return||c.return()}finally{if(r)throw s}}t[0].upload.chunked&&(t[0].upload.chunks[n[0].chunkIndex].xhr=o);var u=this.resolveOption(this.options.method,t),p=this.resolveOption(this.options.url,t);o.open(u,p,!0),o.timeout=this.resolveOption(this.options.timeout,t),o.withCredentials=!!this.options.withCredentials,o.onload=function(e){i._finishedUploading(t,o,e)},o.ontimeout=function(){i._handleUploadError(t,o,"Request timedout after ".concat(i.options.timeout," seconds"))},o.onerror=function(){i._handleUploadError(t,o)};var h=null!=o.upload?o.upload:o;h.onprogress=function(e){return i._updateFilesUploadProgress(t,o,e)};var f={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"};this.options.headers&&e.extend(f,this.options.headers);for(var m in f){var g=f[m];g&&o.setRequestHeader(m,g)}var v=new FormData;if(this.options.params){var y=this.options.params;"function"==typeof y&&(y=y.call(this,t,o,t[0].upload.chunked?this._getChunk(t[0],o):null));for(var b in y){var x=y[b];v.append(b,x)}}var w=!0,_=!1,k=void 0;try{for(var C,S=t[Symbol.iterator]();!(w=(C=S.next()).done);w=!0){var T=C.value;this.emit("sending",T,o,v)}}catch(t){_=!0,k=t}finally{try{w||null==S.return||S.return()}finally{if(_)throw k}}this.options.uploadMultiple&&this.emit("sendingmultiple",t,o,v),this._addFormElementData(v);for(var $=0;$<n.length;$++){var D=n[$];v.append(D.name,D.data,D.filename)}this.submitRequest(o,v,t)}},{key:"_transformFiles",value:function(t,e){for(var n=this,i=[],o=0,a=function(a){n.options.transformFile.call(n,t[a],function(n){i[a]=n,++o===t.length&&e(i)})},r=0;r<t.length;r++)a(r)}},{key:"_addFormElementData",value:function(t){if("FORM"===this.element.tagName){var e=!0,n=!1,i=void 0;try{for(var o,a=this.element.querySelectorAll("input, textarea, select, button")[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value,s=r.getAttribute("name"),l=r.getAttribute("type");if(l&&(l=l.toLowerCase()),"undefined"!=typeof s&&null!==s)if("SELECT"===r.tagName&&r.hasAttribute("multiple")){var c=!0,d=!1,u=void 0;try{for(var p,h=r.options[Symbol.iterator]();!(c=(p=h.next()).done);c=!0){var f=p.value;f.selected&&t.append(s,f.value)}}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}}else(!l||"checkbox"!==l&&"radio"!==l||r.checked)&&t.append(s,r.value)}}catch(t){n=!0,i=t}finally{try{e||null==a.return||a.return()}finally{if(n)throw i}}}}},{key:"_updateFilesUploadProgress",value:function(t,e,n){var i;if("undefined"!=typeof n){if(i=100*n.loaded/n.total,t[0].upload.chunked){var o=t[0],a=this._getChunk(o,e);a.progress=i,a.total=n.total,a.bytesSent=n.loaded;o.upload.progress=0,o.upload.total=0,o.upload.bytesSent=0;for(var r=0;r<o.upload.totalChunkCount;r++)void 0!==o.upload.chunks[r]&&void 0!==o.upload.chunks[r].progress&&(o.upload.progress+=o.upload.chunks[r].progress,o.upload.total+=o.upload.chunks[r].total,o.upload.bytesSent+=o.upload.chunks[r].bytesSent);o.upload.progress=o.upload.progress/o.upload.totalChunkCount}else{var s=!0,l=!1,c=void 0;try{for(var d,u=t[Symbol.iterator]();!(s=(d=u.next()).done);s=!0){var p=d.value;p.upload.progress=i,p.upload.total=n.total,p.upload.bytesSent=n.loaded}}catch(t){l=!0,c=t}finally{try{s||null==u.return||u.return()}finally{if(l)throw c}}}var h=!0,f=!1,m=void 0;try{for(var g,v=t[Symbol.iterator]();!(h=(g=v.next()).done);h=!0){var y=g.value;this.emit("uploadprogress",y,y.upload.progress,y.upload.bytesSent)}}catch(t){f=!0,m=t}finally{try{h||null==v.return||v.return()}finally{if(f)throw m}}}else{var b=!0;i=100;var x=!0,w=!1,_=void 0;try{for(var k,C=t[Symbol.iterator]();!(x=(k=C.next()).done);x=!0){var S=k.value;100===S.upload.progress&&S.upload.bytesSent===S.upload.total||(b=!1),S.upload.progress=i,S.upload.bytesSent=S.upload.total}}catch(t){w=!0,_=t}finally{try{x||null==C.return||C.return()}finally{if(w)throw _}}if(b)return;var T=!0,$=!1,D=void 0;try{for(var E,F=t[Symbol.iterator]();!(T=(E=F.next()).done);T=!0){var A=E.value;this.emit("uploadprogress",A,i,A.upload.bytesSent)}}catch(t){$=!0,D=t}finally{try{T||null==F.return||F.return()}finally{if($)throw D}}}}},{key:"_finishedUploading",value:function(t,n,i){var o;if(t[0].status!==e.CANCELED&&4===n.readyState){if("arraybuffer"!==n.responseType&&"blob"!==n.responseType&&(o=n.responseText,n.getResponseHeader("content-type")&&~n.getResponseHeader("content-type").indexOf("application/json")))try{o=JSON.parse(o)}catch(t){i=t,o="Invalid JSON response from server."}this._updateFilesUploadProgress(t),200<=n.status&&n.status<300?t[0].upload.chunked?t[0].upload.finishedChunkUpload(this._getChunk(t[0],n),o):this._finished(t,o,i):this._handleUploadError(t,n,o)}}},{key:"_handleUploadError",value:function(t,n,i){if(t[0].status!==e.CANCELED){if(t[0].upload.chunked&&this.options.retryChunks){var o=this._getChunk(t[0],n);if(o.retries++<this.options.retryChunksLimit)return void this._uploadData(t,[o.dataBlock]);console.warn("Retried this chunk too often. Giving up.")}this._errorProcessing(t,i||this.options.dictResponseError.replace("{{statusCode}}",n.status),n)}}},{key:"submitRequest",value:function(t,e,n){t.send(e)}},{key:"_finished",value:function(t,n,i){var o=!0,a=!1,r=void 0;try{for(var s,l=t[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c.status=e.SUCCESS,this.emit("success",c,n,i),this.emit("complete",c)}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}if(this.options.uploadMultiple&&(this.emit("successmultiple",t,n,i),this.emit("completemultiple",t)),this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function(t,n,i){var o=!0,a=!1,r=void 0;try{for(var s,l=t[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c.status=e.ERROR,this.emit("error",c,n,i),this.emit("complete",c)}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}if(this.options.uploadMultiple&&(this.emit("errormultiple",t,n,i),this.emit("completemultiple",t)),this.options.autoProcessQueue)return this.processQueue()}}],[{key:"uuidv4",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0,n="x"===t?e:3&e|8;return n.toString(16)})}}]),e}(h);f.initClass(),f.version="5.7.0",f.options={},f.optionsForElement=function(t){return t.getAttribute("id")?f.options[g(t.getAttribute("id"))]:void 0},f.instances=[],f.forElement=function(t){if("string"==typeof t&&(t=document.querySelector(t)),null==(null!=t?t.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return t.dropzone},f.autoDiscover=!0,f.discover=function(){var t;if(document.querySelectorAll)t=document.querySelectorAll(".dropzone");else{t=[];var e=function(e){return function(){var n=[],i=!0,o=!1,a=void 0;try{for(var r,s=e[Symbol.iterator]();!(i=(r=s.next()).done);i=!0){var l=r.value;/(^| )dropzone($| )/.test(l.className)?n.push(t.push(l)):n.push(void 0)}}catch(t){o=!0,a=t}finally{try{i||null==s.return||s.return()}finally{if(o)throw a}}return n}()};e(document.getElementsByTagName("div")),e(document.getElementsByTagName("form"))}return function(){var e=[],n=!0,i=!1,o=void 0;try{for(var a,r=t[Symbol.iterator]();!(n=(a=r.next()).done);n=!0){var s=a.value;f.optionsForElement(s)!==!1?e.push(new f(s)):e.push(void 0)}}catch(t){i=!0,o=t}finally{try{n||null==r.return||r.return()}finally{if(i)throw o}}return e}()},f.blacklistedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i],f.isBrowserSupported=function(){var t=!0;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a")){var e=!0,n=!1,i=void 0;try{for(var o,a=f.blacklistedBrowsers[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;r.test(navigator.userAgent)&&(t=!1)}}catch(t){n=!0,i=t}finally{try{e||null==a.return||a.return()}finally{if(n)throw i}}}else t=!1;else t=!1;return t},f.dataURItoBlob=function(t){for(var e=atob(t.split(",")[1]),n=t.split(",")[0].split(":")[1].split(";")[0],i=new ArrayBuffer(e.length),o=new Uint8Array(i),a=0,r=e.length,s=0<=r;s?a<=r:a>=r;s?a++:a--)o[a]=e.charCodeAt(a);return new Blob([i],{type:n})};var m=function(t,e){return t.filter(function(t){return t!==e}).map(function(t){return t})},g=function(t){return t.replace(/[\-_](\w)/g,function(t){return t.charAt(1).toUpperCase()})};f.createElement=function(t){var e=document.createElement("div");return e.innerHTML=t,e.childNodes[0]},f.elementInside=function(t,e){if(t===e)return!0;for(;t=t.parentNode;)if(t===e)return!0;return!1},f.getElement=function(t,e){var n;if("string"==typeof t?n=document.querySelector(t):null!=t.nodeType&&(n=t),null==n)throw new Error("Invalid `".concat(e,"` option provided. Please provide a CSS selector or a plain HTML element."));return n},f.getElements=function(t,e){var n,i;if(t instanceof Array){i=[];try{var o=!0,a=!1,r=void 0;try{for(var s,l=t[Symbol.iterator]();!(o=(s=l.next()).done);o=!0)n=s.value,i.push(this.getElement(n,e))}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}}catch(t){i=null}}else if("string"==typeof t){i=[];var c=!0,d=!1,u=void 0;try{for(var p,h=document.querySelectorAll(t)[Symbol.iterator]();!(c=(p=h.next()).done);c=!0)n=p.value,i.push(n)}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}}else null!=t.nodeType&&(i=[t]);if(null==i||!i.length)throw new Error("Invalid `".concat(e,"` option provided. Please provide a CSS selector, a plain HTML element or a list of those."));return i},f.confirm=function(t,e,n){return window.confirm(t)?e():null!=n?n():void 0},f.isValidFile=function(t,e){if(!e)return!0;e=e.split(",");var n=t.type,i=n.replace(/\/.*$/,""),o=!0,a=!1,r=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;if(c=c.trim(),"."===c.charAt(0)){if(t.name.toLowerCase().indexOf(c.toLowerCase(),t.name.length-c.length)!==-1)return!0}else if(/\/\*$/.test(c)){if(i===c.replace(/\/.*$/,""))return!0}else if(n===c)return!0}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}return!1},"undefined"!=typeof t&&null!==t&&(t.fn.dropzone=function(t){return this.each(function(){return new f(this,t)})}),"undefined"!=typeof p&&null!==p?p.exports=f:window.Dropzone=f,f.ADDED="added",f.QUEUED="queued",f.ACCEPTED=f.QUEUED,f.UPLOADING="uploading",f.PROCESSING=f.UPLOADING,f.CANCELED="canceled",f.ERROR="error",f.SUCCESS="success";var v=function(t){var e=(t.naturalWidth,t.naturalHeight),n=document.createElement("canvas");n.width=1,n.height=e;var i=n.getContext("2d");i.drawImage(t,0,0);for(var o=i.getImageData(1,0,1,e),a=o.data,r=0,s=e,l=e;l>r;){var c=a[4*(l-1)+3];0===c?s=l:r=l,l=s+r>>1}var d=l/e;return 0===d?1:d},y=function(t,e,n,i,o,a,r,s,l,c){var d=v(e);return t.drawImage(e,n,i,o,a,r,s,l,c/d)},b=function(){function t(){s(this,t)}return c(t,null,[{key:"initClass",value:function(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function(t){for(var e="",n=void 0,i=void 0,o="",a=void 0,r=void 0,s=void 0,l="",c=0;;)if(n=t[c++],i=t[c++],o=t[c++],a=n>>2,r=(3&n)<<4|i>>4,s=(15&i)<<2|o>>6,l=63&o,isNaN(i)?s=l=64:isNaN(o)&&(l=64),e=e+this.KEY_STR.charAt(a)+this.KEY_STR.charAt(r)+this.KEY_STR.charAt(s)+this.KEY_STR.charAt(l),n=i=o="",a=r=s=l="",!(c<t.length))break;return e}},{key:"restore",value:function(t,e){if(!t.match("data:image/jpeg;base64,"))return e;var n=this.decode64(t.replace("data:image/jpeg;base64,","")),i=this.slice2Segments(n),o=this.exifManipulation(e,i);return"data:image/jpeg;base64,".concat(this.encode64(o))}},{key:"exifManipulation",value:function(t,e){var n=this.getExifArray(e),i=this.insertExif(t,n),o=new Uint8Array(i);return o}},{key:"getExifArray",value:function(t){for(var e=void 0,n=0;n<t.length;){if(e=t[n],255===e[0]&225===e[1])return e;n++}return[]}},{key:"insertExif",value:function(t,e){var n=t.replace("data:image/jpeg;base64,",""),i=this.decode64(n),o=i.indexOf(255,3),a=i.slice(0,o),r=i.slice(o),s=a;return s=s.concat(e),s=s.concat(r)}},{key:"slice2Segments",value:function(t){for(var e=0,n=[];;){var i;if(255===t[e]&218===t[e+1])break;if(255===t[e]&216===t[e+1])e+=2;else{i=256*t[e+2]+t[e+3];var o=e+i+2,a=t.slice(e,o);n.push(a),e=o}if(e>t.length)break}return n}},{key:"decode64",value:function(t){var e=void 0,n=void 0,i="",o=void 0,a=void 0,r=void 0,s="",l=0,c=[],d=/[^A-Za-z0-9\+\/\=]/g;for(d.exec(t)&&console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding."),t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");;)if(o=this.KEY_STR.indexOf(t.charAt(l++)),a=this.KEY_STR.indexOf(t.charAt(l++)),r=this.KEY_STR.indexOf(t.charAt(l++)),s=this.KEY_STR.indexOf(t.charAt(l++)),e=o<<2|a>>4,n=(15&a)<<4|r>>2,i=(3&r)<<6|s,c.push(e),64!==r&&c.push(n),64!==s&&c.push(i),e=n=i="",o=a=r=s="",!(l<t.length))break;return c}}]),t}();b.initClass();var x=function(t,e){var n=!1,i=!0,o=t.document,a=o.documentElement,r=o.addEventListener?"addEventListener":"attachEvent",s=o.addEventListener?"removeEventListener":"detachEvent",l=o.addEventListener?"":"on",c=function i(a){if("readystatechange"!==a.type||"complete"===o.readyState)return("load"===a.type?t:o)[s](l+a.type,i,!1),!n&&(n=!0)?e.call(t,a.type||a):void 0},d=function t(){try{a.doScroll("left")}catch(e){return void setTimeout(t,50)}return c("poll")};if("complete"!==o.readyState){if(o.createEventObject&&a.doScroll){try{i=!t.frameElement}catch(t){}i&&d()}return o[r](l+"DOMContentLoaded",c,!1),o[r](l+"readystatechange",c,!1),t[r](l+"load",c,!1)}};return f._autoDiscoverFunction=function(){if(f.autoDiscover)return f.discover()},x(window,f._autoDiscoverFunction),p.exports}),define("upload",["jquery","bootstrap","dropzone","template"],function(t,e,n,i){var o={list:{},options:{},config:{container:document.body,classname:".plupload:not([initialized]),.faupload:not([initialized])",previewtpl:'<li class="col-xs-3"><a href="<%=fullurl%>" data-url="<%=url%>" target="_blank" class="thumbnail"><img src="<%=fullurl%>" onerror="this.src=\''+Fast.api.fixurl("ajax/icon")+'?suffix=<%=suffix%>\';this.onerror=null;" class="img-responsive"></a><a href="javascript:;" class="btn btn-danger btn-xs btn-trash"><i class="fa fa-trash"></i></a></li>'},events:{onInit:function(){},onUploadSuccess:function(e,n,i){var a=e.element,r=e.options.onUploadSuccess,s="undefined"!=typeof n.data?n.data:null;if(a){var l=t(a).data("input-id")?t(a).data("input-id"):"";if(l){var c=[],d=t("#"+l);t(a).data("multiple")&&""!==d.val()&&c.push(d.val());var u=Config.upload.fullmode?s.fullurl?s.fullurl:Fast.api.cdnurl(s.url):s.url;c.push(u),d.val(c.join(",")).trigger("change").trigger("validate")}var p=t(a).data("upload-success");if(p&&("function"!=typeof p&&"function"==typeof o.api.custom[p]&&(p=o.api.custom[p]),"function"==typeof p)){var h=p.call(a,s,n,e,i);if(h===!1)return}}if("function"==typeof r){var h=r.call(a,s,n,e,i);if(h===!1)return}},onUploadError:function(e,n,i){var a=e.element,r=e.options.onUploadError,s="undefined"!=typeof n.data?n.data:null;if(a){var l=t(a).data("upload-error");if(l&&("function"!=typeof l&&"function"==typeof o.api.custom[l]&&(l=o.api.custom[l]),"function"==typeof l)){var c=l.call(a,s,n,e,i);if(c===!1)return}}if("function"==typeof r){var c=r.call(a,s,n,e,i);if(c===!1)return}Toastr.error(n.msg.toString().replace(/(<([^>]+)>)/gi,"")+"(code:"+n.code+")")},onUploadResponse:function(e,n,i){try{var o="object"==typeof e?e:JSON.parse(e);o.hasOwnProperty("code")||t.extend(o,{code:-2,msg:e,data:null})}catch(t){var o={code:-1,msg:t.message,data:null}}return o},onUploadComplete:function(e,n){var i=e.element,a=e.options.onUploadComplete;if(i){var r=t(i).data("upload-complete");if(r&&("function"!=typeof r&&"function"==typeof o.api.custom[r]&&(r=o.api.custom[r]),"function"==typeof r)){var s=r.call(i,n);if(s===!1)return}}if("function"==typeof a){var s=a.call(i,n);if(s===!1)return}}},api:{upload:function(e,a,r,s){e="undefined"==typeof e?o.config.classname:e,t(e,o.config.container).each(function(){if(t(this).attr("initialized"))return!0;t(this).attr("initialized",!0);var e=this,l=t(this).prop("id")||t(this).prop("name")||n.uuidv4(),c=t(this).data("url"),d=t(this).data("maxsize"),u=t(this).data("maxcount"),p=t(this).data("mimetype"),h=t(this).data("multipart"),f=t(this).data("multiple"),m=t(e).data("input-id")?t(e).data("input-id"):"",g=t(e).data("preview-id")?t(e).data("preview-id"):"";c=c?c:Config.upload.uploadurl,c=Fast.api.fixurl(c);var v=!1,y=Config.upload.chunksize||2097152,b=Config.upload.timeout||6e5;d="undefined"!=typeof d?d:Config.upload.maxsize,p="undefined"!=typeof p?p:Config.upload.mimetype,h="undefined"!=typeof h?h:Config.upload.multipart,f="undefined"!=typeof f?f:Config.upload.multiple,p=p.split(",").map(function(t){return t.indexOf("/")>-1?t:t&&"*"!==t&&"."!==t.charAt(0)?"."+t:t}).join(","),p="*"===p?null:p;var x=function(t){var e=t.toString().match(/^([0-9\.]+)(\w+)$/),n=e?parseFloat(e[1]):parseFloat(t),i=e?e[2].toLowerCase():"b",o={b:0,k:1,kb:1,m:2,mb:2,gb:3,g:3,tb:4,t:4},a="undefined"!=typeof o[i]?o[i]:0,r=n*Math.pow(1024,a);return r/Math.pow(1024,2)}(d),w=t(this).data()||{};w=t.extend(!0,{},w,t(this).data("upload-options")||{}),delete w.success,delete w.url,h=t.isArray(h)?{}:h;var _=t(this).data("params")||{};"undefined"!=typeof _.category?_.category:t(this).data("category")||"";o.list[l]=new n(this,t.extend({url:c,params:function(e,n,i){var o=h;return i?t.extend({},o,{filesize:i.file.size,filename:i.file.name,chunkid:i.file.upload.uuid,chunkindex:i.index,chunkcount:i.file.upload.totalChunkCount,chunksize:this.options.chunkSize,chunkfilesize:i.dataBlock.data.size,width:i.file.width||0,height:i.file.height||0,type:i.file.type}):o},chunking:v,chunkSize:y,maxFilesize:x,acceptedFiles:p,maxFiles:u&&parseInt(u)>1?u:f?null:1,timeout:b,parallelUploads:1,previewsContainer:!1,dictDefaultMessage:__("Drop files here to upload"),dictFallbackMessage:__("Your browser does not support drag'n'drop file uploads"),dictFallbackText:__("Please use the fallback form below to upload your files like in the olden days"),dictFileTooBig:__("File is too big (%sMiB), Max filesize: %sMiB","{{filesize}}","{{maxFilesize}}"),dictInvalidFileType:__("You can't upload files of this type"),dictResponseError:__("Server responded with %s code.","{{statusCode}}"),dictCancelUpload:__("Cancel upload"),dictUploadCanceled:__("Upload canceled"),dictCancelUploadConfirmation:__("Are you sure you want to cancel this upload?"),dictRemoveFile:__("Remove file"),dictMaxFilesExceeded:__("You can only upload a maximum of %s files","{{maxFiles}}"),init:function(){o.events.onInit.call(this),t(">i",this.element).addClass("dz-message"),this.options.elementHtml=t(this.element).html()},sending:function(t,e,n){"undefined"!=typeof t.category&&n.append("category",t.category)},addedfile:function(e){var n=t(this.element).data("params")||{},i="undefined"!=typeof n.category?n.category:t(this.element).data("category")||"";e.category="function"==typeof i?i.call(this,e):i},addedfiles:function(e){if(this.options.maxFiles&&(!this.options.maxFiles||this.options.maxFiles>1)&&this.options.inputId){var n=t("#"+this.options.inputId);if(n.length>0){var i=t.trim(n.val()),o=""===i?0:i.split(/\,/).length,a=this.options.maxFiles-o;if(0===a||e.length>a){e=Array.prototype.slice.call(e,a);for(var r=0;r<e.length;r++)this.removeFile(e[r]);Toastr.error(__("You can only upload a maximum of %s files",this.options.maxFiles))}}}},success:function(t,e){var n=o.events.onUploadResponse(e,this,t);t.ret=n,1===n.code?o.events.onUploadSuccess(this,n,t):o.events.onUploadError(this,n,t)},error:function(e,n,i){var a=t("<div>"+(i&&"undefined"!=typeof i.responseText?i.responseText:n)+"</div>");a.find("style, title, script").remove();var r=a.text()||__("Network error"),s={code:0,data:null,msg:r};o.events.onUploadError(this,s,e)},uploadprogress:function(e,n,i){e.upload.chunked&&t(this.element).prop("disabled",!0).html("<i class='fa fa-upload'></i> "+__("Upload")+Math.floor(e.upload.bytesSent/e.size*100)+"%")},totaluploadprogress:function(e,n){this.getActiveFiles().length>0&&!this.options.chunking&&t(this.element).prop("disabled",!0).html("<i class='fa fa-upload'></i> "+__("Upload")+Math.floor(e)+"%")},queuecomplete:function(){o.events.onUploadComplete(this,this.files),this.removeAllFiles(!0),t(this.element).prop("disabled",!1).html(this.options.elementHtml)},chunkSuccess:function(t,e,n){},chunksUploaded:function(e,n){var i=this;Fast.api.ajax({url:this.options.url,data:t.extend({},h,{action:"merge",filesize:e.size,filename:e.name,chunkid:e.upload.uuid,chunkcount:e.upload.totalChunkCount})},function(t,e){return n(JSON.stringify(e)),!1},function(t,n){e.accepted=!1,i._errorProcessing([e],n.msg)})},onUploadSuccess:a,onUploadError:r,onUploadComplete:s},o.options,w)),g&&f&&require(["dragsort"],function(){t("#"+g).dragsort({dragSelector:"li a:not(.btn-trash)",dragEnd:function(){t("#"+g).trigger("fa.preview.change")},placeHolderTemplate:'<li class="col-xs-3"></li>'})});var k=function(e){var n={},i=t("textarea[name='"+e+"']"),o=i.prev("ul");t.each(t("input,select,textarea",o).serializeArray(),function(t,e){var i=/\[?(\w+)\]?\[(\w+)\]$/g,o=i.exec(e.name);return!o||void(isNaN(o[2])?(o[1]="x"+parseInt(o[1]),"undefined"==typeof n[o[1]]&&(n[o[1]]={}),n[o[1]][o[2]]=e.value):n[t]=e.value)});var a=[];t.each(n,function(t,e){a.push(e)}),i.val(JSON.stringify(a))};g&&m&&(t(document.body).on("keyup change","#"+m,function(n){var a=t("#"+m).val(),r=a.split(/\,/),s=t("#"+g);s.empty();var l=s.data("template")?s.data("template"):"",c=s.next().is("textarea")?s.next("textarea").val():"{}",d={};try{d=JSON.parse(c)}catch(t){}t.each(r,function(n,a){if(!a)return!0;var r=/[\.]?([a-zA-Z0-9]+)$/.exec(a);r=r?r[1]:"file";var c=t(e).data(),u="undefined"!=typeof c.cdnurl?Fast.api.cdnurl(a,c.cdnurl):Fast.api.cdnurl(a);a=Config.upload.fullmode?u:a;var p=d&&"undefined"!=typeof d[n]?d[n]:null,h={url:a,fullurl:u,data:c,key:n,index:n,value:p,row:p,suffix:r},f=l?i(l,h):i.render(o.config.previewtpl,h);s.append(f)}),k(s.data("name"))}),t("#"+m).trigger("change")),g&&(t("#"+g).on("change keyup","input,textarea,select",function(){k(t(this).closest("ul").data("name"))}),t(document.body).on("fa.preview.change","#"+g,function(){var e=[];t("#"+g+" [data-url]").each(function(n,i){e.push(t(this).data("url"))}),m&&t("#"+m).val(e.join(",")),k(t("#"+g).data("name"))}),t(document.body).on("click","#"+g+" .btn-trash",function(){t(this).closest("li").remove(),t("#"+g).trigger("fa.preview.change")})),m&&(t("#"+m).closest("form").on("reset",function(){setTimeout(t.proxy(function(){t("#"+m,this).trigger("change")},this),0)}),t("body").on("paste drop","#"+m,function(e){var n=e.originalEvent,i=t(".plupload[data-input-id='"+t(this).attr("id")+"'],.faupload[data-input-id='"+t(this).attr("id")+"']");if("paste"===e.type&&n.clipboardData&&n.clipboardData.items){var a=n.clipboardData.items;if(!(1===a.length&&a[0].type.indexOf("text")>-1||2===a.length&&a[1].type.indexOf("text")>-1))return o.list[i.attr("id")].paste(n),!1}if("drop"===e.type&&n.dataTransfer&&n.dataTransfer.files)return o.list[i.attr("id")].drop(n),!1}))})},plupload:function(t,e,n,i){return o.api.upload(t,e,n,i)},faupload:function(t,e,n,i){return o.api.upload(t,e,n,i)},send:function(e,i,a,r){var s=Layer.msg(__("Uploading"),{offset:"t",time:0}),l="dropzone-"+n.uuidv4();t('<button type="button" id="'+l+'" class="btn btn-danger hidden faupload" />').appendTo("body"),t("#"+l).data("upload-complete",function(t){Layer.close(s),o.list[l].removeAllFiles(!0)}),o.api.upload("#"+l,i,a,r),setTimeout(function(){o.list[l].addFile(e)},1)},custom:{afteruploadcallback:function(t){console.log(this,t),alert("Custom Callback,Response URL:"+t.url)}}}};return o}),function(t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define("validator",["jquery"],t):t(jQuery)}(function(t,e){"use strict";function n(e,i){function o(){a.$el=t(e),a.$el.length?a._init(a.$el[0],i):q(e)&&(X[e]=i);
}var a=this;return a instanceof n?void(n.pending?t(window).on("validatorready",o):o()):new n(e,i)}function i(e){function n(){var e=this.options;for(var n in e)n in K&&(this[n]=e[n]);t.extend(this,{_valHook:function(){return"true"===this.element.contentEditable?"text":"val"},getValue:function(){var e=this.element;return"number"===e.type&&e.validity&&e.validity.badInput?"NaN":t(e)[this._valHook()]()},setValue:function(e){t(this.element)[this._valHook()](this.value=e)},getRangeMsg:function(t,e,n){function i(t,e){return l?t>e:t>=e}if(e){var o,a=this,r=a.messages[a._r]||"",s=e[0].split("~"),l="false"===e[1],c=s[0],d=s[1],u="rg",p=[""],h=B(t)&&+t===+t;return 2===s.length?c&&d?(h&&i(t,+c)&&i(+d,t)&&(o=!0),p=p.concat(s),u=l?"gtlt":"rg"):c&&!d?(h&&i(t,+c)&&(o=!0),p.push(c),u=l?"gt":"gte"):!c&&d&&(h&&i(+d,t)&&(o=!0),p.push(d),u=l?"lt":"lte"):(t===+c&&(o=!0),p.push(c),u="eq"),r&&(n&&r[u+n]&&(u+=n),p[0]=r[u]),o||a._rules&&(a._rules[a._i].msg=a.renderMsg.apply(null,p))}},renderMsg:function(){var t=arguments,e=t[0],n=t.length;if(e){for(;--n;)e=e.replace("{"+n+"}",t[n]);return e}}})}function i(n,i,o){this.key=n,this.validator=e,t.extend(this,o,i)}return n.prototype=e,i.prototype=new n,i}function o(t,e){if(W(t)){var n,i=e?e===!0?this:e:o.prototype;for(n in t)h(n)&&(i[n]=r(t[n]))}}function a(t,e){if(W(t)){var n,i=e?e===!0?this:e:a.prototype;for(n in t)i[n]=t[n]}}function r(e){switch(t.type(e)){case"function":return e;case"array":var n=function(){return e[0].test(this.value)||e[1]||!1};return n.msg=e[1],n;case"regexp":return function(){return e.test(this.value)}}}function s(e){var n,i,o;if(e&&e.tagName){switch(e.tagName){case"INPUT":case"SELECT":case"TEXTAREA":case"BUTTON":case"FIELDSET":n=e.form||t(e).closest("."+w);break;case"FORM":n=e;break;default:n=t(e).closest("."+w)}for(i in X)if(t(n).is(i)){o=X[i];break}return t(n).data(g)||t(n)[g](o).data(g)}}function l(t,e){var n=B(G(t,C+"-"+e));if(n&&(n=new Function("return "+n)()))return r(n)}function c(t,e,n){var i=e.msg,o=e._r;return W(i)&&(i=i[o]),q(i)||(i=G(t,S+"-"+o)||G(t,S)||(n?q(n)?n:n[o]:"")),i}function d(t){var e;return t&&(e=I.exec(t)),e&&e[0]}function u(t){return"INPUT"===t.tagName&&"checkbox"===t.type||"radio"===t.type}function p(t){return Date.parse(t.replace(/\.|\-/g,"/"))}function h(t){return/^\w+$/.test(t)}function f(t){var e="#"===t.charAt(0);return t=t.replace(/([:.{(|)}/\[\]])/g,"\\$1"),e?t:'[name="'+t+'"]:first'}var m,g="validator",v="."+g,y=".rule",b=".field",x=".form",w="nice-"+g,_="msg-box",k="aria-invalid",C="data-rule",S="data-msg",T="data-tip",$="data-ok",D="data-timely",E="data-target",F="data-display",A="data-must",O="novalidate",N=":verifiable",L=/(&)?(!)?\b(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?\s*(;|\|)?/g,R=/(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?/,M=/(?:([^:;\(\[]*):)?(.*)/,P=/[^\x00-\xff]/g,I=/top|right|bottom|left/,j=/(?:(cors|jsonp):)?(?:(post|get):)?(.+)/i,H=/[<>'"`\\]|&#x?\d+[A-F]?;?|%3[A-F]/gim,z=t.noop,Y=t.proxy,B=t.trim,U=t.isFunction,q=function(t){return"string"==typeof t},W=function(t){return t&&"[object Object]"===Object.prototype.toString.call(t)},V=document.documentMode||+(navigator.userAgent.match(/MSIE (\d+)/)&&RegExp.$1),G=function(t,n,i){return t&&t.tagName?i===e?t.getAttribute(n):void(null===i?t.removeAttribute(n):t.setAttribute(n,""+i)):null},X={},Q={debug:0,theme:"default",ignore:"",focusInvalid:!0,focusCleanup:!1,stopOnError:!1,beforeSubmit:null,valid:null,invalid:null,validation:null,formClass:"n-default",validClass:"n-valid",invalidClass:"n-invalid",bindClassTo:null,remoteDataType:"cors"},K={timely:1,display:null,target:null,ignoreBlank:!1,showOk:!0,dataFilter:function(t){if(q(t)||W(t)&&("error"in t||"ok"in t))return t},msgMaker:function(e){var n;return n='<span role="alert" class="msg-wrap n-'+e.type+'">'+e.arrow,e.result?t.each(e.result,function(t,i){n+='<span class="n-'+i.type+'">'+e.icon+'<span class="n-msg">'+i.msg+"</span></span>"}):n+=e.icon+'<span class="n-msg">'+e.msg+"</span>",n+="</span>"},msgWrapper:"span",msgArrow:"",msgIcon:'<span class="n-icon"></span>',msgClass:"n-right",msgStyle:"",msgShow:null,msgHide:null},Z={};return t.fn.validator=function(e){var i=this,o=arguments;return i.is(N)?i:(i.is("form")||(i=this.find("form")),i.length||(i=this),i.each(function(){var i=t(this).data(g);if(i)if(q(e)){if("_"===e.charAt(0))return;i[e].apply(i,[].slice.call(o,1))}else e&&(i._reset(!0),i._init(this,e));else new n(this,e)}),this)},t.fn.isValid=function(t,n){var i,o,a=s(this[0]),r=U(t);return!a||(r||n!==e||(n=t),a.checkOnly=!!n,o=a.options,i=a._multiValidate(this.is(N)?this:this.find(N),function(e){e||!o.focusInvalid||a.checkOnly||a.$el.find("["+k+"]:first").focus(),r&&(t.length?t(e):e&&t()),a.checkOnly=!1}),r?this:i)},t.extend(t.expr.pseudos||t.expr[":"],{verifiable:function(t){var e=t.nodeName.toLowerCase();return("input"===e&&!{submit:1,button:1,reset:1,image:1}[t.type]||"select"===e||"textarea"===e||"true"===t.contentEditable)&&!t.disabled},filled:function(e){return!!B(t(e).val())}}),n.prototype={_init:function(e,n){var r,s,l,c=this;U(n)&&(n={valid:n}),n=c._opt=n||{},l=G(e,"data-"+g+"-option"),l=c._dataOpt=l&&"{"===l.charAt(0)?new Function("return "+l)():{},s=c._themeOpt=Z[n.theme||l.theme||Q.theme],r=c.options=t.extend({},Q,K,s,c.options,n,l),c.rules=new o(r.rules,!0),c.messages=new a(r.messages,!0),c.Field=i(c),c.elements=c.elements||{},c.deferred={},c.errors={},c.fields={},c._initFields(r.fields),c.$el.data(g)||(c.$el.data(g,c).addClass(w+" "+r.formClass).on("form-submit-validate",function(t,e,n,i,o){c.vetoed=o.veto=!c.isValid,c.ajaxFormOptions=i}).on("submit"+v+" validate"+v,Y(c,"_submit")).on("reset"+v,Y(c,"_reset")).on("showmsg"+v,Y(c,"_showmsg")).on("hidemsg"+v,Y(c,"_hidemsg")).on("focusin"+v+" click"+v,N,Y(c,"_focusin")).on("focusout"+v+" validate"+v,N,Y(c,"_focusout")).on("keyup"+v+" input"+v+" compositionstart compositionend",N,Y(c,"_focusout")).on("click"+v,":radio,:checkbox","click",Y(c,"_focusout")).on("change"+v,'select,input[type="file"]',"change",Y(c,"_focusout")),c._NOVALIDATE=G(e,O),G(e,O,O)),q(r.target)&&c.$el.find(r.target).addClass("msg-container")},_guessAjax:function(e){function n(e,n,i){return!!(e&&e[n]&&t.map(e[n],function(t){return~t.namespace.indexOf(i)?1:null}).length)}var i=this;if(!(i.isAjaxSubmit=!!i.options.valid)){var o=(t._data||t.data)(e,"events");i.isAjaxSubmit=n(o,"valid","form")||n(o,"submit","form-plugin")}},_initFields:function(t){function e(t,e){if(null===e||r){var n=a.elements[t];n&&a._resetElement(n,!0),delete a.fields[t]}else a.fields[t]=new a.Field(t,q(e)?{rule:e}:e,a.fields[t])}var n,i,o,a=this,r=null===t;if(r&&(t=a.fields),W(t))for(n in t)if(~n.indexOf(","))for(i=n.split(","),o=i.length;o--;)e(B(i[o]),t[n]);else e(n,t[n]);a.$el.find(N).each(function(){a._parse(this)})},_parse:function(t){var e,n,i,o=this,a=t.name,r=G(t,C);return r&&G(t,C,null),t.id&&("#"+t.id in o.fields||!a||null!==r&&(e=o.fields[a])&&r!==e.rule&&t.id!==e.key)&&(a="#"+t.id),a||(a="#"+(t.id="N"+String(Math.random()).slice(-12))),e=o.getField(a,!0),e.rule=r||e.rule,(n=G(t,F))&&(e.display=n),e.rule&&((null!==G(t,A)||/\b(?:match|checked)\b/.test(e.rule))&&(e.must=!0),/\brequired\b/.test(e.rule)&&(e.required=!0),(i=G(t,D))?e.timely=+i:e.timely>3&&G(t,D,e.timely),o._parseRule(e),e.old={}),q(e.target)&&G(t,E,e.target),q(e.tip)&&G(t,T,e.tip),o.fields[a]=e},_parseRule:function(n){var i=M.exec(n.rule);i&&(n._i=0,i[1]&&(n.display=i[1]),i[2]&&(n._rules=[],i[2].replace(L,function(){var i=arguments;i[4]=i[4]||i[5],n._rules.push({and:"&"===i[1],not:"!"===i[2],or:"|"===i[6],method:i[3],params:i[4]?t.map(i[4].split(", "),B):e})})))},_multiValidate:function(n,i){var o=this,a=o.options;return o.hasError=!1,a.ignore&&(n=n.not(a.ignore)),n.each(function(){if(o._validate(this),o.hasError&&a.stopOnError)return!1}),i&&(o.validating=!0,t.when.apply(null,t.map(o.deferred,function(t){return t})).done(function(){i.call(o,!o.hasError),o.validating=!1})),t.isEmptyObject(o.deferred)?!o.hasError:e},_submit:function(n){var i=this,o=i.options,a=n.target,r="submit"===n.type&&"FORM"===a.tagName&&!n.isDefaultPrevented();n.preventDefault(),m&&~(m=!1)||i.submiting||"validate"===n.type&&i.$el[0]!==a||U(o.beforeSubmit)&&o.beforeSubmit.call(i,a)===!1||(i.isAjaxSubmit===e&&i._guessAjax(a),i._debug("log","\n<<< event: "+n.type),i._reset(),i.submiting=!0,i._multiValidate(i.$el.find(N),function(e){var n,s=e||2===o.debug?"valid":"invalid";e||(o.focusInvalid&&i.$el.find("["+k+"]:first").focus(),n=t.map(i.errors,function(t){return t})),i.submiting=!1,i.isValid=e,U(o[s])&&o[s].call(i,a,n),i.$el.trigger(s+x,[a,n]),i._debug("log",">>> "+s),e&&(i.vetoed?t(a).ajaxSubmit(i.ajaxFormOptions):r&&!i.isAjaxSubmit&&document.createElement("form").submit.call(a))}))},_reset:function(t){var e=this;e.errors={},t&&(e.reseting=!0,e.$el.find(N).each(function(){e._resetElement(this)}),delete e.reseting)},_resetElement:function(t,e){this._setClass(t,null),this.hideMsg(t)},_focusin:function(t){var e,n,i=this,o=i.options,a=t.target;i.validating||"click"===t.type&&document.activeElement===a||(o.focusCleanup&&"true"===G(a,k)&&(i._setClass(a,null),i.hideMsg(a)),n=G(a,T),n?i.showMsg(a,{type:"tip",msg:n}):(G(a,C)&&i._parse(a),(e=G(a,D))&&(8!==e&&9!==e||i._focusout(t))))},_focusout:function(e){var n,i,o,a,r,s,l,c,d,p=this,h=p.options,f=e.target,m=e.type,g="focusin"===m,v="validate"===m,y=0;if("compositionstart"===m&&(p.pauseValidate=!0),"compositionend"===m&&(p.pauseValidate=!1),!p.pauseValidate&&(i=f.name&&u(f)?p.$el.find('input[name="'+f.name+'"]').get(0):f,(o=p.getField(i))&&o.rule)){if(n=o._e,o._e=m,d=o.timely,!v){if(!d||u(f)&&"click"!==m)return;if(r=o.getValue(),o.ignoreBlank&&!r&&!g)return void p.hideMsg(f);if("focusout"===m){if("change"===n)return;if(2===d||8===d){if(a=o.old,!r||!a)return;o.isValid&&!a.showOk?p.hideMsg(f):p._makeMsg(f,o,a)}}else{if(d<2&&!e.data)return;if(s=+new Date,s-(f._ts||0)<100)return;if(f._ts=s,"keyup"===m){if("input"===n)return;if(l=e.keyCode,c={8:1,9:1,16:1,32:1,46:1},9===l&&!r)return;if(l<48&&!c[l])return}g||(y=d<100?"click"===m||"SELECT"===f.tagName?0:400:d)}}h.ignore&&t(f).is(h.ignore)||(clearTimeout(o._t),y?o._t=setTimeout(function(){p._validate(f,o)},y):(v&&(o.old={}),p._validate(f,o)))}},_setClass:function(e,n){var i=t(e),o=this.options;o.bindClassTo&&(i=i.closest(o.bindClassTo)),i.removeClass(o.invalidClass+" "+o.validClass),null!==n&&i.addClass(n?o.validClass:o.invalidClass)},_showmsg:function(t,e,n){var i=this,o=t.target;i.$el.is(o)?W(e)?i.showMsg(e):"tip"===e&&i.$el.find(N+"["+T+"]",o).each(function(){i.showMsg(this,{type:e,msg:n})}):i.showMsg(o,{type:e,msg:n})},_hidemsg:function(e){var n=t(e.target);n.is(N)&&this.hideMsg(n)},_validatedField:function(e,n,i){var o=this,a=o.options,r=n.isValid=i.isValid=!!i.isValid,s=r?"valid":"invalid";i.key=n.key,i.ruleName=n._r,i.id=e.id,i.value=n.value,o.elements[n.key]=i.element=e,o.isValid=o.$el[0].isValid=r?o.isFormValid():r,r?i.type="ok":(o.submiting&&(o.errors[n.key]=i.msg),o.hasError=!0),n.old=i,U(n[s])&&n[s].call(o,e,i),U(a.validation)&&a.validation.call(o,e,i),t(e).attr(k,!r||null).trigger(s+b,[i,o]),o.$el.triggerHandler("validation",[i,o]),o.checkOnly||(o._setClass(e,i.skip||"tip"===i.type?null:r),o._makeMsg.apply(o,arguments))},_makeMsg:function(e,n,i){n.msgMaker&&(i=t.extend({},i),"focusin"===n._e&&(i.type="tip"),this[i.showOk||i.msg||"tip"===i.type?"showMsg":"hideMsg"](e,i,n))},_validatedRule:function(n,i,o,a){i=i||u.getField(n),a=a||{};var r,s,l,d,u=this,p=i._r,h=i.timely,f=9===h||8===h,m=!1;if(null===o)return u._validatedField(n,i,{isValid:!0,skip:!0}),void(i._i=0);if(o===e?l=!0:o===!0||""===o?m=!0:q(o)?r=o:W(o)?o.error?r=o.error:(r=o.ok,m=!0):m=!!o,s=i._rules[i._i],s.not&&(r=e,m="required"===p||!m),s.or)if(m)for(;i._i<i._rules.length&&i._rules[i._i].or;)i._i++;else l=!0;else s.and&&(i.isValid||(l=!0));l?m=!0:(m&&i.showOk!==!1&&(d=G(n,$),r=null===d?q(i.ok)?i.ok:r:d,!q(r)&&q(i.showOk)&&(r=i.showOk),q(r)&&(a.showOk=m)),m&&!f||(r=(c(n,i,r||s.msg||u.messages[p])||u.messages.fallback).replace(/\{0\|?([^\}]*)\}/,function(t,e){return u._getDisplay(n,i.display)||e||u.messages[0]})),m||(i.isValid=m),a.msg=r,t(n).trigger((m?"valid":"invalid")+y,[p,r])),!f||l&&!s.and||(m||i._m||(i._m=r),i._v=i._v||[],i._v.push({type:m?l?"tip":"ok":"error",msg:r||s.msg})),u._debug("log","   "+i._i+": "+p+" => "+(m||r)),(m||f)&&i._i<i._rules.length-1?(i._i++,u._checkRule(n,i)):(i._i=0,f?(a.isValid=i.isValid,a.result=i._v,a.msg=i._m||"",i.value||"focusin"!==i._e||(a.type="tip")):a.isValid=m,u._validatedField(n,i,a),delete i._m,delete i._v)},_checkRule:function(n,i){var o,a,r,s=this,c=i.key,d=i._rules[i._i],u=d.method,p=d.params;s.submiting&&s.deferred[c]||(r=i.old,i._r=u,r&&!i.must&&!d.must&&d.result!==e&&r.ruleName===u&&r.id===n.id&&i.value&&r.value===i.value?o=d.result:(a=l(n,u)||s.rules[u]||z,o=a.call(i,n,p,i),a.msg&&(d.msg=a.msg)),W(o)&&U(o.then)?(s.deferred[c]=o,i.isValid=e,!s.checkOnly&&s.showMsg(n,{type:"loading",msg:s.messages.loading},i),o.then(function(o,a,r){var l,c=B(r.responseText),u=i.dataFilter;/jsonp?/.test(this.dataType)?c=o:"{"===c.charAt(0)&&(c=t.parseJSON(c)),l=u.call(this,c,i),l===e&&(l=u.call(this,c.data,i)),d.data=this.data,d.result=i.old?l:e,s._validatedRule(n,i,l)},function(t,e){s._validatedRule(n,i,s.messages[e]||e)}).always(function(){delete s.deferred[c]})):s._validatedRule(n,i,o))},_validate:function(t,e){var n=this;if(!t.disabled&&null===G(t,O)&&(e=e||n.getField(t),e&&(e._rules||n._parse(t),e._rules)))return n._debug("info",e.key),e.isValid=!0,e.element=t,e.value=e.getValue(),e.required||e.must||e.value||u(t)?(n._checkRule(t,e),e.isValid):(n._validatedField(t,e,{isValid:!0}),!0)},_debug:function(t,e){window.console&&this.options.debug&&console[t](e)},test:function(t,n){var i,o,a,r,s=this,l=R.exec(n);return l&&(a=l[1],a in s.rules&&(r=l[2]||l[3],r=r?r.split(", "):e,o=s.getField(t,!0),o._r=a,o.value=o.getValue(),i=s.rules[a].call(o,t,r))),i===!0||i===e||null===i},_getDisplay:function(t,e){return q(e)?e:U(e)?e.call(this,t):""},_getMsgOpt:function(e,n){var i=n?n:this.options;return t.extend({type:"error",pos:d(i.msgClass),target:i.target,wrapper:i.msgWrapper,style:i.msgStyle,cls:i.msgClass,arrow:i.msgArrow,icon:i.msgIcon},q(e)?{msg:e}:e)},_getMsgDOM:function(n,i){var o,a,r,s,l=t(n);if(l.is(N)?(r=i.target||G(n,E),r&&(r=U(r)?r.call(this,n):"#"===r.charAt(0)?t(r):this.$el.find(r),r.length&&(r.is(N)?(l=r,n=r.get(0)):r.hasClass(_)?o=r:s=r)),o||(a=u(n)&&n.name||!n.id?n.name:n.id,o=(s||this.$el).find(i.wrapper+"."+_+'[for="'+a+'"]'))):o=l,!i.hide&&!o.length)if(o=t("<"+i.wrapper+">").attr({class:_+(i.cls?" "+i.cls:""),style:i.style||e,for:a}),s)o.appendTo(s);else if(u(n)){var c=l.parent();o.appendTo(c.is("label")?c.parent():c)}else o[i.pos&&"right"!==i.pos?"insertBefore":"insertAfter"](l);return o},showMsg:function(e,n,i){if(e){var o,a,r,s,l=this,c=l.options;if(W(e)&&!e.jquery&&!n)return void t.each(e,function(t,e){var n=l.elements[t]||l.$el.find(f(t))[0];l.showMsg(n,e)});t(e).is(N)&&(i=i||l.getField(e)),(a=(i||c).msgMaker)&&(n=l._getMsgOpt(n,i),e=(e.name&&u(e)?l.$el.find('input[name="'+e.name+'"]'):t(e)).get(0),n.msg||"error"===n.type||(r=G(e,"data-"+n.type),null!==r&&(n.msg=r)),q(n.msg)&&(s=l._getMsgDOM(e,n),!I.test(s[0].className)&&s.addClass(n.cls),6===V&&"bottom"===n.pos&&(s[0].style.marginTop=t(e).outerHeight()+"px"),s.html(a.call(l,n))[0].style.display="",U(o=i&&i.msgShow||c.msgShow)&&o.call(l,s,n.type)))}},hideMsg:function(e,n,i){var o,a,r=this,s=r.options;e=t(e).get(0),t(e).is(N)&&(i=i||r.getField(e),i&&(i.isValid||r.reseting)&&G(e,k,null)),n=r._getMsgOpt(n,i),n.hide=!0,a=r._getMsgDOM(e,n),a.length&&(U(o=i&&i.msgHide||s.msgHide)?o.call(r,a,n.type):(a[0].style.display="none",a[0].innerHTML=""))},getField:function(t,n){var i,o,a=this;if(q(t))i=t,t=e;else{if(G(t,C))return a._parse(t);i=t.id&&"#"+t.id in a.fields||!t.name?"#"+t.id:t.name}return((o=a.fields[i])||n&&(o=new a.Field(i)))&&(o.element=t),o},setField:function(t,e){var n={};t&&(q(t)?n[t]=e:n=t,this._initFields(n))},isFormValid:function(){var t,e,n=this.fields;for(t in n)if(e=n[t],e._rules&&(e.required||e.must||e.value)&&!e.isValid)return!1;return!0},holdSubmit:function(t){this.submiting=t===e||t},cleanUp:function(){this._reset(1)},destroy:function(){this._reset(1),this.$el.off(v).removeData(g),G(this.$el[0],O,this._NOVALIDATE)}},t(window).on("beforeunload",function(){this.focus()}),t(document).on("click",":submit",function(){var t,e=this;e.form&&(t=e.getAttributeNode("formnovalidate"),(t&&null!==t.nodeValue||null!==G(e,O))&&(m=!0))}).on("focusin submit validate","form,."+w,function(e){if(null===G(this,O)){var n,i=t(this);!i.data(g)&&(n=s(this))&&(t.isEmptyObject(n.fields)?(G(this,O,O),i.off(v).removeData(g)):"focusin"===e.type?n._focusin(e):n._submit(e))}}),new a({fallback:"This field is not valid.",loading:"Validating..."}),new o({required:function(e,n){var i=this,o=B(i.value),a=!0;if(n)if(1===n.length){if(h(n[0])){if(i.rules[n[0]]){if(!o&&!i.test(e,n[0]))return null;i._r="required"}}else if(!o&&!t(n[0],i.$el).length)return null}else if("not"===n[0])t.each(n.slice(1),function(){return a=o!==B(this)});else if("from"===n[0]){var r,s=i.$el.find(n[1]),l="_validated_";return a=s.filter(function(){var t=i.getField(this);return t&&!!B(t.getValue())}).length>=(n[2]||1),a?o||(r=null):r=c(s[0],i)||!1,t(e).data(l)||s.data(l,1).each(function(){e!==this&&i._validate(this)}).removeData(l),r}return a&&!!o},integer:function(t,e){var n,i="0|",o="[1-9]\\d*",a=e?e[0]:"*";switch(a){case"+":n=o;break;case"-":n="-"+o;break;case"+0":n=i+o;break;case"-0":n=i+"-"+o;break;default:n=i+"-?"+o}return n="^(?:"+n+")$",new RegExp(n).test(this.value)||this.messages.integer&&this.messages.integer[a]},match:function(e,n){if(n){var i,o,a,r,s,l,c,d=this,u=!0,h="eq";if(1===n.length?a=n[0]:(h=n[0],a=n[1]),s=f(a),l=d.$el.find(s)[0]){if(c=d.getField(l),i=d.value,o=c.getValue(),d._match||(d.$el.on("valid"+b+v,s,function(){t(e).trigger("validate")}),d._match=c._match=1),!d.required&&""===i&&""===o)return null;if(r=n[2],r&&(/^date(time)?$/i.test(r)?(i=p(i),o=p(o)):"time"===r&&(i=+i.replace(/:/g,""),o=+o.replace(/:/g,""))),"eq"!==h&&!isNaN(+i)&&isNaN(+o))return!0;switch(h){case"lt":u=+i<+o;break;case"lte":u=+i<=+o;break;case"gte":u=+i>=+o;break;case"gt":u=+i>+o;break;case"neq":u=i!==o;break;default:u=i===o}return u||W(d.messages.match)&&d.messages.match[h].replace("{1}",d._getDisplay(l,c.display||a))}}},range:function(t,e){return this.getRangeMsg(this.value,e)},checked:function(t,e){if(u(t)){var n,i,o=this;return t.name?i=o.$el.find('input[name="'+t.name+'"]').filter(function(){var t=this;return!n&&u(t)&&(n=t),!t.disabled&&t.checked}).length:(n=t,i=n.checked),e?o.getRangeMsg(i,e):!!i||c(n,o,"")||o.messages.required||!1}},length:function(t,e){var n=this.value,i=("true"===e[1]?n.replace(P,"xx"):n).length;return this.getRangeMsg(i,e,e[1]?"_2":"")},remote:function(e,n){if(n){var i,o=this,a=j.exec(n[0]),r=o._rules[o._i],s={},l="",c=a[3],d=a[2]||"POST",u=(a[1]||this.validator.options.remoteDataType||"").toLowerCase();return r.must=!0,s[e.name]=o.value,n[1]&&t.map(n.slice(1),function(t){var e,n;~t.indexOf("=")?l+="&"+t:(e=t.split(":"),t=B(e[0]),n=B(e[1])||t,s[t]=o.$el.find(f(n)).val())}),s=t.param(s)+l,!o.must&&r.data&&r.data===s?r.result:("cors"!==u&&/^https?:/.test(c)&&!~c.indexOf(location.host)&&(i="jsonp"),t.ajax({url:c,type:d,data:s,dataType:i}))}},filter:function(t,e){var n=this.value,i=n.replace(e?new RegExp("["+e[0]+"]","gm"):H,"");i!==n&&this.setValue(i)}}),n.config=function(e,n){function i(t,e){"rules"===t?new o(e):"messages"===t?new a(e):t in K?K[t]=e:Q[t]=e}W(e)?t.each(e,i):q(e)&&i(e,n)},n.setTheme=function(e,n){W(e)?t.extend(!0,Z,e):q(e)&&W(n)&&(Z[e]=t.extend(Z[e],n))},n.load=function(e){if(e){var i,o,a,r=document,s={},l=r.scripts[0];e.replace(/([^?=&]+)=([^&#]*)/g,function(t,e,n){s[e]=n}),i=s.dir||n.dir,n.css||""===s.css||(o=r.createElement("link"),o.rel="stylesheet",o.href=n.css=i+"jquery.validator.css",l.parentNode.insertBefore(o,l)),!n.local&&~e.indexOf("local")&&""!==s.local&&(n.local=(s.local||r.documentElement.lang||"en").replace("_","-"),n.pending=1,o=r.createElement("script"),o.src=i+"local/"+n.local+".js",a="onload"in o?"onload":"onreadystatechange",o[a]=function(){o.readyState&&!/loaded|complete/.test(o.readyState)||(o=o[a]=null,delete n.pending,t(window).triggerHandler("validatorready"))},l.parentNode.insertBefore(o,l))}},function(){for(var t,e,i=document.scripts,o=i.length,a=/(.*validator(?:\.min)?.js)(\?.*(?:local|css|dir)(?:=[\w\-]*)?)?/;o--&&!e;)t=i[o],e=(t.hasAttribute?t.src:t.getAttribute("src",4)||"").match(a);e&&(n.dir=e[1].split("/").slice(0,-1).join("/")+"/",n.load(e[2]))}(),t[g]=n}),function(t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define("validator-lang",["jquery"],t):t(jQuery)}(function(t){t.validator.config({rules:{digits:[/^\d+$/,"请填写数字"],letters:[/^[a-z]+$/i,"请填写字母"],date:[/^\d{4}-\d{2}-\d{2}$/,"请填写有效的日期，格式:yyyy-mm-dd"],time:[/^([01]\d|2[0-3])(:[0-5]\d){1,2}$/,"请填写有效的时间，00:00到23:59之间"],email:[/^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/i,"请填写有效的邮箱"],url:[/^(https?|s?ftp):\/\/\S+$/i,"请填写有效的网址"],qq:[/^[1-9]\d{4,}$/,"请填写有效的QQ号"],IDcard:[/^\d{6}(19|2\d)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)?$/,"请填写正确的身份证号码"],tel:[/^(?:(?:0\d{2,3}[\- ]?[1-9]\d{6,7})|(?:[48]00[\- ]?[1-9]\d{6}))$/,"请填写有效的电话号码"],mobile:[/^1[3-9]\d{9}$/,"请填写有效的手机号"],zipcode:[/^\d{6}$/,"请检查邮政编码格式"],chinese:[/^[\u0391-\uFFE5]+$/,"请填写中文字符"],username:[/^\w{3,12}$/,"请填写3-12位数字、字母、下划线"],password:[/^[\S]{6,16}$/,"请填写6-16位字符，不能包含空格"],accept:function(e,n){if(!n)return!0;var i=n[0],o=t(e).val();return"*"===i||new RegExp(".(?:"+i+")$","i").test(o)||this.renderMsg("只接受{1}后缀的文件",i.replace(/\|/g,","))}},messages:{0:"此处",fallback:"{0}格式不正确",loading:"正在验证...",error:"网络异常",timeout:"请求超时",required:"{0}不能为空",remote:"{0}已被使用",integer:{"*":"请填写整数","+":"请填写正整数","+0":"请填写正整数或0","-":"请填写负整数","-0":"请填写负整数或0"},match:{eq:"{0}与{1}不一致",neq:"{0}与{1}不能相同",lt:"{0}必须小于{1}",gt:"{0}必须大于{1}",lte:"{0}不能大于{1}",gte:"{0}不能小于{1}"},range:{rg:"请填写{1}到{2}的数",gte:"请填写不小于{1}的数",lte:"请填写最大{1}的数",gtlt:"请填写{1}到{2}之间的数",gt:"请填写大于{1}的数",lt:"请填写小于{1}的数"},checked:{eq:"请选择{1}项",rg:"请选择{1}到{2}项",gte:"请至少选择{1}项",lte:"请最多选择{1}项"},length:{eq:"请填写{1}个字符",rg:"请填写{1}到{2}个字符",gte:"请至少填写{1}个字符",lte:"请最多填写{1}个字符",eq_2:"",rg_2:"",gte_2:"",lte_2:""}}});var e='<span class="n-arrow"><b>◆</b><i>◆</i></span>';t.validator.setTheme({simple_right:{formClass:"n-simple",msgClass:"n-right"},simple_bottom:{formClass:"n-simple",msgClass:"n-bottom"},yellow_top:{formClass:"n-yellow",msgClass:"n-top",msgArrow:e},yellow_right:{formClass:"n-yellow",msgClass:"n-right",msgArrow:e},yellow_right_effect:{formClass:"n-yellow",msgClass:"n-right",msgArrow:e,msgShow:function(t,e){var n=t.children();n.is(":animated")||("error"===e?n.css({left:"20px",opacity:0}).delay(100).show().stop().animate({left:"-4px",opacity:1},150).animate({left:"3px"},80).animate({left:0},80):n.css({left:0,opacity:1}).fadeIn(200))},msgHide:function(t,e){var n=t.children();n.stop().delay(100).show().animate({left:"20px",opacity:0},300,function(){t.hide()})}}})}),define("form",["jquery","bootstrap","upload","validator","validator-lang"],function(t,e,n,i,e){var o={config:{fieldlisttpl:'<dd class="form-inline"><input type="text" name="<%=name%>[<%=index%>][key]" class="form-control" value="<%=key%>" placeholder="<%=options.keyPlaceholder||\'\'%>" size="10" /> <input type="text" name="<%=name%>[<%=index%>][value]" class="form-control" value="<%=value%>" placeholder="<%=options.valuePlaceholder||\'\'%>" /> <span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></dd>'},events:{validator:function(e,n,i,a){e.is("form")&&(e.validator(t.extend({rules:{username:[/^\w{3,30}$/,__("Username must be 3 to 30 characters")],password:[/^[\S]{6,30}$/,__("Password must be 6 to 30 characters")]},validClass:"has-success",invalidClass:"has-error",bindClassTo:".form-group",formClass:"n-default n-bootstrap",msgClass:"n-right",stopOnError:!0,display:function(e){return t(e).closest(".form-group").find(".control-label").text().replace(/\:/,"")},dataFilter:function(t){return 1===t.code?t.msg?{ok:t.msg}:"":t.msg},target:function(e){var n=t(e).data("target");if(n&&t(n).length>0)return t(n);var i=t(e).closest(".form-group"),o=i.find("span.msg-box");return o.length?o:[]},valid:function(r){var s=this,l=t(".layer-footer [type=submit]",e);s.holdSubmit(!0),l.addClass("disabled");var c=o.api.submit(t(r),function(e,i){if(s.holdSubmit(!1),l.removeClass("disabled"),!1===t(this).triggerHandler("success.form",[e,i]))return!1;if("function"==typeof n&&!1===n.call(t(this),e,i))return!1;var o=i.hasOwnProperty("msg")&&""!==i.msg?i.msg:__("Operation completed");if(parent.Toastr.success(o),parent.$(".btn-refresh").trigger("click"),window.name){var a=parent.Layer.getFrameIndex(window.name);parent.Layer.close(a)}return!1},function(e,n){return s.holdSubmit(!1),!1!==t(this).triggerHandler("error.form",[e,n])&&(l.removeClass("disabled"),("function"!=typeof i||!1!==i.call(t(this),e,n))&&void 0)},a);return c||(s.holdSubmit(!1),l.removeClass("disabled")),!1}},e.data("validator-options")||{})),t(".layer-footer [type=submit],.fixed-footer [type=submit],.normal-footer [type=submit]",e).removeClass("disabled"),e.on("click",".layer-close",function(){if(window.name){var t=parent.Layer.getFrameIndex(window.name);parent.Layer.close(t)}return!1}))},selectpicker:function(e){t(".selectpicker",e).length>0&&require(["bootstrap-select","bootstrap-select-lang"],function(){t(".selectpicker",e).selectpicker(),t(e).on("reset",function(){setTimeout(function(){t(".selectpicker").selectpicker("refresh").trigger("change")},1)})})},selectpage:function(e){t(".selectpage",e).length>0&&(require(["selectpage"],function(){t(".selectpage",e).selectPage({eAjaxSuccess:function(t){return t.list="undefined"!=typeof t.rows?t.rows:"undefined"!=typeof t.list?t.list:[],t.totalRow="undefined"!=typeof t.total?t.total:"undefined"!=typeof t.totalRow?t.totalRow:t.list.length,t}})}),t(document).on("change",".sp_hidden",function(){t(this).trigger("validate")}),t(document).on("change",".sp_input",function(){t(this).closest(".sp_container").find(".sp_hidden").trigger("change")}),t(e).on("reset",function(){setTimeout(function(){t(".selectpage",e).each(function(){var e=t(this).data("selectPageObject");e.elem.hidden.val(t(this).val()),t(this).selectPageRefresh()})},1)}))},cxselect:function(e){t("[data-toggle='cxselect']",e).length>0&&require(["cxselect"],function(){t.cxSelect.defaults.jsonName="name",t.cxSelect.defaults.jsonValue="value",t.cxSelect.defaults.jsonSpace="data",t("[data-toggle='cxselect']",e).cxSelect()})},citypicker:function(e){t("[data-toggle='city-picker']",e).length>0&&require(["citypicker"],function(){t(e).on("reset",function(){setTimeout(function(){t("[data-toggle='city-picker']").citypicker("refresh")},1)})})},datetimepicker:function(e){t(".datetimepicker",e).length>0&&require(["bootstrap-datetimepicker"],function(){var n={format:"YYYY-MM-DD HH:mm:ss",icons:{time:"fa fa-clock-o",date:"fa fa-calendar",up:"fa fa-chevron-up",down:"fa fa-chevron-down",previous:"fa fa-chevron-left",next:"fa fa-chevron-right",today:"fa fa-history",clear:"fa fa-trash",close:"fa fa-remove"},showTodayButton:!0,showClose:!0};t(".datetimepicker",e).parent().css("position","relative"),t(".datetimepicker",e).datetimepicker(n).on("dp.change",function(e){t(this,document).trigger("changed")})})},daterangepicker:function(e){t(".datetimerange",e).length>0&&require(["bootstrap-daterangepicker"],function(){var n={};n[__("Today")]=[Moment().startOf("day"),Moment().endOf("day")],n[__("Yesterday")]=[Moment().subtract(1,"days").startOf("day"),Moment().subtract(1,"days").endOf("day")],n[__("Last 7 Days")]=[Moment().subtract(6,"days").startOf("day"),Moment().endOf("day")],n[__("Last 30 Days")]=[Moment().subtract(29,"days").startOf("day"),Moment().endOf("day")],n[__("This Month")]=[Moment().startOf("month"),Moment().endOf("month")],n[__("Last Month")]=[Moment().subtract(1,"month").startOf("month"),Moment().subtract(1,"month").endOf("month")];var i={timePicker:!1,autoUpdateInput:!1,timePickerSeconds:!0,timePicker24Hour:!0,autoApply:!0,locale:{format:"YYYY-MM-DD HH:mm:ss",customRangeLabel:__("Custom Range"),applyLabel:__("Apply"),cancelLabel:__("Clear")},ranges:n},o=function(e,n){t(this.element).val(e.format(this.locale.format)+" - "+n.format(this.locale.format)),t(this.element).trigger("change")};t(".datetimerange",e).each(function(){var e="function"==typeof t(this).data("callback")?t(this).data("callback"):o;t(this).on("apply.daterangepicker",function(t,n){e.call(n,n.startDate,n.endDate)}),t(this).on("cancel.daterangepicker",function(e,n){t(this).val("").trigger("change")}),t(this).daterangepicker(t.extend(!0,{},i,t(this).data()||{},t(this).data("daterangepicker-options")||{}))})})},plupload:function(t){o.events.faupload(t)},faupload:function(e){t(".plupload,.faupload",e).length>0&&n.api.upload(t(".plupload,.faupload",e))},faselect:function(e){t(".faselect,.fachoose",e).length>0&&t(".faselect,.fachoose",e).off("click").on("click",function(){var e=this,n=!!t(this).data("multiple")&&t(this).data("multiple"),i=t(this).data("mimetype")?t(this).data("mimetype"):"",o=t(this).data("admin-id")?t(this).data("admin-id"):"",a=t(this).data("user-id")?t(this).data("user-id"):"";i=i.replace(/\/\*/gi,"/");var r=t(this).data("url")?t(this).data("url"):"undefined"!=typeof Backend?"general/attachment/select":"user/attachment";return parent.Fast.api.open(r+"?element_id="+t(this).attr("id")+"&multiple="+n+"&mimetype="+i+"&admin_id="+o+"&user_id="+a,__("Choose"),{callback:function(n){var i=t(e),o=t(i).data("maxcount"),a=t(i).data("input-id")?t(i).data("input-id"):"";if(o="undefined"!=typeof o?o:0,a&&n.multiple){var r=[],s=t("#"+a),l=t.trim(s.val());""!==l&&r.push(s.val());var c=""===l?0:l.split(/\,/).length,d=""!==n.url?n.url.split(/\,/):[];if(t.each(d,function(t,e){var n=Config.upload.fullmode?Fast.api.cdnurl(e):e;r.push(n)}),o>0){var u=o-c;if(d.length>u)return Toastr.error(__("You can choose up to %d file%s",u)),!1}var p=r.join(",");s.val(p).trigger("change").trigger("validate")}else if(a){var h=Config.upload.fullmode?Fast.api.cdnurl(n.url):n.url;t("#"+a).val(h).trigger("change").trigger("validate")}}}),!1})},fieldlist:function(e){t(".fieldlist",e).length>0&&require(["dragsort","template"],function(n,i){var a=function(n){var i={},o=n.data("name"),a=t("textarea[name='"+o+"']",e),r=n.data("template");t.each(t("input,select,textarea",n).serializeArray(),function(t,e){var n=/\[(\w+)\]\[(\w+)\]$/g,o=n.exec(e.name);return!o||(o[1]="x"+parseInt(o[1]),"undefined"==typeof i[o[1]]&&(i[o[1]]={}),void(i[o[1]][o[2]]=e.value))});var s=n.data("usearray")||!1,l=n.data("keepempty")||!1,c=r||s?[]:{},d=Object.keys(Object.values(i)[0]||{}),u=!s&&d.indexOf("value")>-1&&(1===d.length||2===d.length&&d.indexOf("key")>-1);u&&2===d.length&&(c={}),t.each(i,function(t,e){e&&(u?2===d.length?(""!=e.key||l)&&(c["__PLACEHOLDKEY__"+e.key]=e.value):c.push(e.value):c.push(e))}),a.val(JSON.stringify(c).replace(/__PLACEHOLDKEY__/g,""))},r=function(e,n,a){var r=e.data("tag")||(e.is("table")?"tr":"dd"),s=e.data("index"),l=e.data("name"),c=e.data("template"),d=e.data();s=s?parseInt(s):0,e.data("index",s+1),n=n?n:{},n="undefined"==typeof n.key||"undefined"==typeof n.value?{key:"",value:n}:n;var u=e.data("fieldlist-options")||{},p={index:s,name:l,data:d,options:u,key:n.key,value:n.value,row:n.value},h=c?i(c,p):i.render(o.config.fieldlisttpl,p),f=t(h);return u.deleteBtn!==!1&&u.removeBtn!==!1||!a||f.find(".btn-remove").remove(),u.dragsortBtn===!1&&a&&f.find(".btn-dragsort").remove(),u.readonlyKey!==!0&&u.disableKey!==!0||!a||f.find("input[name$='[key]']").prop("readonly",!0),f.attr("fieldlist-item",!0),f.insertAfter(t(r+"[fieldlist-item]",e).length>0?t(r+"[fieldlist-item]:last",e):t(r+":first",e)),t(".btn-append,.append",e).length>0?t(".btn-append,.append",e).trigger("fa.event.appendfieldlist",f):e.trigger("fa.event.appendfieldlist",f),
f},s=t(".fieldlist",e);e.on("reset",function(){setTimeout(function(){s.trigger("fa.event.refreshfieldlist")})}),t(document).on("change keyup changed",".fieldlist input,.fieldlist textarea,.fieldlist select",function(){var e=t(this).closest(".fieldlist");a(e)}),s.on("click",".btn-append,.append",function(e,n){var i=t(this).closest(".fieldlist");r(i,n),a(i)}),s.on("click",".btn-remove",function(){var e=t(this).closest(".fieldlist"),n=e.data("tag")||(e.is("table")?"tr":"dd");t(this).closest(n).remove(),a(e)}),s.on("fa.event.appendtofieldlist",function(e,n){var i=t(this);r(i,n),a(i)}),s.on("fa.event.refreshfieldlist",function(){var n=t(this),i=t("textarea[name='"+n.data("name")+"']",e);t("[fieldlist-item]",n).remove();var o={};try{var a=i.val().replace(/"(\d+)"\:/g,'"__PLACEHOLDERKEY__$1":');o=JSON.parse(a)}catch(t){}t.each(o,function(t,e){r(n,{key:t.toString().replace("__PLACEHOLDERKEY__",""),value:e},!0)})}),s.each(function(){var n=t(this),i=n.data("tag")||(n.is("table")?"tr":"dd");n.dragsort({itemSelector:i,dragSelector:".btn-dragsort",dragEnd:function(){a(n)},placeHolderTemplate:t("<"+i+"/>")}),"object"==typeof n.data("options")&&n.data("options").appendBtn===!1&&t(".btn-append,.append",n).hide(),t("textarea[name='"+n.data("name")+"']",e).on("fa.event.refreshfieldlist",function(){t(this).closest(".fieldlist").trigger("fa.event.refreshfieldlist")})}),s.trigger("fa.event.refreshfieldlist")})},switcher:function(e){e.on("click","[data-toggle='switcher']",function(){if(t(this).hasClass("disabled"))return!1;var e=t.proxy(function(){var e=t(this).prev("input");if(e=t(this).data("input-id")?t("#"+t(this).data("input-id")):e,e.length>0){var n=t(this).data("yes"),i=t(this).data("no");e.val()==n?(e.val(i),t("i",this).addClass("fa-flip-horizontal text-gray")):(e.val(n),t("i",this).removeClass("fa-flip-horizontal text-gray")),e.trigger("change")}},this);return"undefined"!=typeof t(this).data("confirm")?Layer.confirm(t(this).data("confirm"),function(t){e(),Layer.close(t)}):e(),!1})},bindevent:function(t){},slider:function(e){t("[data-role='slider'],input.slider",e).length>0&&require(["bootstrap-slider"],function(){t("[data-role='slider'],input.slider").removeClass("hidden").css("width",function(e,n){return t(this).parents(".form-control").width()}).slider().on("slide",function(e){var n=t(this).data();"undefined"!=typeof n.unit&&t(this).parents(".form-control").siblings(".value").text(e.value+n.unit)})})},tagsinput:function(e){t("[data-role='tagsinput']",e).length>0&&require(["tagsinput","autocomplete"],function(){t("[data-role='tagsinput']").tagsinput(),e.on("reset",function(){setTimeout(function(){t("[data-role='tagsinput']").tagsinput("reset")},0)})})},autocomplete:function(e){t("[data-role='autocomplete']",e).length>0&&require(["autocomplete"],function(){t("[data-role='autocomplete']").autocomplete({onSelect:function(){t(this).trigger("change").trigger("validate")}})})},favisible:function(e){if(0!=t("[data-favisible]",e).length){var n=function(n){var i,o,a=n.split(/&&/),r=0,s=/^([a-z0-9\_]+)([>|<|=|\!]=?)(.*)$/i,l=/^('|")(.*)('|")$/,c=/^regex:(.*)$/,d={">":function(t,e){return t>e},">=":function(t,e){return t>=e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},"==":function(t,e){return t==e},"!=":function(t,e){return t!=e},in:function(t,e){return e.split(/\,/).indexOf(t)>-1},regex:function(t,e){var n=e.match(/^\/(.*?)\/([gim]*)$/),i=n?new RegExp(n[1],n[2]):new RegExp(e);return i.test(t)}},u=e.serializeArray(),p={};return t(u).each(function(t,e){i=e.name,o=e.value,i="[]"===i.substr(-2)?i.substr(0,i.length-2):i,p[i]="undefined"!=typeof p[i]?[p[i],o].join(","):o}),t.each(a,function(e,n){var i=s.exec(n);if(i){var o=i[1],a=i[2],u=i[3].toString();if("="===a){var h=l.exec(u);a=h?"==":"in",u=h?h[2]:u}var f=c.exec(u);f&&(a="regex",u=f[1]);var m="row["+o+"]";if("undefined"==typeof p[m])return!1;var g=p[m];t.isArray(g)&&(g=p[m].join(",")),[">",">=","<","<="].indexOf(a)>-1&&(g=parseFloat(g),u=parseFloat(u));var v=d[a](g,u);r+=v?1:0}}),r===a.length};e.on("keyup change click configchange","input,textarea,select",function(){t("[data-favisible][data-favisible!='']",e).each(function(){var e=t(this).data("favisible"),i=e?e.toString().split(/\|\|/):[],o=0;t.each(i,function(t,e){n(e)&&o++}),o>0?t(this).removeClass("hidden"):t(this).addClass("hidden")})}),setTimeout(function(){var t=e.data("validator");t&&(t.options.ignore+=(t.options.ignore?",":"")+".hidden[data-favisible] :hidden,.hidden[data-favisible]:hidden")},0),t("input,textarea,select",e).trigger("configchange")}}},api:{submit:function(e,n,i,o){if(0===e.length)return Toastr.error("表单未初始化完成,无法提交"),!1;if("function"==typeof o&&!1===o.call(e,n,i))return!1;var a=e.attr("method")?e.attr("method").toUpperCase():"GET";a=!a||"GET"!==a&&"POST"!==a?"GET":a,url=e.attr("action"),url=url?url:location.href;var r={},s=t("[name$='[]']",e);if(s.length>0){var l=e.serializeArray().map(function(e){return t(e).prop("name")});t.each(s,function(e,n){l.indexOf(t(this).prop("name"))<0&&(r[t(this).prop("name")]="")})}return Fast.api.ajax({type:a,url:url,data:e.serialize()+(Object.keys(r).length>0?"&"+t.param(r):""),dataType:"json",complete:function(e){var n=e.getResponseHeader("__token__");n&&t("input[name='__token__']").val(n)}},function(i,o){if(t(".form-group",e).removeClass("has-feedback has-success has-error"),i&&"object"==typeof i&&("undefined"!=typeof i.token&&t("input[name='__token__']").val(i.token),"undefined"!=typeof i.callback&&"function"==typeof i.callback&&i.callback.call(e,i)),"function"==typeof n&&!1===n.call(e,i,o))return!1},function(n,o){if(n&&"object"==typeof n&&"undefined"!=typeof n.token&&t("input[name='__token__']").val(n.token),"function"==typeof i&&!1===i.call(e,n,o))return!1}),!0},bindevent:function(e,n,i,a){e="object"==typeof e?e:t(e);var r=o.events;r.bindevent(e),r.validator(e,n,i,a),r.selectpicker(e),r.daterangepicker(e),r.selectpage(e),r.cxselect(e),r.citypicker(e),r.datetimepicker(e),r.faupload(e),r.faselect(e),r.fieldlist(e),r.slider(e),r.switcher(e),r.tagsinput(e),r.autocomplete(e),r.favisible(e)},custom:{}}};return o}),!function(t){"use strict";var e=[],n=t.fn.bootstrapTable.utils.sprintf,i=function(e,i){var a=o(e,i),r=n('<div class="commonsearch-table %s">',i.options.searchFormVisible?"":"hidden");r+=a,r+="</div>",i.$container.prepend(t(r)),i.$commonsearch=t(".commonsearch-table",i.$container);var s=t("form.form-commonsearch",i.$commonsearch);require(["form"],function(t){t.api.bindevent(s),s.validator("destroy")}),s.on("submit",function(t){return t.preventDefault(),i.onCommonSearch(),!1}),s.on("click","button[type=reset]",function(t){s[0].reset(),setTimeout(function(){i.onCommonSearch()},1)})},o=function(i,o){if(o.options.searchFormTemplate)return Template(o.options.searchFormTemplate,{columns:i,table:o});var s=[];s.push(n('<form class="form-horizontal form-commonsearch" novalidate method="post" action="%s" >',o.options.actionForm)),s.push("<fieldset>"),o.options.titleForm.length>0&&s.push(n("<legend>%s</legend>",o.options.titleForm)),s.push('<div class="row">');for(var l in i){var c=i[l];if(!c.checkbox&&!c.radio&&c.field&&"operate"!==c.field&&c.searchable&&c.operate!==!1){var d=Fast.api.query(c.field),u=Fast.api.query(c.field+"-operate"),p=o.options.renderDefault&&("undefined"==typeof c.renderDefault||c.renderDefault);c.defaultValue=p&&d?d:"undefined"==typeof c.defaultValue?"":c.defaultValue,c.operate=p&&u?u:"undefined"==typeof c.operate?"=":c.operate,e.push(c),s.push('<div class="form-group col-xs-12 col-sm-6 col-md-4 col-lg-3">'),s.push(n('<label for="%s" class="control-label col-xs-4">%s</label>',c.field,c.title)),s.push('<div class="col-xs-8">'),c.operate=c.operate?c.operate.toUpperCase():"=",s.push(n('<input type="hidden" class="form-control operate" name="%s-operate" data-name="%s" value="%s" readonly>',c.field,c.field,c.operate));var h="undefined"==typeof c.addClass?"undefined"==typeof c.addclass?"form-control":"form-control "+c.addclass:"form-control "+c.addClass,f="undefined"==typeof c.extend?"":c.extend,m="undefined"==typeof c.style?"":n('style="%s"',c.style);if(f="undefined"!=typeof c.data&&""==f?c.data:f,f="undefined"!=typeof c.autocomplete?f+' autocomplete="'+(c.autocomplete===!1||"off"===c.autocomplete?"off":"on")+'"':f,c.searchList)if("function"==typeof c.searchList)s.push(c.searchList.call(this,c));else{var g=[n('<option value="">%s</option>',o.options.formatCommonChoose())];"object"==typeof c.searchList&&"function"==typeof c.searchList.then?!function(e,n){t.when(e.searchList).done(function(i){var o=[];i.data&&i.data.searchlist&&t.isArray(i.data.searchlist)?o=i.data.searchlist:i.constructor!==Array&&i.constructor!==Object||(o=i);var a=r(o,e,n);t("form.form-commonsearch select[name='"+e.field+"']",n.$container).html(a.join("")).trigger("change")})}(c,o):g=r(c.searchList,c,o),s.push(n('<select class="%s" name="%s" %s %s>%s</select>',h,c.field,m,f,g.join("")))}else{var v="undefined"==typeof c.placeholder?c.title:c.placeholder,y="undefined"==typeof c.type?"text":c.type,b="undefined"==typeof c.defaultValue?"":c.defaultValue;if(/BETWEEN$/.test(c.operate)){var x=b.toString().match(/\|/)?b.split("|"):["",""],w=v.toString().match(/\|/)?v.split("|"):[v,v];s.push('<div class="row row-between">'),s.push(n('<div class="col-xs-6"><input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s-min" data-index="%s" %s %s></div>',y,h,c.field,x[0],w[0],c.field,l,m,f)),s.push(n('<div class="col-xs-6"><input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s-max" data-index="%s" %s %s></div>',y,h,c.field,x[1],w[1],c.field,l,m,f)),s.push("</div>")}else s.push(n('<input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s" data-index="%s" %s %s>',y,h,c.field,b,v,c.field,l,m,f))}s.push("</div>"),s.push("</div>")}}return s.push('<div class="form-group col-xs-12 col-sm-6 col-md-4 col-lg-3">'),s.push(a(o).join("")),s.push("</div>"),s.push("</div>"),s.push("</fieldset>"),s.push("</form>"),s.join("")},a=function(t){var e=[],i=t.options.formatCommonSubmitButton(),o=t.options.formatCommonResetButton();return e.push('<div class="col-sm-8 col-xs-offset-4">'),e.push(n('<button type="submit" class="btn btn-success" formnovalidate>%s</button> ',i)),e.push(n('<button type="reset" class="btn btn-default" >%s</button> ',o)),e.push("</div>"),e},r=function(e,i,o){var a=e.constructor===Array,r=[];return r.push(n('<option value="">%s</option>',o.options.formatCommonChoose())),t.each(e,function(t,e){e.constructor===Object?(t=e.id,e=e.name):t=a?e:t,r.push(n("<option value='"+t+"' %s>"+e+"</option>",t==i.defaultValue?"selected":""))}),r},s=function(t){return!(!t.options.commonSearch||"server"!=t.options.sidePagination||!t.options.url)},l=function(n,i){var o={},a={},r="";return t("form.form-commonsearch .operate",n.$commonsearch).each(function(s){var l=t(this).data("name"),c=t(this).is("select")?t("option:selected",this).val():t(this).val().toUpperCase(),d=t("[name='"+l+"']",n.$commonsearch);if(0==d.length)return!0;var u=e[s],p=!n.options.searchFormTemplate&&u&&"function"==typeof u.process?u.process:null;if(d.length>1)if(/BETWEEN$/.test(c)){var h=t.trim(t("[name='"+l+"']:first",n.$commonsearch).val()),f=t.trim(t("[name='"+l+"']:last",n.$commonsearch).val());h.length||f.length?(p&&(h=p(h,"begin"),f=p(f,"end")),r=h+","+f):r="",t("[name='"+l+"']:first",n.$commonsearch).hasClass("datetimepicker")&&(c="RANGE")}else r=t("[name='"+l+"']:checked",n.$commonsearch).val(),r=p?p(r):r;else r=p?p(d.val()):d.val();return!(!i||!(""==r||null==r||t.isArray(r)&&0==r.length)||c.match(/null/i))||(o[l]=c,void(a[l]=r))}),{op:o,filter:a}},c=function(e,n,i){return e.filter="Object"==typeof e.filter?e.filter:e.filter?JSON.parse(e.filter):{},e.op="Object"==typeof e.op?e.op:e.op?JSON.parse(e.op):{},e.filter=t.extend({},e.filter,n.filter),e.op=t.extend({},e.op,n.op),i&&t.each(e.filter,function(n,i){(""==i||null==i||t.isArray(i)&&0==i.length)&&!e.op[n].match(/null/i)&&(delete e.filter[n],delete e.op[n])}),e.filter=JSON.stringify(e.filter),e.op=JSON.stringify(e.op),e};t.extend(t.fn.bootstrapTable.defaults,{commonSearch:!1,titleForm:"Common search",actionForm:"",searchFormTemplate:"",searchFormVisible:!0,searchClass:"searchit",showSearch:!0,renderDefault:!0,onCommonSearch:function(t,e){return!1},onPostCommonSearch:function(t){return!1}}),t.extend(t.fn.bootstrapTable.defaults.icons,{commonSearchIcon:"glyphicon-search"}),t.extend(t.fn.bootstrapTable.Constructor.EVENTS,{"common-search.bs.table":"onCommonSearch","post-common-search.bs.table":"onPostCommonSearch"}),t.extend(t.fn.bootstrapTable.locales[t.fn.bootstrapTable.defaults.locale],{formatCommonSearch:function(){return"Common search"},formatCommonSubmitButton:function(){return"Submit"},formatCommonResetButton:function(){return"Reset"},formatCommonCloseButton:function(){return"Close"},formatCommonChoose:function(){return"Choose"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales);var d=t.fn.bootstrapTable.Constructor,u=d.prototype.initHeader,p=d.prototype.initToolbar,h=d.prototype.load,f=d.prototype.initSearch;d.prototype.initHeader=function(){u.apply(this,Array.prototype.slice.apply(arguments)),this.$header.find("th[data-field]").each(function(e){var n=t(this).data();"undefined"!=typeof n.width&&n.width.toString().indexOf("%")===-1&&(t(".th-inner",this).outerWidth(n.width),t(this).css("max-width",n.width))}),this.options.stateField=this.header.stateField},d.prototype.initToolbar=function(){if(p.apply(this,Array.prototype.slice.apply(arguments)),s(this)){var e=this,o=[];e.options.showSearch&&(o.push(n('<div class="columns-%s pull-%s" style="margin-top:10px;margin-bottom:10px;">',this.options.buttonsAlign,this.options.buttonsAlign)),o.push(n('<button class="btn btn-default%s" type="button" name="commonSearch" title="%s">',void 0===e.options.iconSize?"":" btn-"+e.options.iconSize,e.options.formatCommonSearch())),o.push(n('<i class="%s %s"></i>',e.options.iconsPrefix,e.options.icons.commonSearchIcon)),o.push("</button></div>")),e.$toolbar.find(".pull-right").length>0?t(o.join("")).insertBefore(e.$toolbar.find(".pull-right:first")):e.$toolbar.append(o.join("")),i(e.columns,e),e.$toolbar.find('button[name="commonSearch"]').off("click").on("click",function(){e.$commonsearch.toggleClass("hidden")}),e.$container.on("click","."+e.options.searchClass,function(){var n=t(this).data("value"),i=t(this).data("field"),o=e.$container.closest(".panel-intro").find("ul[data-field='"+i+"']");if(o.length>0)return void t('li a[data-value="'+n+'"][data-toggle="tab"]',o).trigger("click");var a=t("form [name='"+i+"']",e.$commonsearch);a.length>0&&(a.is("select")?t("option[value='"+n+"']",a).prop("selected",!0):a.length>1?t("form [name='"+i+"'][value='"+n+"']",e.$commonsearch).prop("checked",!0):a.val(n+""),a.trigger("change"),t("form",e.$commonsearch).trigger("submit"))});var a=e.options.queryParams;this.options.queryParams=function(t){return a(c(t,l(e,!0)))},this.trigger("post-common-search",e)}},d.prototype.onCommonSearch=function(){var t=l(this);this.trigger("common-search",this,t),this.options.pageNumber=1,this.refresh({})},d.prototype.load=function(t){h.apply(this,Array.prototype.slice.apply(arguments)),!s(this)},d.prototype.initSearch=function(){if(f.apply(this,Array.prototype.slice.apply(arguments)),s(this)){var e=this,n=t.isEmptyObject(this.filterColumnsPartial)?null:this.filterColumnsPartial;this.data=n?t.grep(this.data,function(i,o){for(var a in n){var r=n[a].toLowerCase(),s=i[a];if(s=t.fn.bootstrapTable.utils.calculateObjectValue(e.header,e.header.formatters[t.inArray(a,e.header.fields)],[s,i,o],s),t.inArray(a,e.header.fields)===-1||"string"!=typeof s&&"number"!=typeof s||(s+"").toLowerCase().indexOf(r)===-1)return!1}return!0}):this.data}}}(jQuery),define("bootstrap-table-commonsearch",["bootstrap-table"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable.defaults}}(this)),!function(t){"use strict";t.extend(t.fn.bootstrapTable.defaults,{templateView:!1,templateFormatter:"itemtpl",templateParentClass:"row row-flex",templateTableClass:"table-template"});var e=t.fn.bootstrapTable.Constructor,n=e.prototype.initContainer,i=e.prototype.initBody,o=e.prototype.initRow;e.prototype.initContainer=function(){n.apply(this,Array.prototype.slice.apply(arguments));var t=this;t.options.templateView&&(t.options.cardView=!0)},e.prototype.initBody=function(){var e=this;t.extend(e.options,{showHeader:!e.options.templateView&&t.fn.bootstrapTable.defaults.showHeader,showFooter:!e.options.templateView&&t.fn.bootstrapTable.defaults.showFooter}),t(e.$el).toggleClass(e.options.templateTableClass,e.options.templateView),i.apply(this,Array.prototype.slice.apply(arguments)),e.options.templateView&&t("> *:not(.no-records-found)",e.$body).wrapAll(t("<div />").addClass(e.options.templateParentClass))},e.prototype.initRow=function(t,e,n,i){var a=this;if(!a.options.templateView)return o.apply(a,Array.prototype.slice.apply(arguments));var r="";if("function"==typeof a.options.templateFormatter)r=a.options.templateFormatter.call(a,t,e,n);else{var s=require("template");r=s(a.options.templateFormatter,{item:t,i:e,data:n})}return r}}(jQuery),define("bootstrap-table-template",["bootstrap-table","template"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";var e=t.fn.bootstrapTable.utils.sprintf;t.extend(t.fn.bootstrapTable.defaults,{showJumpto:!1,exportOptions:{}}),t.extend(t.fn.bootstrapTable.locales,{formatJumpto:function(){return"GO"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales);var n=t.fn.bootstrapTable.Constructor,i=n.prototype.initPagination;n.prototype.initPagination=function(){if(this.showToolbar=this.options.showExport,i.apply(this,Array.prototype.slice.apply(arguments)),this.options.showJumpto){var n=this,o=this.$pagination.find("ul.pagination"),a=o.find("li.jumpto");a.length||(a=t(['<li class="jumpto">','<input type="text" class="form-control">','<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" title="'+this.options.formatJumpto()+'"  type="button">'+this.options.formatJumpto(),"</button>","</li>"].join("")).appendTo(o),a.find("button").click(function(){n.selectPage(parseInt(a.find("input").val()))}))}}}(jQuery),define("bootstrap-table-jumpto",["bootstrap-table"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";function e(t){var e=0,a=0,r=0,s=0;return"detail"in t&&(a=t.detail),"wheelDelta"in t&&(a=-t.wheelDelta/120),"wheelDeltaY"in t&&(a=-t.wheelDeltaY/120),"wheelDeltaX"in t&&(e=-t.wheelDeltaX/120),"axis"in t&&t.axis===t.HORIZONTAL_AXIS&&(e=a,a=0),r=e*n,s=a*n,"deltaY"in t&&(s=t.deltaY),"deltaX"in t&&(r=t.deltaX),(r||s)&&t.deltaMode&&(1===t.deltaMode?(r*=i,s*=i):(r*=o,s*=o)),r&&!e&&(e=r<1?-1:1),s&&!a&&(a=s<1?-1:1),{spinX:e,spinY:a,pixelX:r,pixelY:s}}var n=10,i=40,o=800,a=null,r=function(){if(null===a){var e=t("<p/>").addClass("fixed-table-scroll-inner"),n=t("<div/>").addClass("fixed-table-scroll-outer"),i=void 0,o=void 0;n.append(e),t("body").append(n),i=e[0].offsetWidth,n.css("overflow","scroll"),o=e[0].offsetWidth,i===o&&(o=n[0].clientWidth),n.remove(),a=i-o}return a},s=function(t){return t[0].scrollHeight>t[0].clientHeight?15:0};t.extend(t.fn.bootstrapTable.defaults,{fixedColumns:!1,fixedNumber:0,fixedRightNumber:0});var l=t.fn.bootstrapTable.Constructor,c=l.prototype.initBody,d=l.prototype.initContainer,u=l.prototype.trigger,p=l.prototype.hideLoading,h=l.prototype.updateSelected;l.prototype.fixedColumnsSupported=function(){var t=this;return t.options.fixedColumns&&!t.options.detailView&&!t.options.cardView},l.prototype.initFixedContainer=function(){this.options.fixedNumber&&(0==this.$tableContainer.find(".fixed-columns").length&&this.$tableContainer.append('<div class="fixed-columns"></div>'),this.$fixedColumns=this.$tableContainer.find(".fixed-columns")),this.options.fixedRightNumber&&(0==this.$tableContainer.find(".fixed-columns-right").length&&this.$tableContainer.append('<div class="fixed-columns-right"></div>'),this.$fixedColumnsRight=this.$tableContainer.find(".fixed-columns-right"))},l.prototype.initContainer=function(){d.apply(this,Array.prototype.slice.apply(arguments)),this.initFixedContainer()},l.prototype.initBody=function(){c.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&(this.options.showHeader&&this.options.height||(this.initFixedColumnsBody(),this.initFixedColumnsEvents()))},l.prototype.trigger=function(){var t=this;u.apply(this,Array.prototype.slice.apply(arguments)),"pre-body"===arguments[0]&&this.options.cardView&&this.$tableBody.css("height","auto"),"toggle"===arguments[0]&&(arguments[1]?(this.$tableBody.css("height","auto"),this.$fixedColumns&&this.$fixedColumns.hide(),this.$fixedColumnsRight&&this.$fixedColumnsRight.hide()):(this.$tableBody.css("height","100%"),this.$fixedColumns&&this.$fixedColumns.show(),this.$fixedColumnsRight&&this.$fixedColumnsRight.show(),this.$fixedHeaderRight&&this.$fixedHeaderRight.scrollLeft(this.$tableBody.find("table").width()),this.$fixedBodyRight&&this.$fixedBodyRight.scrollLeft(this.$tableBody.find("table").width()))),t.fixedColumnsSupported()&&("post-header"===arguments[0]?this.initFixedColumnsHeader():"scroll-body"===arguments[0]?(this.needFixedColumns&&this.options.fixedNumber&&this.$fixedBody&&this.$fixedBody.scrollTop(this.$tableBody.scrollTop()),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedBodyRight&&this.$fixedBodyRight.scrollTop(this.$tableBody.scrollTop())):"load-success"===arguments[0]&&this.hideLoading())},l.prototype.updateSelected=function(){var e=this;h.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&this.$tableBody.find("tr").each(function(n,i){var o=t(i),a=o.data("index"),r=o.attr("class"),s='[name="'+e.options.selectItemName+'"]',l=o.find(s);if("undefined"!=typeof a){var c=function(t,n){var i=n.find('tr[data-index="'+a+'"]');i.attr("class",r),l.length&&i.find(s).prop("checked",l.prop("checked")),e.$selectAll.length&&t.add(n).find('[name="btSelectAll"]').prop("checked",e.$selectAll.prop("checked"))};e.$fixedBody&&e.options.fixedNumber&&c(e.$fixedHeader,e.$fixedBody),e.$fixedBodyRight&&e.options.fixedRightNumber&&c(e.$fixedHeaderRight,e.$fixedBodyRight)}})},l.prototype.hideLoading=function(){p.apply(this,Array.prototype.slice.apply(arguments)),this.needFixedColumns&&this.options.fixedNumber&&this.$fixedColumns.find(".fixed-table-loading").hide(),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedColumnsRight.find(".fixed-table-loading").hide()},l.prototype.initFixedColumnsHeader=function(){var t=this;this.options.height?this.needFixedColumns=this.$tableHeader.outerWidth(!0)<this.$tableHeader.find("table").outerWidth(!0):this.needFixedColumns=this.$tableBody.outerWidth(!0)<this.$tableBody.find("table").outerWidth(!0);var e=function(e,n){return e.find(".fixed-table-header").remove(),e.append(t.$tableHeader.clone(!0)),e.find(".fixed-table-header").css("margin-right",""),e.css({width:t.getFixedColumnsWidth(n)}),e.find(".fixed-table-header")};this.needFixedColumns&&this.options.fixedNumber?(this.$fixedHeader=e(this.$fixedColumns),this.$fixedHeader.css("margin-right","")):this.$fixedColumns&&this.$fixedColumns.html("").css("width",""),this.needFixedColumns&&this.options.fixedRightNumber?(this.$fixedHeaderRight=e(this.$fixedColumnsRight,!0),this.$fixedHeaderRight.scrollLeft(this.$fixedHeaderRight.find("table").width())):this.$fixedColumnsRight&&this.$fixedColumnsRight.html("").css("width",""),this.initFixedColumnsBody(),this.initFixedColumnsEvents()},l.prototype.initFixedColumnsBody=function(){var e=this,n=function(n,i){n.find(".fixed-table-body").remove(),n.append(e.$tableBody.clone(!0));var o=n.find(".fixed-table-body"),a=e.$tableBody.get(0),s=function(){var s=a.scrollWidth>a.clientWidth?r():0,l=t(".fixed-table-pagination",e.$tableContainer).height();"undefined"!=typeof e.options.height&&(l=0),n.css({height:"calc(100% - "+(l+s)+"px)"}),o.css({height:"calc(100% - "+i.height()+"px)",overflow:"hidden"})};return t(window).on("resize",s),s(),o};this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody=n(this.$fixedColumns,this.$fixedHeader)),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight=n(this.$fixedColumnsRight,this.$fixedHeaderRight),this.$fixedBodyRight.scrollLeft(this.$fixedBodyRight.find("table").width()),this.$fixedBodyRight.css("overflow-y","hidden"))},l.prototype.getFixedColumnsWidth=function(t){var e=this.getVisibleFields(),n=0,i=this.options.fixedNumber;t&&(e=e.reverse(),i=this.options.fixedRightNumber,this.$fixedColumnsRight.css("right",s(this.$tableBody)));for(var o=0;o<i;o++)n+=this.$header.find('th[data-field="'+e[o]+'"]').outerWidth();return n+1},l.prototype.initFixedColumnsEvents=function(){var n=this,i=function(e,i){var o='tr[data-index="'+t(e.currentTarget).data("index")+'"]',a=n.$tableBody.find(o);n.$fixedBody&&(a=a.add(n.$fixedBody.find(o))),n.$fixedBodyRight&&(a=a.add(n.$fixedBodyRight.find(o))),a.css("background-color",i?t(e.currentTarget).css("background-color"):"")};this.$tableBody.find("tr").hover(function(t){i(t,!0)},function(t){i(t,!1)});var o="undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().indexOf("firefox")>-1,a=o?"DOMMouseScroll":"mousewheel",r=function(t,i){var o=e(t),a=Math.ceil(o.pixelY),r=n.$tableBody.scrollTop()+a;(a<0&&r>0||a>0&&r<i.scrollHeight-i.clientHeight)&&t.preventDefault(),n.$tableBody.scrollTop(r),n.$fixedBody&&n.$fixedBody.scrollTop(r),n.$fixedBodyRight&&n.$fixedBodyRight.scrollTop(r)};this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody.find("tr").hover(function(t){i(t,!0)},function(t){i(t,!1)}),this.$fixedBody[0].addEventListener(a,function(t){r(t,n.$fixedBody[0])}),this.$fixedBody.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(e){e.stopImmediatePropagation();var i=t(e.target).data("index");t(n.$selectItem[i]).trigger("click")}),this.$fixedBody.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var i=t(this).closest("tr[data-index]").data("index");t(n.$selectItem[i]).closest("tr[data-index]").find(">td:eq("+t(this).index()+")").trigger("click")})),t("div.fixed-table-body").off("scroll"),this.$tableBody.off("scroll").on("scroll",function(t){n.$tableHeader.scrollLeft(0),n.$tableBody.scrollLeft()>0&&(n.$tableHeader.scrollLeft(n.$tableBody.scrollLeft()),n.options.showFooter&&!n.options.cardView&&n.$tableFooter.scrollLeft(n.$tableBody.scrollLeft()));var e=n.$tableBody.scrollTop();n.$fixedBody&&n.$fixedBody.scrollTop(e),n.$fixedBodyRight&&n.$fixedBodyRight.scrollTop(e)}),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight.find("tr").hover(function(t){i(t,!0)},function(t){i(t,!1)}),this.$fixedBodyRight[0].addEventListener(a,function(t){r(t,n.$fixedBodyRight[0])}),this.$fixedBodyRight.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(e){e.stopImmediatePropagation();var i=t(e.target).data("index");t(n.$selectItem[i]).trigger("click")}),this.$fixedBodyRight.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var i=t(this).closest("tr[data-index]").data("index");t(n.$selectItem[i]).closest("tr[data-index]").find(">td:eq("+t(this).index()+")").trigger("click")})),this.options.filterControl&&t(this.$fixedColumns).off("keyup change").on("keyup change",function(e){var i=t(e.target),o=i.val(),a=i.parents("th").data("field"),r=n.$header.find('th[data-field="'+a+'"]');if(i.is("input"))r.find("input").val(o);else if(i.is("select")){var s=r.find("select");s.find("option[selected]").removeAttr("selected"),s.find('option[value="'+o+'"]').attr("selected",!0)}n.triggerSearch()})}}(jQuery),define("bootstrap-table-fixed-columns",["bootstrap-table"],function(t){return function(){var e;return e||t.$.fn.bootstrapTable.defaults}}(this)),define("table",["jquery","bootstrap","moment","moment/locale/zh-cn","bootstrap-table","bootstrap-table-lang","bootstrap-table-export","bootstrap-table-commonsearch","bootstrap-table-template","bootstrap-table-jumpto","bootstrap-table-fixed-columns"],function(t,e,n){var i={list:{},defaults:{url:"",sidePagination:"server",method:"get",toolbar:".toolbar",search:!0,cache:!1,commonSearch:!0,searchFormVisible:!1,titleForm:"",idTable:"commonTable",showExport:!0,exportDataType:"auto",exportTypes:["json","xml","csv","txt","doc","excel"],exportOptions:{fileName:"export_"+n().format("YYYY-MM-DD"),preventInjection:!1,mso:{onMsoNumberFormat:function(e,n,i){return isNaN(t(e).text())?"":"\\@"}},ignoreColumn:[0,"operate"]},pageSize:Config.pagesize||localStorage.getItem("pagesize")||10,pageList:[10,15,20,25,50,"All"],pagination:!0,clickToSelect:!0,dblClickToEdit:!0,singleSelect:!1,showRefresh:!1,showJumpto:!0,locale:"zh-cn"===Config.language?"zh-CN":"en-US",showToggle:!0,showColumns:!0,pk:"id",sortName:"id",sortOrder:"desc",paginationFirstText:__("First"),paginationPreText:__("Previous"),paginationNextText:__("Next"),paginationLastText:__("Last"),cardView:!1,iosCardView:!0,checkOnInit:!0,escape:!0,fixDropdownPosition:!0,dragCheckboxMultiselect:!0,selectedIds:[],selectedData:[],extend:{index_url:"",add_url:"",edit_url:"",del_url:"",import_url:"",multi_url:"",dragsort_url:"ajax/weigh"}},columnDefaults:{align:"center",valign:"middle"},config:{checkboxtd:"tbody>tr>td.bs-checkbox",toolbar:".toolbar",refreshbtn:".btn-refresh",addbtn:".btn-add",editbtn:".btn-edit",delbtn:".btn-del",importbtn:".btn-import",multibtn:".btn-multi",disabledbtn:".btn-disabled",editonebtn:".btn-editone",restoreonebtn:".btn-restoreone",destroyonebtn:".btn-destroyone",restoreallbtn:".btn-restoreall",destroyallbtn:".btn-destroyall",dragsortfield:"weigh"},button:{edit:{name:"edit",icon:"fa fa-pencil",title:__("Edit"),extend:'data-toggle="tooltip" data-container="body"',classname:"btn btn-xs btn-success btn-editone"},del:{name:"del",icon:"fa fa-trash",title:__("Del"),extend:'data-toggle="tooltip" data-container="body"',classname:"btn btn-xs btn-danger btn-delone"},dragsort:{name:"dragsort",icon:"fa fa-arrows",title:__("Drag to sort"),extend:'data-toggle="tooltip"',classname:"btn btn-xs btn-primary btn-dragsort"}},api:{init:function(e,n,o){e=e?e:{},n=n?n:{},o=o?o:{},t.fn.bootstrapTable.Constructor.prototype.getSelectItem=function(){return this.$selectItem};var a=t.fn.bootstrapTable.Constructor.prototype.onPageListChange;t.fn.bootstrapTable.Constructor.prototype.onPageListChange=function(){return a.apply(this,Array.prototype.slice.apply(arguments)),localStorage.setItem("pagesize",this.options.pageSize),!1},t.extend(!0,t.fn.bootstrapTable.defaults,i.defaults,e),t.extend(t.fn.bootstrapTable.columnDefaults,i.columnDefaults,n),t.extend(t.fn.bootstrapTable.locales[i.defaults.locale],{formatCommonSearch:function(){return __("Common search")},formatCommonSubmitButton:function(){return __("Submit")},formatCommonResetButton:function(){return __("Reset")},formatCommonCloseButton:function(){return __("Close")},formatCommonChoose:function(){return __("Choose")},formatJumpto:function(){return __("Go")}},o),t.fn.bootstrapTable.defaults.iosCardView&&navigator.userAgent.match(/(iPod|iPhone|iPad)/)&&(i.defaults.cardView=!0,t.fn.bootstrapTable.defaults.cardView=!0),"undefined"!=typeof e.exportTypes&&(t.fn.bootstrapTable.defaults.exportTypes=e.exportTypes)},bindevent:function(e){var n=e.closest(".bootstrap-table"),o=e.bootstrapTable("getOptions"),a=t(o.toolbar,n),r=t(".btn-selected-tips",n);0===r.length&&(r=t('<a href="javascript:" class="btn btn-warning-light btn-selected-tips hide" data-animation="false" data-toggle="tooltip" data-title="'+__("Click to uncheck all")+'"><i class="fa fa-info-circle"></i> '+__("Multiple selection mode: %s checked","<b>0</b>")+"</a>").appendTo(a)),r.off("click").on("click",function(t){e.trigger("uncheckbox"),e.bootstrapTable("refresh")}),e.on("uncheckbox",function(t,e,n){o.selectedIds=[],o.selectedData=[],
r.tooltip("hide"),r.addClass("hide")}),e.on("load-error.bs.table",function(t,e,n){0!==n.status&&Toastr.error(__("Unknown data format"))}),e.on("load-success.bs.table",function(t,e){"undefined"==typeof e.rows&&"undefined"!=typeof e.code&&Toastr.error(e.msg)}),e.on("refresh.bs.table",function(e,n,o){t(i.config.refreshbtn,a).find(".fa").addClass("fa-spin"),t(".layui-layer-autocontent").remove()}),e.on("search.bs.table common-search.bs.table",function(t,n,i){e.trigger("uncheckbox")}),o.dblClickToEdit&&e.on("dbl-click-row.bs.table",function(e,n,o,a){t(i.config.editonebtn,o).trigger("click")}),e.on("pre-body.bs.table",function(e,n){o.maintainSelected&&t.each(n,function(e,n){n[o.stateField]=t.inArray(n[o.pk],o.selectedIds)>-1})}),e.on("post-body.bs.table",function(n,r){if(t(i.config.refreshbtn,a).find(".fa").removeClass("fa-spin"),t(i.config.checkboxtd+":first",e).find("input[type='checkbox'][data-index]").length>0){var s,l,c,d=!1,u=!1,p=function(n){if(d){var o=Math.min(n.pageX,s),a=Math.min(n.pageY,l),r=Math.abs(s-n.pageX),u=Math.abs(l-n.pageY);c.css({left:o+"px",top:a+"px",width:r+"px",height:u+"px"});var p={x:o,y:a,width:r,height:u};t(i.config.checkboxtd,e).each(function(){var e=t("input:checkbox",this),n=this.getBoundingClientRect();n.x+=document.documentElement.scrollLeft,n.y+=document.documentElement.scrollTop;var i=n.x,o=n.y,a=n.x+n.width,r=n.y+n.height,s=p.x,l=p.y,c=p.x+p.width,d=p.y+p.height,u=i<=c&&a>=s&&o<=d&&r>=l;u?t(this).hasClass("overlaped")||(t(this).addClass("overlaped"),e.trigger("click")):t(this).hasClass("overlaped")&&(t(this).removeClass("overlaped"),e.trigger("click"))})}},h=function(){return!1},f=function(){d&&(t(document).off("mousemove",p),t(document).off("selectstart",h),c.remove()),d=!1,u=!1,t(document.body).css({MozUserSelect:"",webkitUserSelect:""}).attr("unselectable","off")};t(i.config.checkboxtd,e).on("mousedown",function(e){return 2!==e.button&&!t(e.target).is("input")&&(s=e.pageX,l=e.pageY,void(u=!0))}).on("mousemove",function(n){u&&!d&&(d=!0,c=t("<div />"),c.css({position:"absolute",width:0,height:0,border:"1px dashed blue",background:"#0029ff",left:n.pageX+"px",top:n.pageY+"px",opacity:.1}),c.appendTo(document.body),t(document.body).css({MozUserSelect:"none",webkitUserSelect:"none"}).attr("unselectable","on"),t(document).on("mousemove",p).on("mouseup",f).on("selectstart",h),o.dragCheckboxMultiselect&&t(i.config.checkboxtd,e).removeClass("overlaped"))})}});var s=o.exportDataType;if(e.on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table post-body.bs.table",function(n){var l=[];t.each(e.bootstrapTable("getData"),function(t,e){l.push("undefined"!=typeof e[o.pk]?e[o.pk]:"")});var c=i.api.selectedids(e,!0),d=i.api.selecteddata(e,!0);o.maintainSelected?(o.selectedIds=o.selectedIds.filter(function(e,n,i){return t.inArray(e,l)===-1}).concat(c),o.selectedData=o.selectedData.filter(function(e,n,i){return t.inArray(e[o.pk],l)===-1}).concat(d),o.selectedIds.length>c.length?(t("b",r).text(o.selectedIds.length),r.removeClass("hide")):r.addClass("hide")):(o.selectedIds=c,o.selectedData=d),"auto"===s&&(o.exportDataType=c.length>0?"selected":"all",0===t(".export .exporttips").length&&t(".export .dropdown-menu").prepend("<li class='exporttips alert alert-warning-light mb-0 no-border p-2'></li>"),t(".export .exporttips").html("导出记录："+(c.length>0?"选中":"全部"))),t(i.config.disabledbtn,a).toggleClass("disabled",!o.selectedIds.length)}),e.on("common-search.bs.table",function(n,i,o){var a=t(".panel-heading [data-field]",e.closest(".panel-intro")),r=a.data("field"),s=t("li.active > a",a).data("value");o.filter&&"undefined"!=typeof o.filter[r]&&o.filter[r]!=s&&(t("li",a).removeClass("active"),t("li > a[data-value='"+o.filter[r]+"']",a).parent().addClass("active"))}),t('.panel-heading [data-field] a[data-toggle="tab"]',e.closest(".panel-intro")).on("shown.bs.tab",function(n){var i=t(this).closest("[data-field]").data("field"),o=t(this).data("value"),a=t("[name='"+i+"']",e.closest(".bootstrap-table").find(".commonsearch-table"));return"SELECT"===a.prop("tagName")?t("option[value='"+o+"']",a).prop("selected",!0):a.val(o),e.trigger("uncheckbox"),e.bootstrapTable("getOptions").totalRows=0,e.bootstrapTable("refresh",{pageNumber:1}),!1}),t("form",e.closest(".bootstrap-table").find(".commonsearch-table")).on("reset",function(){setTimeout(function(){},0),t(".panel-heading [data-field] li",e.closest(".panel-intro")).removeClass("active"),t(".panel-heading [data-field] li:first",e.closest(".panel-intro")).addClass("active")}),a.on("click",i.config.refreshbtn,function(){e.bootstrapTable("refresh")}),a.on("click",i.config.addbtn,function(){var n=i.api.selectedids(e),a=o.extend.add_url;a.indexOf("{ids}")!==-1&&(a=i.api.replaceurl(a,{ids:n.length>0?n.join(","):0},e)),Fast.api.open(a,t(this).data("original-title")||t(this).attr("title")||__("Add"),t(this).data()||{})}),t(i.config.importbtn,a).length>0&&require(["upload"],function(n){n.api.upload(t(i.config.importbtn,a),function(t,n){Fast.api.ajax({url:o.extend.import_url,data:{file:t.url}},function(t,n){e.trigger("uncheckbox"),e.bootstrapTable("refresh")})})}),a.on("click",i.config.editbtn,function(){var n=this,a=i.api.selectedids(e);if(!(a.length>10)){var r=t(n).data("title")||t(n).attr("title")||__("Edit"),s=t(n).data()||{};delete s.title,t.each(i.api.selecteddata(e),function(n,a){var l=o.extend.edit_url;a=t.extend({},a?a:{},{ids:a[o.pk]}),l=i.api.replaceurl(l,a,e),Fast.api.open(l,"function"==typeof r?r.call(e,a):r,s)})}}),t(document).on("click",i.config.destroyallbtn,function(){var n=this;return Layer.confirm(__("Are you sure you want to truncate?"),function(){var i=t(n).data("url")?t(n).data("url"):t(n).attr("href");Fast.api.ajax(i,function(){Layer.closeAll(),e.trigger("uncheckbox"),e.bootstrapTable("refresh")},function(){Layer.closeAll()})}),!1}),t(document).on("click",i.config.restoreallbtn,function(){var n=this,i=t(n).data("url")?t(n).data("url"):t(n).attr("href");return Fast.api.ajax(i,function(){Layer.closeAll(),e.trigger("uncheckbox"),e.bootstrapTable("refresh")},function(){Layer.closeAll()}),!1}),t(document).on("click",i.config.restoreonebtn+","+i.config.destroyonebtn,function(){var n=this,a=t(n).data("url")?t(n).data("url"):t(n).attr("href"),r=i.api.getrowbyindex(e,t(n).data("row-index"));return Fast.api.ajax({url:a,data:{ids:r[o.pk]}},function(){e.trigger("uncheckbox"),e.bootstrapTable("refresh")}),!1}),a.on("click",i.config.multibtn,function(){var n=i.api.selectedids(e);i.api.multi(t(this).data("action"),n,e,this)}),a.on("click",i.config.delbtn,function(){var t=this,n=i.api.selectedids(e);Layer.confirm(__("Are you sure you want to delete the %s selected item?",n.length),{icon:3,title:__("Warning"),offset:0,shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(o){i.api.multi("del",n,e,t),Layer.close(o)})}),require(["dragsort"],function(){t("tbody",e).dragsort({itemSelector:"tr:visible",dragSelector:"a.btn-dragsort",dragEnd:function(n,o){var a=t("a.btn-dragsort",this),r=e.bootstrapTable("getData"),s=r[parseInt(t(this).data("index"))],l=e.bootstrapTable("getOptions"),c=t.map(t("tbody tr:visible",e),function(e){return r[parseInt(t(e).data("index"))][l.pk]}),d=s[l.pk],u="undefined"!=typeof s.pid?s.pid:"",p={url:e.bootstrapTable("getOptions").extend.dragsort_url,data:{ids:c.join(","),changeid:d,pid:u,field:i.config.dragsortfield,orderway:l.sortOrder,table:l.extend.table,pk:l.pk}};Fast.api.ajax(p,function(n,i){var o=t(a).data("success")||t.noop;return("function"!=typeof o||!1!==o.call(a,n,i))&&void e.bootstrapTable("refresh")},function(n,i){var o=t(a).data("error")||t.noop;return("function"!=typeof o||!1!==o.call(a,n,i))&&void e.bootstrapTable("refresh")})},placeHolderTemplate:""})}),e.on("click","input[data-id][name='checkbox']",function(n){var i=t(this).data("id");e.bootstrapTable(t(this).prop("checked")?"checkBy":"uncheckBy",{field:o.pk,values:[i]})}),e.on("click","[data-id].btn-change",function(n){n.preventDefault();var o=t.proxy(function(){i.api.multi(t(this).data("action")?t(this).data("action"):"",[t(this).data("id")],e,this)},this);"undefined"!=typeof t(this).data("confirm")?Layer.confirm(t(this).data("confirm"),function(t){o(),Layer.close(t)}):o()}),e.on("click","[data-id].btn-edit",function(n){n.preventDefault();var a=t(this).data("id"),r=i.api.getrowbyid(e,a);r.ids=a;var s=i.api.replaceurl(o.extend.edit_url,r,e);Fast.api.open(s,t(this).data("original-title")||t(this).attr("title")||__("Edit"),t(this).data()||{})}),e.on("click","[data-id].btn-del",function(n){n.preventDefault();var o=t(this).data("id"),a=this;Layer.confirm(__("Are you sure you want to delete this item?"),{icon:3,title:__("Warning"),shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(t){i.api.multi("del",o,e,a),Layer.close(t)})}),e.on("mouseenter mouseleave",".autocontent",function(e){var n=t(".autocontent-item",this).get(0);n&&("mouseenter"===e.type?n.scrollWidth>n.offsetWidth&&0===t(".autocontent-caret",this).length&&t(this).append("<div class='autocontent-caret'><i class='fa fa-chevron-down'></div>"):t(".autocontent-caret",this).remove())}),e.on("click mouseenter",".autocontent-caret",function(e){var n=t(this).prev().hasClass("autocontent-hover");if(n||"mouseenter"!==e.type){var i=t(this).prev().text(),o=t(this).parent().get(0).getBoundingClientRect(),a=Layer.open({id:"autocontent",skin:"layui-layer-fast layui-layer-autocontent",title:!1,content:i,btn:!1,anim:!1,shade:0,isOutAnim:!1,area:"auto",maxWidth:450,maxHeight:350,offset:[o.y,o.x]});n&&t(document).one("mouseleave","#layui-layer"+a,function(){Layer.close(a)});var r=function(e){0===t(e.target).closest(".layui-layer").length&&(Layer.close(a),t(document).off("mousedown",r))};t(document).off("mousedown",r).on("mousedown",r)}}),o.fixDropdownPosition){var l=e.closest(".fixed-table-body");e.on("show.bs.dropdown fa.event.refreshdropdown",".btn-group",function(e){var n,i,o,a=t(".dropdown-menu",this),r=t(this),s=a.hasClass("pull-right")||a.hasClass("dropdown-menu-right");o="fixed",i=r.offset().top-t(window).scrollTop()+r.outerHeight(),i+a.outerHeight()>t(window).height()&&(i=r.offset().top-a.outerHeight()-5),n=s?r.offset().left+r.outerWidth()-a.outerWidth():r.offset().left,(n||i)&&a.css({position:o,left:n,top:i,right:"inherit"})});var c=function(){t(".btn-group.open",e).length>0&&"fixed"==t(".btn-group.open .dropdown-menu",e).css("position")&&t(".btn-group.open",e).trigger("fa.event.refreshdropdown")};t(window).on("scroll",function(){c()}),l.on("scroll",function(){c()})}var d=e.attr("id");return i.list[d]=e,e},multi:function(e,n,i,o){var a=i.bootstrapTable("getOptions"),r=o?t(o).data():{};n=t.isArray(n)?n.join(","):n;var s="undefined"!=typeof r.url?r.url:"del"==e?a.extend.del_url:a.extend.multi_url,l="undefined"!=typeof r.params?"object"==typeof r.params?t.param(r.params):r.params:"";a={url:s,data:{action:e,ids:n,params:l}},Fast.api.ajax(a,function(e,n){i.trigger("uncheckbox");var a=t(o).data("success")||t.noop;return("function"!=typeof a||!1!==a.call(o,e,n))&&void i.bootstrapTable("refresh")},function(e,n){var i=t(o).data("error")||t.noop;if("function"==typeof i&&!1===i.call(o,e,n))return!1})},events:{operate:{"click .btn-editone":function(e,n,o,a){e.stopPropagation(),e.preventDefault();var r=t(this).closest("table"),s=r.bootstrapTable("getOptions"),l=o[s.pk];o=t.extend({},o?o:{},{ids:l});var c=s.extend.edit_url;Fast.api.open(i.api.replaceurl(c,o,r),t(this).data("original-title")||t(this).attr("title")||__("Edit"),t(this).data()||{})},"click .btn-delone":function(n,o,a,r){n.stopPropagation(),n.preventDefault();var s=this,l=t(s).offset().top-t(window).scrollTop(),c=t(s).offset().left-t(window).scrollLeft()-260;l+154>t(window).height()&&(l-=154),t(window).width()<480&&(l=c=e),Layer.confirm(__("Are you sure you want to delete this item?"),{icon:3,title:__("Warning"),offset:[l,c],shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(e){var n=t(s).closest("table"),o=n.bootstrapTable("getOptions");i.api.multi("del",a[o.pk],n,s),Layer.close(e)})}},image:{"click .img-center":function(e,n,i,o){var a=[];n=null===n?"":n.toString();var r,s=""!=n?n.split(","):[];t.each(s,function(t,e){r=Fast.api.cdnurl(e),a.push({src:r,thumb:r.match(/^(\/|data:image\\)/)?r:r+Config.upload.thumbstyle})}),Layer.photos({photos:{start:t(this).parent().index(),data:a},anim:5})}}},formatter:{icon:function(t,e,n){return t=null===t?"":t.toString(),t=t.indexOf(" ")>-1?t:"fa fa-"+t,'<i class="'+t+'"></i> '+t},image:function(t,e,n){return i.api.formatter.images.call(this,t,e,n)},images:function(e,n,i){e=null==e||0===e.length?"":e.toString();var o,a="undefined"!=typeof this.classname?this.classname:"img-sm img-center",r=""!==e?e.indexOf("data:image/")===-1?e.split(","):[e]:[],s=[];return t.each(r,function(t,e){e=e?e:"/assets/img/blank.gif",o=Fast.api.cdnurl(e,!0),o=!Config.upload.thumbstyle||o.match(/^(\/|data:image\/)/)||o.indexOf(Config.upload.thumbstyle.substring(0,1))>-1?o:o+Config.upload.thumbstyle,s.push('<a href="javascript:"><img class="'+a+'" src="'+o+'" /></a>')}),s.join(" ")},file:function(t,e,n){return i.api.formatter.files.call(this,t,e,n)},files:function(e,n,i){e=null==e||0===e.length?"":e.toString();var o,a,r="undefined"!=typeof this.classname?this.classname:"img-sm img-center",s=""!==e?e.indexOf("data:image/")===-1?e.split(","):[e]:[],l=[];return t.each(s,function(t,e){e=Fast.api.cdnurl(e,!0),o=/[\.]?([a-zA-Z0-9]+)$/.exec(e),o=o?o[1]:"file",a=Fast.api.fixurl("ajax/icon?suffix="+o),l.push('<a href="'+e+'" target="_blank"><img src="'+a+'" class="'+r+'"></a>')}),l.join(" ")},content:function(t,n,i){var o=this.width!=e?this.width.toString().match(/^\d+$/)?this.width+"px":this.width:"250px",a=this.hover!=e&&this.hover?"autocontent-hover":"";return"<div class='autocontent-item "+a+"' style='white-space: nowrap; text-overflow:ellipsis; overflow: hidden; max-width:"+o+";'>"+t+"</div>"},status:function(e,n,o){var a={normal:"success",hidden:"gray",deleted:"danger",locked:"info"};return"undefined"!=typeof this.custom&&(a=t.extend(a,this.custom)),this.custom=a,this.icon="fa fa-circle",i.api.formatter.normal.call(this,e,n,o)},normal:function(e,n,i){var o=["primary","success","danger","warning","info","gray","red","yellow","aqua","blue","navy","teal","olive","lime","fuchsia","purple","maroon"],a={};"undefined"!=typeof this.custom&&(a=t.extend(a,this.custom)),e=null==e||0===e.length?"":e.toString();var r="object"==typeof this.searchList?Object.keys(this.searchList):[],i=r.indexOf(e),s=e&&"undefined"!=typeof a[e]?a[e]:null,l=i>-1?this.searchList[e]:null,c="undefined"!=typeof this.icon?this.icon:null;s||(s=i>-1&&"undefined"!=typeof o[i]?o[i]:"primary"),l||(l=__(e.charAt(0).toUpperCase()+e.slice(1)));var d='<span class="text-'+s+'">'+(c?'<i class="'+c+'"></i> ':"")+l+"</span>";return 0!=this.operate&&(d='<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",l)+'" data-field="'+this.field+'" data-value="'+e+'">'+d+"</a>"),d},toggle:function(t,e,n){var i=this.table,o=i?i.bootstrapTable("getOptions"):{},a=o.pk||"id",r="undefined"!=typeof this.color?this.color:"success",s="undefined"!=typeof this.yes?this.yes:1,l="undefined"!=typeof this.no?this.no:0,c="undefined"!=typeof this.url?this.url:"",d="",u=!1;return"undefined"!=typeof this.confirm&&(d="function"==typeof this.confirm?this.confirm.call(this,t,e,n):this.confirm),"undefined"!=typeof this.disable&&(u="function"==typeof this.disable?this.disable.call(this,t,e,n):this.disable),"<a href='javascript:;' data-toggle='tooltip' title='"+__("Click to toggle")+"' class='btn-change "+(u?"btn disabled no-padding":"")+"' data-index='"+n+"' data-id='"+e[a]+"' "+(c?"data-url='"+c+"'":"")+(d?"data-confirm='"+d+"'":"")+" data-params='"+this.field+"="+(t==s?l:s)+"'><i class='fa fa-toggle-on text-success text-"+r+" "+(t==s?"":"fa-flip-horizontal text-gray")+" fa-2x'></i></a>"},url:function(t,e,n){return t=null==t||0===t.length?"":t.toString(),'<div class="input-group input-group-sm" style="width:250px;margin:0 auto;"><input type="text" class="form-control input-sm" value="'+t+'"><span class="input-group-btn input-group-sm"><a href="'+t+'" target="_blank" class="btn btn-default btn-sm"><i class="fa fa-link"></i></a></span></div>'},search:function(t,n,i){var o=this.field;if("undefined"!=typeof this.customField){var a=this.customField.split(".").reduce(function(t,n){return null===t||t===e?"":t[n]},n);t=Fast.api.escape(a),o=this.customField}return'<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",t)+'" data-field="'+o+'" data-value="'+t+'">'+t+"</a>"},addtabs:function(t,e,n){var o=i.api.replaceurl(this.url||"",e,this.table),a=this.atitle?this.atitle:__("Search %s",t);return'<a href="'+Fast.api.fixurl(o)+'" class="addtabsit" data-value="'+t+'" title="'+a+'">'+t+"</a>"},dialog:function(t,e,n){var o=i.api.replaceurl(this.url||"",e,this.table),a=this.atitle?this.atitle:__("View %s",t);return'<a href="'+Fast.api.fixurl(o)+'" class="dialogit" data-value="'+t+'" title="'+a+'">'+t+"</a>"},flag:function(n,i,o){var a=this;n=null==n||0===n.length?"":n.toString();var r={index:"success",hot:"warning",recommend:"danger",new:"info"};"undefined"!=typeof this.custom&&(r=t.extend(r,this.custom));var s=this.field;if("undefined"!=typeof this.customField){var l=this.customField.split(".").reduce(function(t,n){return null===t||t===e?"":t[n]},i);n=Fast.api.escape(l),s=this.customField}if("object"==typeof a.searchList&&"undefined"==typeof a.custom){var c=0,d=Object.values(r);t.each(a.searchList,function(t,e){"undefined"==typeof r[t]&&(r[t]=d[c],c="undefined"==typeof d[c+1]?0:c+1)})}var u,p,h,f=[],m=""!=n?n.split(","):[];return t.each(m,function(t,e){return e=null==e||0===e.length?"":e.toString(),""==e||(u=e&&"undefined"!=typeof r[e]?r[e]:"primary",p="undefined"!=typeof a.searchList&&"undefined"!=typeof a.searchList[e]?a.searchList[e]:__(e.charAt(0).toUpperCase()+e.slice(1)),h='<span class="label label-'+u+'">'+p+"</span>",void(a.operate?f.push('<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",p)+'" data-field="'+s+'" data-value="'+e+'">'+h+"</a>"):f.push(h)))}),f.join(" ")},label:function(t,e,n){return i.api.formatter.flag.call(this,t,e,n)},datetime:function(t,e,i){var o="undefined"==typeof this.datetimeFormat?"YYYY-MM-DD HH:mm:ss":this.datetimeFormat;return isNaN(t)?t?n(t).format(o):__("None"):t?n(1e3*parseInt(t)).format(o):__("None")},operate:function(e,n,o){var a=this.table,r=a?a.bootstrapTable("getOptions"):{},s=t.extend([],this.buttons||[]),l=[];return s.forEach(function(t){l.push(t.name)}),""!==r.extend.dragsort_url&&l.indexOf("dragsort")===-1&&s.push(i.button.dragsort),""!==r.extend.edit_url&&l.indexOf("edit")===-1&&(i.button.edit.url=r.extend.edit_url,s.push(i.button.edit)),""!==r.extend.del_url&&l.indexOf("del")===-1&&s.push(i.button.del),i.api.buttonlink(this,s,e,n,o,"operate")},buttons:function(e,n,o){var a=t.extend([],this.buttons||[]);return i.api.buttonlink(this,a,e,n,o,"buttons")}},buttonlink:function(e,n,o,a,r,s){var l=e.table;e.clickToSelect=!1,s="undefined"==typeof s?"buttons":s;var c,d,u,p,h,f,m,g,v,y,b,x,w,_=l?l.bootstrapTable("getOptions"):{},k=[],C=e.fieldIndex,S={};if(t.each(n,function(t,e){if("operate"===s){if("dragsort"===e.name&&"undefined"==typeof a[i.config.dragsortfield])return!0;if(["add","edit","del","multi","dragsort"].indexOf(e.name)>-1&&!_.extend[e.name+"_url"])return!0}var n=l.data(s+"-"+e.name);if("undefined"==typeof n||n){if(c="function"==typeof e.hidden?e.hidden.call(l,a,e):"undefined"!=typeof e.hidden&&e.hidden)return!0;if(d="function"==typeof e.visible?e.visible.call(l,a,e):"undefined"==typeof e.visible||e.visible,!d)return!0;x=e.dropdown?e.dropdown:"",p=e.url?e.url:"",p="function"==typeof p?p.call(l,a,e):p?Fast.api.fixurl(i.api.replaceurl(p,a,l)):"javascript:;",h=e.classname?e.classname:x?"btn-"+name+"one":"btn-primary btn-"+name+"one",f=e.icon?e.icon:"",m="function"==typeof e.text?e.text.call(l,a,e):e.text?e.text:"",g="function"==typeof e.title?e.title.call(l,a,e):e.title?e.title:m,v=e.refresh?'data-refresh="'+e.refresh+'"':"",y="function"==typeof e.confirm?e.confirm.call(l,a,e):"undefined"!=typeof e.confirm&&e.confirm,y=y?'data-confirm="'+y+'"':"",b="function"==typeof e.extend?e.extend.call(l,a,e):"undefined"!=typeof e.extend?e.extend:"",u="function"==typeof e.disable?e.disable.call(l,a,e):"undefined"!=typeof e.disable&&e.disable,u&&(h+=" disabled"),w='<a href="'+p+'" class="'+h+'" '+(y?y+" ":"")+(v?v+" ":"")+b+' title="'+g+'" data-table-id="'+(l?l.attr("id"):"")+'" data-field-index="'+C+'" data-row-index="'+r+'" data-button-index="'+t+'"><i class="'+f+'"></i>'+(m?" "+m:"")+"</a>",x?("undefined"==typeof S[x]&&(S[x]=[]),S[x].push(w)):k.push(w)}}),!t.isEmptyObject(S)){var T=[];t.each(S,function(t,e){T.push('<div class="btn-group"><button type="button" class="btn btn-primary dropdown-toggle btn-xs" data-toggle="dropdown">'+t+'</button><button type="button" class="btn btn-primary dropdown-toggle btn-xs" data-toggle="dropdown"><span class="caret"></span></button><ul class="dropdown-menu dropdown-menu-right"><li>'+e.join("</li><li>")+"</li></ul></div>")}),k.unshift(T.join(" "))}return k.join(" ")},replaceurl:function(t,n,i){var o=i?i.bootstrapTable("getOptions"):null,a=o?n[o.pk]:0;return n.ids=a?a:"undefined"!=typeof n.ids?n.ids:0,t=null==t||0===t.length?"":t.toString(),t=t.match(/(?=([?&]ids=)|(\/ids\/)|(\{ids}))/i)?t:t+(t.match(/(\?|&)+/)?"&ids=":"/ids/")+"{ids}",t=t.replace(/\{(.*?)\}/gi,function(t){t=t.substring(1,t.length-1);var i=t.split(".").reduce(function(t,n){return null===t||t===e?"":t[n]},n);return i=Fast.api.escape(i)})},selectedids:function(e,n){var i=e.bootstrapTable("getOptions");return!n&&i.maintainSelected?i.selectedIds:t.map(e.bootstrapTable("getSelections"),function(t){return t[i.pk]})},selecteddata:function(t,e){var n=t.bootstrapTable("getOptions");return!e&&n.maintainSelected?n.selectedData:t.bootstrapTable("getSelections")},toggleattr:function(e){t("input[type='checkbox']",e).trigger("click")},getrowdata:function(t,e){e=parseInt(e);var n=t.bootstrapTable("getData");return"undefined"!=typeof n[e]?n[e]:null},getrowbyindex:function(t,e){return i.api.getrowdata(t,e)},getrowbyid:function(e,n){var o={},a=e.bootstrapTable("getOptions");return t.each(i.api.selecteddata(e),function(t,e){if(e[a.pk]==n)return o=e,!1}),o}}};return i}),function(t){t.fn.dragsort=function(e){if("destroy"==e)return void t(this.selector).trigger("dragsort-uninit");var n=t.extend({},t.fn.dragsort.defaults,e),i=[],o=null,a=null;return this.each(function(e,r){t(r).is("table")&&1==t(r).children().length&&t(r).children().is("tbody")&&(r=t(r).children().get(0));var s={draggedItem:null,placeHolderItem:null,pos:null,offset:null,offsetLimit:null,scroll:null,container:r,init:function(){n.tagName=""==n.tagName?0==t(this.container).children().length?"li":t(this.container).children().get(0).tagName.toLowerCase():n.tagName,""==n.itemSelector&&(n.itemSelector=n.tagName),""==n.dragSelector&&(n.dragSelector=n.tagName),""==n.placeHolderTemplate&&(n.placeHolderTemplate="<"+n.tagName+">&nbsp;</"+n.tagName+">"),t(this.container).attr("data-listidx",e).mousedown(this.grabItem).bind("dragsort-uninit",this.uninit),this.styleDragHandlers(!0)},uninit:function(){var e=i[t(this).attr("data-listidx")];t(e.container).unbind("mousedown",e.grabItem).unbind("dragsort-uninit"),e.styleDragHandlers(!1)},getItems:function(){return t(this.container).children(n.itemSelector)},styleDragHandlers:function(e){this.getItems().map(function(){return t(this).is(n.dragSelector)?this:t(this).find(n.dragSelector).get()}).css("cursor",e?"pointer":"")},grabItem:function(e){var o=i[t(this).attr("data-listidx")],a=t(e.target).closest("[data-listidx] > "+n.tagName).get(0),r=o.getItems().filter(function(){return this==a}).length>0;if(!(1!=e.which||t(e.target).is(n.dragSelectorExclude)||t(e.target).closest(n.dragSelectorExclude).length>0)&&r&&(t(e.target).is(n.dragSelector)||t(e.target).closest(n.dragSelector).length)){e.preventDefault();for(var s=e.target;!t(s).is(n.dragSelector);){if(s==this)return;s=s.parentNode}t(s).attr("data-cursor",t(s).css("cursor")),t(s).css("cursor","move");var l=this,c=function(){o.dragStart.call(l,e),t(o.container).unbind("mousemove",c)};t(o.container).mousemove(c).mouseup(function(){t(o.container).unbind("mousemove",c),t(s).css("cursor",t(s).attr("data-cursor"))})}},dragStart:function(e){null!=o&&null!=o.draggedItem&&o.dropItem(),o=i[t(this).attr("data-listidx")],o.draggedItem=t(e.target).closest("[data-listidx] > "+n.tagName),o.draggedItem.attr("data-origpos",t(this).attr("data-listidx")+"-"+t(o.container).children().index(o.draggedItem));var a=parseInt(o.draggedItem.css("marginTop")),r=parseInt(o.draggedItem.css("marginLeft"));if(o.offset=o.draggedItem.offset(),o.offset.top=e.pageY-o.offset.top+(isNaN(a)?0:a)-1,o.offset.left=e.pageX-o.offset.left+(isNaN(r)?0:r)-1,!n.dragBetween){var s=0==t(o.container).outerHeight()?Math.max(1,Math.round(.5+o.getItems().length*o.draggedItem.outerWidth()/t(o.container).outerWidth()))*o.draggedItem.outerHeight():t(o.container).outerHeight();o.offsetLimit=t(o.container).offset(),o.offsetLimit.right=o.offsetLimit.left+t(o.container).outerWidth()-o.draggedItem.outerWidth(),o.offsetLimit.bottom=o.offsetLimit.top+s-o.draggedItem.outerHeight()}var l=o.draggedItem.height(),c=o.draggedItem.width();if("tr"==n.tagName?(o.draggedItem.children().each(function(){t(this).width(t(this).width())}),o.placeHolderItem=o.draggedItem.clone().attr("data-placeholder",!0),o.draggedItem.after(o.placeHolderItem),o.placeHolderItem.children().each(function(){t(this).html("&nbsp;")})):(o.draggedItem.after(n.placeHolderTemplate),o.placeHolderItem=o.draggedItem.next().css({height:l,width:c}).attr("data-placeholder",!0)),"td"==n.tagName){var d=o.draggedItem.closest("table").get(0);t("<table id='"+d.id+"' style='border-width: 0px;' class='dragSortItem "+d.className+"'><tr></tr></table>").appendTo("body").children().append(o.draggedItem)}var u=o.draggedItem.attr("style");o.draggedItem.attr("data-origstyle",u?u:""),o.draggedItem.css({position:"absolute",opacity:.8,"z-index":999,height:l,width:c}),o.scroll={moveX:0,moveY:0,maxX:t(document).width()-t(window).width(),maxY:t(document).height()-t(window).height()},o.scroll.scrollY=window.setInterval(function(){if(n.scrollContainer!=window)return void t(n.scrollContainer).scrollTop(t(n.scrollContainer).scrollTop()+o.scroll.moveY);var e=t(n.scrollContainer).scrollTop();(o.scroll.moveY>0&&e<o.scroll.maxY||o.scroll.moveY<0&&e>0)&&(t(n.scrollContainer).scrollTop(e+o.scroll.moveY),o.draggedItem.css("top",o.draggedItem.offset().top+o.scroll.moveY+1))},10),o.scroll.scrollX=window.setInterval(function(){if(n.scrollContainer!=window)return void t(n.scrollContainer).scrollLeft(t(n.scrollContainer).scrollLeft()+o.scroll.moveX);var e=t(n.scrollContainer).scrollLeft();(o.scroll.moveX>0&&e<o.scroll.maxX||o.scroll.moveX<0&&e>0)&&(t(n.scrollContainer).scrollLeft(e+o.scroll.moveX),o.draggedItem.css("left",o.draggedItem.offset().left+o.scroll.moveX+1))},10),t(i).each(function(t,e){e.createDropTargets(),e.buildPositionTable()}),o.setPos(e.pageX,e.pageY),t(document).bind("mousemove",o.swapItems),t(document).bind("mouseup",o.dropItem),n.scrollContainer!=window&&t(window).bind("wheel",o.wheel)},setPos:function(e,i){var a=i-this.offset.top,r=e-this.offset.left;n.dragBetween||(a=Math.min(this.offsetLimit.bottom,Math.max(a,this.offsetLimit.top)),r=Math.min(this.offsetLimit.right,Math.max(r,this.offsetLimit.left)));var s=this.draggedItem.offsetParent().not("body").offset();if(null!=s&&(a-=s.top,r-=s.left),n.scrollContainer==window)i-=t(window).scrollTop(),e-=t(window).scrollLeft(),i=Math.max(0,i-t(window).height()+5)+Math.min(0,i-5),e=Math.max(0,e-t(window).width()+5)+Math.min(0,e-5);else{var l=t(n.scrollContainer),c=l.offset();i=Math.max(0,i-l.height()-c.top)+Math.min(0,i-c.top),e=Math.max(0,e-l.width()-c.left)+Math.min(0,e-c.left)}o.scroll.moveX=0==e?0:e*n.scrollSpeed/Math.abs(e),o.scroll.moveY=0==i?0:i*n.scrollSpeed/Math.abs(i),this.draggedItem.css({top:a,left:r})},wheel:function(e){if(o&&n.scrollContainer!=window){var i=t(n.scrollContainer),a=i.offset();if(e=e.originalEvent,e.clientX>a.left&&e.clientX<a.left+i.width()&&e.clientY>a.top&&e.clientY<a.top+i.height()){var r=(0==e.deltaMode?1:10)*e.deltaY;i.scrollTop(i.scrollTop()+r),e.preventDefault()}}},buildPositionTable:function(){var e=[];this.getItems().not([o.draggedItem[0],o.placeHolderItem[0]]).each(function(n){var i=t(this).offset();i.right=i.left+t(this).outerWidth(),i.bottom=i.top+t(this).outerHeight(),i.elm=this,e[n]=i}),this.pos=e},dropItem:function(){if(null!=o.draggedItem){var e=o.draggedItem.attr("data-origstyle");if(o.draggedItem.attr("style",e),""==e&&o.draggedItem.removeAttr("style"),o.draggedItem.removeAttr("data-origstyle"),o.styleDragHandlers(!0),o.placeHolderItem.before(o.draggedItem),o.placeHolderItem.remove(),t("[data-droptarget], .dragSortItem").remove(),window.clearInterval(o.scroll.scrollY),window.clearInterval(o.scroll.scrollX),o.draggedItem.attr("data-origpos")!=t(i).index(o)+"-"+t(o.container).children().index(o.draggedItem)&&0==n.dragEnd.apply(o.draggedItem)){var a=o.draggedItem.attr("data-origpos").split("-"),r=t(i[a[0]].container).children().not(o.draggedItem).eq(a[1]);r.length>0?r.before(o.draggedItem):0==a[1]?t(i[a[0]].container).prepend(o.draggedItem):t(i[a[0]].container).append(o.draggedItem)}return o.draggedItem.removeAttr("data-origpos"),o.draggedItem=null,t(document).unbind("mousemove",o.swapItems),t(document).unbind("mouseup",o.dropItem),n.scrollContainer!=window&&t(window).unbind("wheel",o.wheel),!1}},swapItems:function(e){if(null==o.draggedItem)return!1;o.setPos(e.pageX,e.pageY);for(var r=o.findPos(e.pageX,e.pageY),s=o,l=0;r==-1&&n.dragBetween&&l<i.length;l++)r=i[l].findPos(e.pageX,e.pageY),s=i[l];if(r==-1)return!1;var c=function(){return t(s.container).children().not(s.draggedItem)},d=c().not(n.itemSelector).each(function(t){this.idx=c().index(this)});return null==a||a.top>o.draggedItem.offset().top||a.left>o.draggedItem.offset().left?t(s.pos[r].elm).before(o.placeHolderItem):t(s.pos[r].elm).after(o.placeHolderItem),d.each(function(){var e=c().eq(this.idx).get(0);this!=e&&c().index(this)<this.idx?t(this).insertAfter(e):this!=e&&t(this).insertBefore(e)}),t(i).each(function(t,e){e.createDropTargets(),e.buildPositionTable()}),a=o.draggedItem.offset(),!1},findPos:function(t,e){for(var n=0;n<this.pos.length;n++)if(this.pos[n].left<t&&this.pos[n].right>t&&this.pos[n].top<e&&this.pos[n].bottom>e)return n;return-1},createDropTargets:function(){n.dragBetween&&t(i).each(function(){var e=t(this.container).find("[data-placeholder]"),i=t(this.container).find("[data-droptarget]");e.length>0&&i.length>0?i.remove():0==e.length&&0==i.length&&("td"==n.tagName?t(n.placeHolderTemplate).attr("data-droptarget",!0).appendTo(this.container):t(this.container).append(o.placeHolderItem.removeAttr("data-placeholder").clone().attr("data-droptarget",!0)),o.placeHolderItem.attr("data-placeholder",!0))})}};s.init(),i.push(s)}),this},t.fn.dragsort.defaults={tagName:"",itemSelector:"",dragSelector:"",dragSelectorExclude:"input, textarea",dragEnd:function(){},dragBetween:!1,placeHolderTemplate:"",scrollContainer:window,scrollSpeed:5}}(jQuery),define("dragsort",function(){}),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),i=n.data(d.dataKey),o=t.extend({},c,n.data(),i&&i.option,"object"==typeof e&&e);i||n.data(d.dataKey,i=new d(this,o))})}function n(e){return t(e).closest("div.sp_container").find("input.sp_input")}function i(){return this.each(function(){var t=n(this),e=t.data(d.dataKey);e&&(e.prop.init_set=!0,e.clearAll(e),e.prop.init_set=!1)})}function o(){return this.each(function(){var t=n(this),e=t.data(d.dataKey);e&&e.elem.hidden.val()&&e.setInitRecord(!0)})}function a(e){return this.each(function(){if(e&&t.isArray(e)){var i=n(this),o=i.data(d.dataKey);o&&(o.clearAll(o),o.option.data=e)}})}function r(e){var i=!1;return this.each(function(){var o=n(this),a=o.data(d.dataKey);a&&("undefined"!==t.type(e)?a.disabled(a,e):i=a.disabled(a));
}),i}function s(){var e="";return this.each(function(){var i=n(this),o=i.data(d.dataKey);if(o)if(o.option.multiple){var a=[];o.elem.element_box.find("li.selected_tag").each(function(e,n){a.push(t(n).text())}),e+=a.toString()}else e+=o.elem.combo_input.val()}),e}function l(){var e=[];return this.each(function(){var i=n(this),o=i.data(d.dataKey);if(o)if(o.option.multiple)o.elem.element_box.find("li.selected_tag").each(function(n,i){e.push(t(i).data("dataObj"))});else{var a=o.elem.combo_input.data("dataObj");a&&e.push(a)}}),e}var c={data:void 0,lang:"cn",multiple:!1,pagination:!0,dropButton:!0,listSize:10,multipleControlbar:!0,maxSelectLimit:0,selectToCloseList:!1,initRecord:void 0,dbTable:"tbl",keyField:"id",showField:"name",searchField:void 0,andOr:"OR",separator:",",orderBy:void 0,pageSize:10,params:void 0,formatItem:void 0,autoFillResult:!1,autoSelectFirst:!1,noResultClean:!0,selectOnly:!1,inputDelay:.5,eSelect:void 0,eOpen:void 0,eAjaxSuccess:void 0,eTagRemove:void 0,eClear:void 0},d=function(e,n){t.each({data:"source",keyField:"primaryKey",showField:"field",pageSize:"perPage"},function(t,e){"undefined"!=typeof n[e]&&(n[t]=n[e],delete n[e])}),this.setOption(n),this.setLanguage(),this.setCssClass(),this.setProp(),this.setElem(e),this.setButtonAttrDefault(),this.setInitRecord(),this.eDropdownButton(),this.eInput(),this.eWhole()};d.version="2.19",d.dataKey="selectPageObject",d.prototype.setOption=function(e){e.searchField=e.searchField||e.showField,e.andOr=e.andOr.toUpperCase(),"AND"!==e.andOr&&"OR"!==e.andOr&&(e.andOr="AND");for(var n=["searchField"],i=0;i<n.length;i++)e[n[i]]=this.strToArray(e[n[i]]);if(e.orderBy=e.orderBy||e.showField,e.orderBy!==!1&&(e.orderBy=this.setOrderbyOption(e.orderBy,e.showField)),e.multiple&&!e.selectToCloseList&&(e.autoFillResult=!1,e.autoSelectFirst=!1),e.pagination||(e.pageSize=200),("number"!==t.type(e.listSize)||e.listSize<0)&&(e.listSize=10),"string"==typeof e.formatItem){var o=e.formatItem;e.formatItem=function(t){return"function"==typeof Template&&o.match(/\#([a-zA-Z0-9_\-]+)$/)?Template(o.substring(1),t):o.replace(/\{(.*?)\}/gi,function(e){return e=e.substring(1,e.length-1),"undefined"!=typeof t[e]?t[e]:""})}}this.option=e},d.prototype.strToArray=function(t){return t?t.replace(/[\s　]+/g,"").split(","):""},d.prototype.setOrderbyOption=function(e,n){var i=[],o=[];if("object"==typeof e)for(var a=0;a<e.length;a++)o=t.trim(e[a]).split(" "),o.length&&i.push(2===o.length?o.concat():[o[0],"ASC"]);else o=t.trim(e).split(" "),i[0]=2===o.length?o.concat():o[0].toUpperCase().match(/^(ASC|DESC)$/i)?[n,o[0].toUpperCase()]:[o[0],"ASC"];return i},d.prototype.setLanguage=function(){var t,e=this.option;switch(e.lang){case"en":t={add_btn:"Add button",add_title:"add a box",del_btn:"Del button",del_title:"delete a box",next:"Next",next_title:"Next"+e.pageSize+" (Right key)",prev:"Prev",prev_title:"Prev"+e.pageSize+" (Left key)",first_title:"First (Shift + Left key)",last_title:"Last (Shift + Right key)",get_all_btn:"Get All (Down key)",get_all_alt:"(button)",close_btn:"Close (Tab key)",close_alt:"(button)",loading:"loading...",loading_alt:"(loading)",page_info:"page_num of page_count",select_ng:"Attention : Please choose from among the list.",select_ok:"OK : Correctly selected.",not_found:"not found",ajax_error:"An error occurred while loading data.",clear:"Clear content",select_all:"Select current page",unselect_all:"Clear current page",clear_all:"Clear all selected",max_selected:"You can only select up to max_selected_limit items"};break;case"cn":default:t={add_btn:"添加按钮",add_title:"添加区域",del_btn:"删除按钮",del_title:"删除区域",next:"下一页",next_title:"下"+e.pageSize+" (→)",prev:"上一页",prev_title:"上"+e.pageSize+" (←)",first_title:"首页 (Shift + ←)",last_title:"尾页 (Shift + →)",get_all_btn:"获得全部 (↓)",get_all_alt:"(按钮)",close_btn:"关闭 (Tab键)",close_alt:"(按钮)",loading:"读取中...",loading_alt:"(读取中)",page_info:"第 page_num 页(共page_count页)",select_ng:"请注意：请从列表中选择.",select_ok:"OK : 已经选择.",not_found:"无查询结果",ajax_error:"加载数据时发生了错误！",clear:"清除内容",select_all:"选择当前页项目",unselect_all:"取消选择当前页项目",clear_all:"清除全部已选择项目",max_selected:"最多只能选择 max_selected_limit 个项目"}}this.message=t},d.prototype.setCssClass=function(){var t={container:"sp_container",container_open:"sp_container_open",re_area:"sp_result_area",result_open:"sp_result_area_open",control_box:"sp_control_box",element_box:"sp_element_box",navi:"sp_navi",results:"sp_results",re_off:"sp_results_off",select:"sp_over",select_ok:"sp_select_ok",select_ng:"sp_select_ng",selected:"sp_selected",input_off:"sp_input_off",message_box:"sp_message_box",disabled:"sp_disabled",button:"sp_button",caret_open:"sp_caret_open",btn_on:"sp_btn_on",btn_out:"sp_btn_out",input:"sp_input",clear_btn:"sp_clear_btn",align_right:"sp_align_right"};this.css_class=t},d.prototype.setProp=function(){this.prop={disabled:!1,current_page:1,max_page:1,is_loading:!1,xhr:!1,key_paging:!1,key_select:!1,prev_value:"",selected_text:"",last_input_time:void 0,init_set:!1},this.template={tag:{content:'<li class="selected_tag" itemvalue="#item_value#">#item_text#<span class="tag_close"><i class="spfont sp-close"></i></span></li>',textKey:"#item_text#",valueKey:"#item_value#"},page:{current:"page_num",total:"page_count"},msg:{maxSelectLimit:"max_selected_limit"}}},d.prototype.elementRealSize=function(e,n){var i,o,a,r={absolute:!1,clone:!1,includeMargin:!1,display:"block"},s=r,l=e.eq(0),c=[],d="";i=function(){a=l.parents().addBack().filter(":hidden"),d+="visibility: hidden !important; display: "+s.display+" !important; ",s.absolute===!0&&(d+="position: absolute !important;"),a.each(function(){var e=t(this),n=e.attr("style");c.push(n),e.attr("style",n?n+";"+d:d)})},o=function(){a.each(function(e){var n=t(this),i=c[e];void 0===i?n.removeAttr("style"):n.attr("style",i)})},i();var u=/(outer)/.test(n)?l[n](s.includeMargin):l[n]();return o(),u},d.prototype.setElem=function(e){var n={},i=this.option,o=this.css_class,a=this.message,r=t(e),s=r.css("width"),l=r.outerWidth();s.indexOf("%")>-1||r.parent().length>0&&r.parent().width()==l?l="100%":(l<=0&&(l=this.elementRealSize(r,"outerWidth")),l<150&&(l=150)),n.combo_input=r.attr({autocomplete:"off"}).addClass(o.input).wrap("<div>"),i.selectOnly&&n.combo_input.prop("readonly",!0),n.container=n.combo_input.parent().addClass(o.container),n.combo_input.prop("disabled")&&(i.multiple?n.container.addClass(o.disabled):n.combo_input.addClass(o.input_off)),n.container.width(l),n.button=t("<div>").addClass(o.button),n.dropdown=t('<span class="sp_caret"></span>'),n.clear_btn=t("<div>").html(t("<i>").addClass("spfont sp-close")).addClass(o.clear_btn).attr("title",a.clear),i.dropButton||n.clear_btn.addClass(o.align_right),n.element_box=t("<ul>").addClass(o.element_box),i.multiple&&i.multipleControlbar&&(n.control=t("<div>").addClass(o.control_box)),n.result_area=t("<div>").addClass(o.re_area),i.pagination&&(n.navi=t("<div>").addClass("sp_pagination").append("<ul>")),n.results=t("<ul>").addClass(o.results);var c="_text",d=n.combo_input.attr("id")||n.combo_input.attr("name"),u=n.combo_input.attr("name")||"selectPage",p=u,h=d;if(n.hidden=t('<input type="hidden" class="sp_hidden" />').attr({name:p,id:h}).val(""),n.combo_input.attr({name:"undefined"!=typeof r.data("name")?r.data("name"):u+c,id:d+c}),n.hidden.attr("data-rule",n.combo_input.data("rule")||""),n.combo_input.attr("novalidate","novalidate"),n.container.append(n.hidden),i.dropButton&&(n.container.append(n.button),n.button.append(n.dropdown)),t(document.body).append(n.result_area),n.result_area.append(n.results),i.pagination&&n.result_area.append(n.navi),i.multiple){i.multipleControlbar&&(n.control.append('<button type="button" class="btn btn-default sp_clear_all" ><i class="spfont sp-clear"></i></button>'),n.control.append('<button type="button" class="btn btn-default sp_unselect_all" ><i class="spfont sp-unselect-all"></i></button>'),n.control.append('<button type="button" class="btn btn-default sp_select_all" ><i class="spfont sp-select-all"></i></button>'),n.control_text=t("<p>"),n.control.append(n.control_text),n.result_area.prepend(n.control)),n.container.addClass("sp_container_combo"),n.combo_input.addClass("sp_combo_input").before(n.element_box);var f=t("<li>").addClass("input_box");f.append(n.combo_input),n.element_box.append(f),n.combo_input.attr("placeholder")&&n.combo_input.attr("placeholder_bak",n.combo_input.attr("placeholder"))}this.elem=n},d.prototype.setButtonAttrDefault=function(){this.option.dropButton&&this.elem.button.attr("title",this.message.close_btn)},d.prototype.setInitRecord=function(e){var n=this,i=n.option,o=n.elem,a="";if("undefined"!=t.type(o.combo_input.data("init"))&&(i.initRecord=String(o.combo_input.data("init"))),e||i.initRecord||!o.combo_input.val()||(i.initRecord=o.combo_input.val()),o.combo_input.val(""),e||o.hidden.val(i.initRecord),a=e&&o.hidden.val()?o.hidden.val():i.initRecord)if("object"==typeof i.data){var r=new Array,s=a.split(",");t.each(s,function(t,e){for(var n=0;n<i.data.length;n++)if(i.data[n][i.keyField]==e){r.push(i.data[n]);break}}),!i.multiple&&r.length>1&&(r=[r[0]]),n.afterInit(n,r)}else{var l=i.params,c={},d=(i.searchField,{searchTable:i.dbTable,searchKey:i.keyField,searchValue:a,orderBy:i.orderBy,showField:i.showField,keyField:i.keyField,keyValue:a,searchField:i.searchField});if(l){var u=t.isFunction(l)?l(n):l;c=u&&t.isPlainObject(u)?t.extend({},d,u):d}else c=d;t.ajax({dataType:"json",type:"POST",url:i.data,data:c,success:function(e){var o=null;i.eAjaxSuccess&&t.isFunction(i.eAjaxSuccess)&&(o=i.eAjaxSuccess(e)),n.afterInit(n,o.list)},error:function(t,e,i){n.ajaxErrorNotify(n,i)}})}},d.prototype.afterInit=function(e,n){if(n&&(!t.isArray(n)||0!==n.length)){t.isArray(n)||(n=[n]);var i=e.option,o=e.css_class;if(e.data=n,i.multiple)e.prop.init_set=!0,e.clearAll(e),t.each(n,function(t,n){var o=n[i.keyField],a=n[i.showField],r={text:a,value:o};e.isAlreadySelected(e,r)||e.addNewTag(e,n,r)}),e.tagValuesSet(e),e.inputResize(e),e.elem.hidden.blur(),e.prop.init_set=!1;else{var a=n[0],r=a[i.keyField],s=a[i.showField];e.elem.combo_input.val(s),e.elem.hidden.val(r),e.prop.prev_value=s,e.prop.selected_text=s,i.selectOnly&&e.elem.combo_input.attr("title",e.message.select_ok).removeClass(o.select_ng).addClass(o.select_ok),e.putClearButton()}}},d.prototype.eDropdownButton=function(){var t=this;t.option.dropButton&&t.elem.button.mouseup(function(e){e.stopPropagation(),t.elem.result_area.is(":hidden")&&!t.elem.combo_input.prop("disabled")?t.elem.combo_input.focus():t.hideResults(t)})},d.prototype.eInput=function(){var e=this,n=e.option,i=e.elem,o=e.message,a=function(){e.prop.page_move=!1,e.suggest(e),e.setCssFocusedInput(e)};i.combo_input.keyup(function(t){e.processKey(e,t)}).keydown(function(t){e.processControl(e,t)}).focus(function(t){i.result_area.is(":hidden")&&(t.stopPropagation(),e.prop.first_show=!0,a())}),i.container.on("click.SelectPage","div."+e.css_class.clear_btn,function(i){i.stopPropagation(),e.disabled(e)||(e.clearAll(e,!0),e.elem.hidden.change(),n.eClear&&t.isFunction(n.eClear)&&n.eClear(e))}),i.result_area.on("mousedown.SelectPage",function(t){t.stopPropagation()}),n.multiple&&(n.multipleControlbar&&(i.control.find(".sp_select_all").on("click.SelectPage",function(t){e.selectAllLine(e)}).hover(function(){i.control_text.html(o.select_all)},function(){i.control_text.html("")}),i.control.find(".sp_unselect_all").on("click.SelectPage",function(t){e.unSelectAllLine(e)}).hover(function(){i.control_text.html(o.unselect_all)},function(){i.control_text.html("")}),i.control.find(".sp_clear_all").on("click.SelectPage",function(t){e.clearAll(e,!0)}).hover(function(){i.control_text.html(o.clear_all)},function(){i.control_text.html("")})),i.element_box.on("click.SelectPage",function(e){var n=e.target||e.srcElement;t(n).is("ul")&&i.combo_input.focus()}),i.element_box.on("click.SelectPage","span.tag_close",function(){var i=t(this).closest("li"),o=i.data("dataObj");e.removeTag(e,i),a(),n.eTagRemove&&t.isFunction(n.eTagRemove)&&n.eTagRemove([o])}),e.inputResize(e))},d.prototype.eWhole=function(){var e=this,n=e.css_class,i=function(t){t.elem.combo_input.val(""),t.option.multiple||t.elem.hidden.val(""),t.prop.selected_text=""};t(document.body).off("mousedown.selectPage").on("mousedown.selectPage",function(e){var o=e.target||e.srcElement,a=t(o).closest("div."+n.container);t("div."+n.container+"."+n.container_open).each(function(){if(this!=a[0]){var e=t(this),o=e.find("input."+n.input).data(d.dataKey);return o.elem.combo_input.val()||!o.elem.hidden.val()||o.option.multiple?void(o.elem.results.find("li").not("."+n.message_box).length?o.option.autoFillResult?o.elem.hidden.val()?o.hideResults(o):o.elem.results.find("li.sp_over").length?o.selectCurrentLine(o,!0):o.option.autoSelectFirst?(o.nextLine(o),o.selectCurrentLine(o,!0)):o.hideResults(o):o.hideResults(o):(o.option.noResultClean?i(o):o.option.multiple||o.elem.hidden.val(""),o.hideResults(o))):(o.prop.current_page=1,i(o),o.hideResults(o),!0)}})})},d.prototype.eResultList=function(){var e=this,n=this.css_class;e.elem.results.children("li").hover(function(){return e.prop.key_select?void(e.prop.key_select=!1):void(t(this).hasClass(n.selected)||t(this).hasClass(n.message_box)||(t(this).addClass(n.select),e.setCssFocusedResults(e)))},function(){t(this).removeClass(n.select)}).click(function(i){return e.prop.key_select?void(e.prop.key_select=!1):(i.preventDefault(),i.stopPropagation(),void(t(this).hasClass(n.selected)||e.selectCurrentLine(e,!1)))})},d.prototype.eScroll=function(){var e=this.css_class;t(window).on("scroll.SelectPage",function(n){t("div."+e.container+"."+e.container_open).each(function(){var n=t(this),i=n.find("input."+e.input).data(d.dataKey),o=i.elem.result_area.offset(),a=t(window).scrollTop(),r=t(document).height(),s=t(window).height(),l=i.elem.result_area.outerHeight(),c=o.top+l,u=r>s,p=i.elem.result_area.hasClass("shadowDown");u&&(p?c>s+a&&i.calcResultsSize(i):o.top<a&&i.calcResultsSize(i))})})},d.prototype.ePaging=function(){var t=this;t.option.pagination&&(t.elem.navi.find("li.csFirstPage").off("click").on("click",function(e){e.preventDefault(),t.firstPage(t)}),t.elem.navi.find("li.csPreviousPage").off("click").on("click",function(e){e.preventDefault(),t.prevPage(t)}),t.elem.navi.find("li.csNextPage").off("click").on("click",function(e){e.preventDefault(),t.nextPage(t)}),t.elem.navi.find("li.csLastPage").off("click").on("click",function(e){e.preventDefault(),t.lastPage(t)}))},d.prototype.ajaxErrorNotify=function(t,e){t.showMessage(t,t.message.ajax_error)},d.prototype.showMessage=function(t,e){if(e){var n='<li class="'+t.css_class.message_box+'"><i class="spfont sp-warning"></i> '+e+"</li>";t.elem.results.empty().append(n).show(),t.calcResultsSize(t),t.setOpenStatus(t,!0),t.elem.control&&t.elem.control.hide(),t.option.pagination&&t.elem.navi.hide()}},d.prototype.scrollWindow=function(e,n){var i,o=e.getCurrentLine(e),a=o&&!n?o.offset().top:e.elem.container.offset().top;e.prop.size_li=e.elem.results.children("li:first").outerHeight(),i=e.prop.size_li;var r,s=t(window).height(),l=t(window).scrollTop(),c=l+s-i;if(o.length)if(a<l||i>s)r=a-l;else{if(!(a>c))return;r=a-c}else a<l&&(r=a-l);window.scrollBy(0,r)},d.prototype.setOpenStatus=function(t,e){var n=t.elem,i=t.css_class;e?(n.container.addClass(i.container_open),n.result_area.addClass(i.result_open)):(n.container.removeClass(i.container_open),n.result_area.removeClass(i.result_open))},d.prototype.setCssFocusedInput=function(t){},d.prototype.setCssFocusedResults=function(t){},d.prototype.checkValue=function(t){var e=t.elem.combo_input.val();e!=t.prop.prev_value&&(t.prop.prev_value=e,t.prop.first_show=!1,t.option.selectOnly&&t.setButtonAttrDefault(),t.option.multiple||e||(t.elem.combo_input.val(""),t.elem.hidden.val(""),t.elem.clear_btn.remove()),t.suggest(t))},d.prototype.processKey=function(e,n){t.inArray(n.keyCode,[37,38,39,40,27,9,13])===-1&&(16!=n.keyCode&&e.setCssFocusedInput(e),e.inputResize(e),"string"===t.type(e.option.data)?(e.prop.last_input_time=n.timeStamp,setTimeout(function(){n.timeStamp-e.prop.last_input_time===0&&e.checkValue(e)},1e3*e.option.inputDelay)):e.checkValue(e))},d.prototype.processControl=function(e,n){if(t.inArray(n.keyCode,[37,38,39,40,27,9])>-1&&e.elem.result_area.is(":visible")||t.inArray(n.keyCode,[13,9])>-1&&e.getCurrentLine(e))switch(n.preventDefault(),n.stopPropagation(),n.cancelBubble=!0,n.returnValue=!1,n.keyCode){case 37:n.shiftKey?e.firstPage(e):e.prevPage(e);break;case 38:e.prop.key_select=!0,e.prevLine(e);break;case 39:n.shiftKey?e.lastPage(e):e.nextPage(e);break;case 40:e.elem.results.children("li").length?(e.prop.key_select=!0,e.nextLine(e)):e.suggest(e);break;case 9:e.prop.key_paging=!0,e.selectCurrentLine(e,!0);break;case 13:e.selectCurrentLine(e,!0);break;case 27:e.prop.key_paging=!0,e.hideResults(e)}},d.prototype.abortAjax=function(t){t.prop.xhr&&(t.prop.xhr.abort(),t.prop.xhr=!1)},d.prototype.suggest=function(e){var n,i=t.trim(e.elem.combo_input.val());n=e.option.multiple?i:i&&i==e.prop.selected_text?"":i,n=n.split(e.option.separator),e.option.eOpen&&t.isFunction(e.option.eOpen)&&e.option.eOpen.call(e),e.abortAjax(e);var o=e.prop.current_page||1;"object"==typeof e.option.data?e.searchForJson(e,n,o):e.searchForDb(e,n,o)},d.prototype.setLoading=function(t){""===t.elem.results.html()&&t.setOpenStatus(t,!0)},d.prototype.searchForDb=function(e,n,i){var o=e.option;o.eAjaxSuccess&&t.isFunction(o.eAjaxSuccess)||e.hideResults(e);var a=o.params,r={},s=o.searchField;n.length&&n[0]&&n.join(e.option.separator)!==e.prop.prev_value&&(i=1);var l={q_word:n,pageNumber:i,pageSize:o.pageSize,andOr:o.andOr,orderBy:o.orderBy,searchTable:o.dbTable,showField:e.option.showField,keyField:e.option.keyField,searchField:e.option.searchField};if(o.orderBy!==!1&&(l.orderBy=o.orderBy),l[s]=n[0],a){var c=t.isFunction(a)?a(e):a;r=c&&t.isPlainObject(c)?t.extend({},l,c):l}else r=l;e.prop.xhr=t.ajax({dataType:"json",url:o.data,type:"POST",data:r,success:function(a){if(!a||!t.isPlainObject(a))return e.hideResults(e),void e.ajaxErrorNotify(e,errorThrown);var r={},s={};try{r=o.eAjaxSuccess(a),s.originalResult=r.list,s.cnt_whole=r.totalRow}catch(t){return void e.showMessage(e,e.message.ajax_error)}if(e.elem.navi&&t(e.elem.navi).toggleClass("hide",s.cnt_whole<=s.originalResult.length),s.candidate=[],s.keyField=[],"object"!=typeof s.originalResult)return e.prop.xhr=null,void e.notFoundSearch(e);s.cnt_page=s.originalResult.length;for(var l=0;l<s.cnt_page;l++)for(var c in s.originalResult[l])c==o.keyField&&s.keyField.push(s.originalResult[l][c]),c==o.showField&&s.candidate.push(s.originalResult[l][c]);e.prepareResults(e,s,n,i)},error:function(t,n,i){"abort"!=n&&(e.hideResults(e),e.ajaxErrorNotify(e,i))},complete:function(){e.prop.xhr=null}})},d.prototype.searchForJson=function(e,n,i){var o=e.option,a=[],r=[],s=[],l={},c=0,d=[];do r[c]=n[c].replace(/\W/g,"\\$&").toString(),d[c]=new RegExp(r[c],"gi"),c++;while(c<n.length);for(var c=0;c<o.data.length;c++){for(var u,p=!1,h=o.data[c],f=0;f<d.length;f++)if(u=h[o.searchField],o.formatItem&&t.isFunction(o.formatItem)&&(u=o.formatItem(h)),u.match(d[f])){if(p=!0,"OR"==o.andOr)break}else if(p=!1,"AND"==o.andOr)break;p&&a.push(h)}if(o.orderBy===!1)s=a.concat();else{for(var m=new RegExp("^"+r[0]+"$","gi"),g=new RegExp("^"+r[0],"gi"),v=[],y=[],b=[],c=0;c<a.length;c++){var x=o.orderBy[0][0],w=String(a[c][x]);w.match(m)?v.push(a[c]):w.match(g)?y.push(a[c]):b.push(a[c])}o.orderBy[0][1].match(/^asc$/i)?(v=e.sortAsc(e,v),y=e.sortAsc(e,y),b=e.sortAsc(e,b)):(v=e.sortDesc(e,v),y=e.sortDesc(e,y),b=e.sortDesc(e,b)),s=s.concat(v).concat(y).concat(b)}if(l.cnt_whole=s.length,e.prop.page_move)s.length<=(i-1)*o.pageSize&&(i=1,e.prop.current_page=1);else if(!o.multiple){var _=e.elem.hidden.val();if("undefined"!==t.type(_)&&""!==t.trim(_)){var k=0;t.each(s,function(t,e){if(e[o.keyField]==_)return k=t+1,!1}),i=Math.ceil(k/o.pageSize),i<1&&(i=1),e.prop.current_page=i}}var C=(i-1)*o.pageSize,S=C+o.pageSize;l.originalResult=[];for(var c=C;c<S&&void 0!==s[c];c++){l.originalResult.push(s[c]);for(var T in s[c])T==o.keyField&&(void 0===l.keyField&&(l.keyField=[]),l.keyField.push(s[c][T])),T==o.showField&&(void 0===l.candidate&&(l.candidate=[]),l.candidate.push(s[c][T]))}void 0===l.candidate&&(l.candidate=[]),l.cnt_page=l.candidate.length,e.prepareResults(e,l,n,i)},d.prototype.sortAsc=function(e,n){return n.sort(function(n,i){var o=n[e.option.orderBy[0][0]],a=i[e.option.orderBy[0][0]];return"number"===t.type(o)?o-a:String(o).localeCompare(String(a))}),n},d.prototype.sortDesc=function(e,n){return n.sort(function(n,i){var o=n[e.option.orderBy[0][0]],a=i[e.option.orderBy[0][0]];return"number"===t.type(o)?a-o:String(a).localeCompare(String(o))}),n},d.prototype.notFoundSearch=function(t){t.elem.results.empty(),t.calcResultsSize(t),t.setOpenStatus(t,!0),t.setCssFocusedInput(t)},d.prototype.prepareResults=function(t,e,n,i){t.data=e.originalResult,t.option.pagination&&t.setNavi(t,e.cnt_whole,e.cnt_page,i),e.keyField||(e.keyField=!1),t.option.selectOnly&&1===e.candidate.length&&e.candidate[0]==n[0]&&(t.elem.hidden.val(e.keyField[0]),this.setButtonAttrDefault());var o=!1;n&&n.length&&n[0]&&(o=!0),t.displayResults(t,e,o)},d.prototype.setNavi=function(t,e,n,i){var o=t.message,a=function(t,e,n,i){var a=function(){var e=o.page_info;return e.replace(t.template.page.current,n).replace(t.template.page.total,i)};if(0===e.find("li").length){e.hide().empty();var r="spfont sp-first",s="spfont sp-previous",l="spfont sp-next",c="spfont sp-last";e.append('<li class="csFirstPage" title="'+o.first_title+'" ><a href="javascript:void(0);"> <i class="'+r+'"></i> </a></li>'),e.append('<li class="csPreviousPage" title="'+o.prev_title+'" ><a href="javascript:void(0);"><i class="'+s+'"></i></a></li>'),e.append('<li class="pageInfoBox"><a href="javascript:void(0);"> '+a()+" </a></li>"),e.append('<li class="csNextPage" title="'+o.next_title+'" ><a href="javascript:void(0);"><i class="'+l+'"></i></a></li>'),e.append('<li class="csLastPage" title="'+o.last_title+'" ><a href="javascript:void(0);"> <i class="'+c+'"></i> </a></li>'),e.show()}else e.find("li.pageInfoBox a").html(a())},r=t.elem.navi.find("ul"),s=Math.ceil(e/t.option.pageSize);0===s?i=0:s<i?i=s:0===i&&(i=1),t.prop.current_page=i,t.prop.max_page=s,a(t,r,i,s);var l="disabled",c=r.find("li.csFirstPage"),d=r.find("li.csPreviousPage"),u=r.find("li.csNextPage"),p=r.find("li.csLastPage");1===i||0===i?(c.hasClass(l)||c.addClass(l),d.hasClass(l)||d.addClass(l)):(c.hasClass(l)&&c.removeClass(l),d.hasClass(l)&&d.removeClass(l)),i===s||0===s?(u.hasClass(l)||u.addClass(l),p.hasClass(l)||p.addClass(l)):(u.hasClass(l)&&u.removeClass(l),p.hasClass(l)&&p.removeClass(l)),s>1&&t.ePaging()},d.prototype.displayResults=function(e,n,i){var o=e.option,a=e.elem;if(a.results.hide().empty(),o.multiple&&"number"===t.type(o.maxSelectLimit)&&o.maxSelectLimit>0){var r=a.element_box.find("li.selected_tag").length;if(r>0&&r>=o.maxSelectLimit){var s=e.message.max_selected;return void e.showMessage(e,s.replace(e.template.msg.maxSelectLimit,o.maxSelectLimit))}}if(n.candidate.length)for(var l=n.candidate,c=n.keyField,d=a.hidden.val(),u=d?d.split(","):new Array,p="",h=0;h<l.length;h++){if(o.formatItem&&t.isFunction(o.formatItem))try{p=o.formatItem(n.originalResult[h])}catch(t){console.error("formatItem内容格式化函数内容设置不正确！"),p=l[h]}else p=l[h];var f=t("<li>").html(p).attr({pkey:c[h],index:h});o.formatItem||f.attr("title",p),t.inArray(c[h].toString(),u)!==-1&&f.addClass(e.css_class.selected),f.data("dataObj",n.originalResult[h]),a.results.append(f)}else{var m='<li class="'+e.css_class.message_box+'"><i class="spfont sp-warning"></i> '+e.message.not_found+"</li>";a.results.append(m)}a.results.show(),o.multiple&&o.multipleControlbar&&a.control.show(),o.pagination&&a.navi.show(),e.calcResultsSize(e),e.setOpenStatus(e,!0),e.eResultList(),e.eScroll(),i&&n.candidate.length&&o.autoSelectFirst&&e.nextLine(e)},d.prototype.calcResultsSize=function(e){var n=e.option,i=e.elem,o=function(){if("static"!==i.container.css("position")){if(!n.pagination){var e=i.results.find("li:first").outerHeight(!0),o=e*n.listSize;i.results.css({"max-height":o,"overflow-y":"auto"})}var a=t(document).width(),r=t(document).height(),s=t(window).height(),l=i.container.offset(),c=t(window).scrollTop(),d=i.result_area.outerWidth(),o=i.result_area.outerHeight(),u=l.left,p=i.container.outerHeight(),h=l.left+d>a?u-(d-i.container.outerWidth()):u,f=l.top,m=0,g=5,v=f+p+o+g,y=f+o+g,b=r>s;return f-c-g>o&&b&&v>s+c||!b&&v>s&&f>=y?(m=l.top-o-g,i.result_area.removeClass("shadowUp shadowDown").addClass("shadowUp")):(m=l.top+(n.multiple?i.container.outerHeight():p),i.result_area.removeClass("shadowUp shadowDown").addClass("shadowDown"),m+=g),{top:m+"px",left:h+"px"}}var l=i.combo_input.offset();i.result_area.css({top:l.top+i.combo_input.outerHeight()+"px",left:l.left+"px"})};if(i.result_area.is(":visible"))i.result_area.css(o());else{var a=o();i.result_area.css(a).show(1,function(){var t=o();a.top===t.top&&a.left===t.left||i.result_area.css(t)})}},d.prototype.hideResults=function(e){e.prop.key_paging&&(e.scrollWindow(e,!0),e.prop.key_paging=!1),e.setCssFocusedInput(e),e.option.autoFillResult,e.elem.results.empty(),e.elem.result_area.hide(),e.setOpenStatus(e,!1),t(window).off("scroll.SelectPage"),e.abortAjax(e),e.setButtonAttrDefault()},d.prototype.disabled=function(e,n){var i=(e.option,e.elem);return"undefined"===t.type(n)?i.combo_input.prop("disabled"):void("boolean"===t.type(n)&&(i.combo_input.prop("disabled",n),n?i.container.addClass(e.css_class.disabled):i.container.removeClass(e.css_class.disabled)))},d.prototype.firstPage=function(t){t.prop.current_page>1&&(t.prop.current_page=1,t.prop.page_move=!0,t.suggest(t))},d.prototype.prevPage=function(t){t.prop.current_page>1&&(t.prop.current_page--,t.prop.page_move=!0,t.suggest(t))},d.prototype.nextPage=function(t){t.prop.current_page<t.prop.max_page&&(t.prop.current_page++,t.prop.page_move=!0,t.suggest(t))},d.prototype.lastPage=function(t){t.prop.current_page<t.prop.max_page&&(t.prop.current_page=t.prop.max_page,t.prop.page_move=!0,t.suggest(t))},d.prototype.afterAction=function(t,e){t.inputResize(t),t.elem.combo_input.change(),t.setCssFocusedInput(t),t.prop.init_set||(t.option.multiple?(t.option.selectToCloseList&&(t.hideResults(t),t.elem.combo_input.blur()),!t.option.selectToCloseList&&e&&(t.suggest(t),t.elem.combo_input.focus())):(t.hideResults(t),t.elem.combo_input.blur()))},d.prototype.selectCurrentLine=function(e,n){e.scrollWindow(e,!0);var i=e.option,o=e.getCurrentLine(e);if(o){var a=o.data("dataObj"),r=a[i.showField]||o.text(),s=o.attr("pkey");if(i.multiple){e.elem.combo_input.val("");var l={text:r,value:s};e.isAlreadySelected(e,l)||(e.addNewTag(e,a,l),e.tagValuesSet(e))}else e.elem.combo_input.val(r),e.elem.hidden.val(s);i.selectOnly&&e.setButtonAttrDefault(),i.eSelect&&t.isFunction(i.eSelect)&&i.eSelect(a,e),e.prop.prev_value=e.elem.combo_input.val(),e.prop.selected_text=e.elem.combo_input.val(),e.putClearButton()}e.afterAction(e,!0)},d.prototype.putClearButton=function(){this.option.multiple||this.elem.combo_input.prop("disabled")||this.elem.container.append(this.elem.clear_btn)},d.prototype.selectAllLine=function(e){var n=e.option,i=new Array;e.elem.results.find("li").each(function(o,a){var r=t(a),s=r.data("dataObj"),l=s[n.showField]||r.text(),c=r.attr("pkey"),d={text:l,value:c};if(e.isAlreadySelected(e,d)||(e.addNewTag(e,s,d),e.tagValuesSet(e)),i.push(s),"number"===t.type(n.maxSelectLimit)&&n.maxSelectLimit>0&&n.maxSelectLimit===e.elem.element_box.find("li.selected_tag").length)return!1}),n.eSelect&&t.isFunction(n.eSelect)&&n.eSelect(i,e),e.afterAction(e,!0)},d.prototype.unSelectAllLine=function(e){var n=e.option,i=(e.elem.results.find("li").length,[]);e.elem.results.find("li").each(function(n,o){var a=t(o).attr("pkey"),r=e.elem.element_box.find('li.selected_tag[itemvalue="'+a+'"]');r.length&&i.push(r.data("dataObj")),e.removeTag(e,r)}),e.afterAction(e,!0),n.eTagRemove&&t.isFunction(n.eTagRemove)&&n.eTagRemove(i)},d.prototype.clearAll=function(e,n){var i=e.option,o=[];i.multiple&&(e.elem.element_box.find("li.selected_tag").each(function(e,n){o.push(t(n).data("dataObj")),n.remove()}),e.elem.element_box.find("li.selected_tag").remove()),e.reset(e),e.afterAction(e,n),i.multiple?i.eTagRemove&&t.isFunction(i.eTagRemove)&&i.eTagRemove(o):e.elem.clear_btn.remove()},d.prototype.reset=function(t){t.elem.combo_input.val(""),t.elem.hidden.val(""),t.prop.prev_value="",t.prop.selected_text="",t.prop.current_page=1},d.prototype.getCurrentLine=function(t){if(t.elem.result_area.is(":hidden"))return!1;var e=t.elem.results.find("li."+t.css_class.select);return!!e.length&&e},d.prototype.isAlreadySelected=function(e,n){var i=!1;if(n.value){var o=e.elem.hidden.val();if(o){var a=o.split(",");a&&a.length&&t.inArray(n.value,a)!=-1&&(i=!0)}}return i},d.prototype.addNewTag=function(e,n,i){if(e.option.multiple&&n&&i){var o,a=e.template.tag.content;a=a.replace(e.template.tag.textKey,i.text),a=a.replace(e.template.tag.valueKey,i.value),o=t(a),o.data("dataObj",n),e.elem.combo_input.prop("disabled")&&o.find("span.tag_close").hide(),e.elem.combo_input.closest("li").before(o)}},d.prototype.removeTag=function(e,n){var i=t(n).attr("itemvalue"),o=e.elem.hidden.val();if("undefined"!=t.type(i)&&o){var a=o.split(","),r=t.inArray(i.toString(),a);r!=-1&&(a.splice(r,1),e.elem.hidden.val(a.toString()).trigger("change"))}t(n).remove(),e.inputResize(e)},d.prototype.tagValuesSet=function(e){if(e.option.multiple){var n=e.elem.element_box.find("li.selected_tag");if(n&&n.length){var i=new Array;t.each(n,function(e,n){var o=t(n).attr("itemvalue");"undefined"!==t.type(o)&&i.push(o)}),i.length&&e.elem.hidden.val(i.join(",")).trigger("change")}}},d.prototype.inputResize=function(t){if(t.option.multiple){var e=t.elem.combo_input.closest("li"),n=function(t,e){e.removeClass("full_width");var n=t.elem.combo_input.val().length+1,i=.75*n+"em";t.elem.combo_input.css("width",i).removeAttr("placeholder")};0===t.elem.element_box.find("li.selected_tag").length?(e.hasClass("full_width")||e.addClass("full_width"),t.elem.combo_input.attr("placeholder_bak")&&t.elem.combo_input.attr("placeholder",t.elem.combo_input.attr("placeholder_bak")).removeAttr("style")):n(t,e)}},d.prototype.nextLine=function(t){var e,n=t.getCurrentLine(t);if(n?(e=t.elem.results.children("li").index(n),n.removeClass(t.css_class.select)):e=-1,e++,e<t.elem.results.children("li").length){var i=t.elem.results.children("li").eq(e);i.addClass(t.css_class.select),t.setCssFocusedResults(t)}else t.setCssFocusedInput(t);t.scrollWindow(t,!1)},d.prototype.prevLine=function(t){var e,n=t.getCurrentLine(t);if(n?(e=t.elem.results.children("li").index(n),n.removeClass(t.css_class.select)):e=t.elem.results.children("li").length,e--,e>-1){var i=t.elem.results.children("li").eq(e);i.addClass(t.css_class.select),t.setCssFocusedResults(t)}else t.setCssFocusedInput(t);t.scrollWindow(t,!1)};var u=t.fn.selectPage;t.fn.selectPage=e,t.fn.selectPage.Constructor=d,t.fn.selectPageClear=i,t.fn.selectPageRefresh=o,t.fn.selectPageData=a,t.fn.selectPageDisabled=r,t.fn.selectPageText=s,t.fn.selectPageSelectedData=l,t.fn.selectPage.noConflict=function(){return t.fn.selectPage=u,this}}(window.jQuery),define("selectpage",function(){});