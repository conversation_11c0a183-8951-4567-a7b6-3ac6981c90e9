# 业务流程文档

## 1. 系统概述

### 1.1 系统架构
本系统是一个基于来疯平台的支付网关系统，主要功能包括：
- 支付订单创建与处理
- 来疯账号管理与登录
- 异步支付状态监控与回调
- 商户管理与结算
- 数据统计与报表
- 订单自动归档
- 代理池管理
- 多渠道支付支持（微信、支付宝）

### 1.2 核心角色
- **商户**: 接入支付服务的第三方商家
- **用户**: 系统内部用户，管理收款账号
- **管理员**: 系统管理员，负责整体运营
- **收款账号**: 来疯平台的实际收款账号

## 2. 完整支付流程

### 2.1 支付流程时序图
```mermaid
sequenceDiagram
    participant C as 客户
    participant M as 商户
    participant G as 支付网关
    participant CS as 收银台
    participant H as 来疯API
    participant A as 收款账号

    C->>M: 1.发起支付请求
    M->>G: 2.调用创建订单API
    G->>G: 3.验证商户和签名
    G->>G: 4.创建本地订单
    G->>CS: 5.返回收银台链接
    M->>C: 6.重定向到收银台
    
    C->>CS: 7.访问收银台页面
    CS->>CS: 8.获取订单信息
    CS->>CS: 9.检查是否有支付链接
    
    alt 无支付链接
        CS->>CS: 10.获取缓存锁
        CS->>CS: 11.选择可用账号
        CS->>CS: 12.分配代理IP
        CS->>H: 13.调用来疯充值API
        H->>A: 14.创建来疯订单
        A-->>H: 15.返回支付链接
        H-->>CS: 16.返回支付信息
        CS->>CS: 17.更新订单支付链接
        CS->>CS: 18.释放缓存锁
    end
    
    CS->>C: 19.返回支付链接
    C->>H: 20.访问来疯支付页面
    C->>H: 21.完成支付
    H->>A: 22.支付成功通知
    
    CS->>H: 23.轮询支付状态
    H-->>CS: 24.返回支付状态
    CS->>G: 25.更新订单状态
    G->>M: 26.发送异步通知
    M-->>G: 27.确认接收通知
```

### 2.2 详细流程步骤

#### 阶段一：订单创建（商户侧）
1. **客户发起支付**: 客户在商户网站选择商品并发起支付
2. **商户调用API**: 商户调用支付网关的创建订单接口
3. **参数验证**: 系统验证商户身份、签名、金额等参数
4. **订单入库**: 创建本地订单记录，状态为"未支付"
5. **返回收银台**: 返回收银台页面链接给商户
6. **页面跳转**: 商户将客户重定向到收银台页面

#### 阶段二：支付链接获取（收银台侧）
1. **页面加载**: 客户访问收银台页面
2. **订单查询**: 根据订单号查询订单信息
3. **链接检查**: 检查订单是否已有支付链接
4. **并发控制**: 使用缓存锁防止重复处理
5. **账号选择**: 智能选择可用的收款账号
6. **代理分配**: 根据客户IP分配合适的代理
7. **来疯拉单**: 调用来疯API创建支付订单
8. **链接更新**: 将获取的支付链接更新到订单
9. **页面跳转**: 将客户重定向到来疯支付页面

#### 阶段三：支付处理（来疯侧）
1. **支付页面**: 客户在来疯页面完成支付操作
2. **支付确认**: 来疯处理支付并更新订单状态
3. **状态同步**: 来疯将支付结果同步到收款账号

#### 阶段四：状态监控（系统侧）
1. **异步监控**: 后台服务自动监控订单支付状态
2. **批量检查**: 每5秒检查近5分钟的未支付订单
3. **并发处理**: 最大100个并发请求检查支付状态
4. **状态更新**: 发现支付成功后更新本地订单
5. **异步通知**: 向商户发送支付成功通知
6. **重试机制**: 通知失败时进行重试
7. **性能统计**: 记录检查耗时和成功率

## 3. 账号管理流程

### 3.1 账号登录流程
```mermaid
flowchart TD
    A[获取二维码] --> B[显示二维码]
    B --> C[用户扫码]
    C --> D[轮询登录状态]
    D --> E{登录成功?}
    E -->|否| F[继续轮询]
    E -->|是| G[获取Cookie]
    G --> H[保存账号信息]
    H --> I[账号可用]
    F --> D
```

#### 详细步骤：
1. **获取二维码**: 调用来疯API获取登录二维码ID
2. **显示二维码**: 在管理页面显示二维码供用户扫描
3. **状态轮询**: 前端定时查询登录状态
4. **登录确认**: 用户扫码确认登录
5. **Cookie获取**: 获取登录后的Cookie信息
6. **账号保存**: 将账号信息和配置保存到数据库
7. **状态更新**: 将账号状态设置为可用

### 3.2 账号选择策略
```mermaid
flowchart TD
    A[开始选择账号] --> B{轮询模式?}
    B -->|是| C[按拉单时间升序]
    B -->|否| D[按ID降序]
    C --> E[过滤异常账号]
    D --> E
    E --> F[过滤频繁操作账号]
    F --> G[过滤未登录账号]
    G --> H[过滤违规账号]
    H --> I[过滤风险账号]
    I --> J{有可用账号?}
    J -->|是| K[返回第一个账号]
    J -->|否| L[返回无可用账号]
```

#### 过滤规则：
- **异常账号**: errormsg包含"异常"
- **频繁操作**: errormsg包含"频繁"
- **未登录**: errormsg包含"登录"
- **违规账号**: errormsg包含"违规"
- **风险账号**: errormsg包含"风险"
- **状态检查**: status=1（启用状态）

## 4. 订单状态流转

### 4.1 订单状态图
```mermaid
stateDiagram-v2
    [*] --> 创建订单
    创建订单 --> 获取支付链接: 拉单成功
    创建订单 --> 拉单失败: 拉单失败
    获取支付链接 --> 支付中: 用户访问支付页面
    支付中 --> 支付成功: 用户完成支付
    支付中 --> 支付超时: 超过5分钟
    支付成功 --> 通知成功: 商户确认接收
    支付成功 --> 通知失败: 商户未确认
    通知失败 --> 通知成功: 重试成功
    通知失败 --> 通知放弃: 重试5次失败
    拉单失败 --> [*]
    支付超时 --> [*]
    通知成功 --> [*]
    通知放弃 --> [*]
```

### 4.2 状态字段说明
| 字段 | 值 | 说明 | 业务含义 |
|------|----|----|---------|
| pay_status | 0 | 未支付 | 订单创建但未完成支付 |
| pay_status | 1 | 已支付 | 用户已完成支付 |
| callback_status | 0 | 未回调 | 尚未发送通知给商户 |
| callback_status | 1 | 回调成功 | 商户已确认接收通知 |
| callback_status | 2 | 回调失败 | 商户未确认或通知失败 |

## 5. 异常处理流程

### 5.1 拉单失败处理
```mermaid
flowchart TD
    A[拉单失败] --> B[记录错误日志]
    B --> C[更新账号错误信息]
    C --> D{错误类型判断}
    D -->|登录失效| E[标记账号需重新登录]
    D -->|频繁操作| F[标记账号暂时不可用]
    D -->|违规风险| G[标记账号高风险]
    D -->|网络异常| H[保持账号状态]
    E --> I[返回错误给用户]
    F --> I
    G --> I
    H --> I
```

### 5.2 支付超时处理
```mermaid
flowchart TD
    A[检测支付超时] --> B[标记订单超时]
    B --> C[释放占用资源]
    C --> D[记录超时日志]
    D --> E[统计超时率]
    E --> F[优化账号选择]
```

### 5.3 通知重试机制
```mermaid
flowchart TD
    A[发送通知] --> B{通知成功?}
    B -->|是| C[标记通知成功]
    B -->|否| D[重试次数+1]
    D --> E{重试次数<5?}
    E -->|是| F[等待重试间隔]
    E -->|否| G[标记通知放弃]
    F --> H[重新发送通知]
    H --> B
    C --> I[完成]
    G --> I
```

#### 重试间隔策略：
- 第1次重试：1分钟后
- 第2次重试：5分钟后
- 第3次重试：15分钟后
- 第4次重试：30分钟后
- 第5次重试：60分钟后

## 6. 并发控制机制

### 6.1 缓存锁机制
```php
// 获取订单锁
if (!CacheLock::acquireOrderLock($orderNo, 5)) {
    return '订单正在处理中，请稍后重试';
}

try {
    // 业务处理逻辑
    $result = processOrder($orderNo);
    return $result;
} finally {
    // 确保释放锁
    CacheLock::releaseOrderLock($orderNo);
}
```

### 6.2 双重检查模式
```php
// 第一次检查
if (!empty($order['pay_url'])) {
    return $order['pay_url'];
}

// 获取锁后再次检查
if (CacheLock::acquireOrderLock($orderNo)) {
    $order = getOrderFromDB($orderNo);
    if (!empty($order['pay_url'])) {
        return $order['pay_url'];  // 其他进程已处理
    }
    
    // 执行拉单逻辑
    $payUrl = createPayOrder($order);
    updateOrderPayUrl($orderNo, $payUrl);
    
    CacheLock::releaseOrderLock($orderNo);
    return $payUrl;
}
```

## 7. 业务规则

### 7.1 金额限制规则
- **最小金额**: 0.01元
- **最大金额**: 50000.00元
- **精度要求**: 保留2位小数
- **验证规则**: 必须为正数且在范围内

### 7.2 账号使用规则
- **轮询模式**: 按拉单时间升序选择账号
- **非轮询模式**: 按ID降序选择账号
- **冷却时间**: 账号拉单后需等待一定时间
- **错误处理**: 连续失败的账号暂时禁用

### 7.3 商户管理规则
- **费率设置**: 0.0000-1.0000之间
- **余额管理**: 支持预付费和后付费模式
- **IP白名单**: 限制访问来源IP
- **签名验证**: 所有请求必须验证签名

### 7.4 风控策略
- **频率限制**: 限制API调用频率
- **异常监控**: 监控异常订单和账号
- **风险评估**: 对高风险操作进行拦截
- **黑名单**: 维护商户和IP黑名单

## 8. 数据统计与监控

### 8.1 关键指标
- **订单成功率**: 成功订单数/总订单数
- **支付成功率**: 支付成功数/创建订单数
- **账号可用率**: 可用账号数/总账号数
- **平均响应时间**: API接口平均响应时间
- **异常率**: 异常订单数/总订单数

### 8.2 监控告警
- **订单积压**: 超过100个未处理订单
- **账号异常**: 可用账号少于10个
- **支付失败**: 成功率低于90%
- **系统异常**: 错误率超过5%
- **响应超时**: 平均响应时间超过3秒

### 8.3 日志记录
- **操作日志**: 记录所有关键操作
- **错误日志**: 记录所有异常和错误
- **性能日志**: 记录接口响应时间
- **业务日志**: 记录业务流程关键节点

## 9. 定时任务

### 9.1 订单归档任务
- **执行时间**: 每天凌晨2点
- **处理逻辑**: 将30天前的订单归档到历史表
- **批量大小**: 每批处理1000条
- **监控指标**: 归档数量和耗时

### 9.2 账号余额更新
- **执行频率**: 每小时执行一次
- **处理逻辑**: 更新所有账号的余额信息
- **并发控制**: 分批并发处理
- **异常处理**: 失败账号记录日志

### 9.3 数据清理任务
- **日志清理**: 清理90天前的日志
- **临时文件**: 清理过期的临时文件
- **缓存清理**: 清理过期的缓存数据

## 10. 应急处理预案

### 10.1 系统故障处理
1. **故障发现**: 监控系统发现异常
2. **影响评估**: 评估故障影响范围
3. **应急响应**: 启动应急处理流程
4. **故障修复**: 定位问题并修复
5. **服务恢复**: 恢复正常服务
6. **事后总结**: 分析原因并改进

### 10.2 数据恢复流程
1. **备份检查**: 确认备份数据完整性
2. **恢复策略**: 制定数据恢复方案
3. **恢复执行**: 执行数据恢复操作
4. **数据验证**: 验证恢复数据正确性
5. **服务重启**: 重启相关服务
6. **功能测试**: 测试系统功能正常

## 11. 异步支付监控流程

### 11.1 监控服务启动
```mermaid
flowchart TD
    A[启动监控服务] --> B[初始化配置]
    B --> C[进入无限循环]
    C --> D[查询未支付订单]
    D --> E{有订单需要检查?}
    E -->|是| F[分批处理订单]
    E -->|否| G[等待5秒]
    F --> H[异步并发查询]
    H --> I[处理查询结果]
    I --> J[更新订单状态]
    J --> K[执行回调逻辑]
    K --> L[统计执行结果]
    L --> G
    G --> C
```

### 11.2 订单状态检查流程
1. **订单筛选**: 查询近5分钟创建的未支付订单
2. **批量处理**: 将订单分批，每批最多100个
3. **并发查询**: 使用curl_multi实现异步并发请求
4. **状态解析**: 解析来疯API返回的支付状态
5. **数据更新**: 更新支付成功的订单状态
6. **回调执行**: 自动执行商户回调通知
7. **性能统计**: 记录总耗时和平均耗时

### 11.3 回调处理流程
1. **获取商户信息**: 查询订单对应的商户配置
2. **构建回调参数**: 组装回调通知数据
3. **签名生成**: 使用商户密钥生成签名
4. **发送通知**: 向商户回调URL发送POST请求
5. **结果处理**: 根据响应更新回调状态
6. **重试机制**: 失败时记录重试次数

## 12. 订单归档流程

### 12.1 自动归档策略
- **归档条件**: 订单创建时间超过30天
- **执行时间**: 每天凌晨2点自动执行
- **批量处理**: 每批处理1000条订单
- **数据迁移**: 从主表迁移到归档表
- **索引优化**: 保持主表查询性能

### 12.2 归档执行流程
1. **检查时间**: 判断是否到达执行时间
2. **查询数据**: 获取需要归档的订单
3. **数据迁移**: 将数据插入归档表
4. **验证完整**: 确认数据迁移完整性
5. **删除原数据**: 从主表删除已归档数据
6. **更新统计**: 记录归档数量和状态

## 13. 代理管理流程

### 13.1 代理选择策略
1. **地域匹配**: 根据客户IP选择合适地区代理
2. **负载均衡**: 轮换使用可用代理
3. **健康检查**: 定期检测代理可用性
4. **故障切换**: 自动切换到备用代理

### 13.2 代理认证流程
1. **获取认证信息**: 从配置中获取代理用户名密码
2. **构建代理URL**: 组装代理服务器地址
3. **请求转发**: 通过代理服务器转发请求
4. **响应处理**: 处理代理返回的响应数据

### 10.3 安全事件处理
1. **事件发现**: 发现安全异常
2. **紧急隔离**: 隔离受影响系统
3. **影响评估**: 评估安全影响
4. **修复加固**: 修复漏洞并加固
5. **监控加强**: 加强安全监控
6. **总结改进**: 总结经验并改进