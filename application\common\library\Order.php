<?php

namespace app\common\library;

use think\Db;
use fast\Http;

/**
 * 订单工具类
 */
class Order
{

    /**
     * 参数排序
     */
    public static function ascii($params = array())
    {
        unset($params['undefined']);
        unset($params['sign']);
        //ksort()对数组按照键名进行升序排序
        ksort($params);
        //reset()内部指针指向数组中的第一个元素
        reset($params);
        $str = http_build_query($params, '', '&');
        return $str;
    }

    /**
     * 生成唯一订单号
     * 格式: 年月日时分秒(14位) + 毫秒(3位) + 随机数(3位) + 用户ID后4位(4位)
     * 
     * @param int $userId 用户ID
     * @return string 24位订单号
     */
    public static function generateOrderNo($userId = 0)
    {
        // 获取毫秒级时间戳
        list($msec, $sec) = explode(' ', microtime());
        $msec = substr(round($msec, 3), 2, 3);

        // 生成随机数
        $random = str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);

        // 用户ID后4位，不足4位前面补0
        $userSuffix = str_pad(substr($userId, -4), 4, '0', STR_PAD_LEFT);

        // 组合订单号
        $orderNo = date('YmdHis') . $msec . $random . $userSuffix;

        // 确保订单号唯一
        while (self::checkOrderNoExists($orderNo)) {
            $random = str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);
            $orderNo = date('YmdHis') . $msec . $random . $userSuffix;
        }

        return $orderNo;
    }

    /**
     * 给商户回调
     * 
     * @param string $url 回调地址
     * @param string $key 签名密钥
     * @param string $out_trade_no 商户订单号
     * @param float $amount 金额
     * @param string $channel_code 支付渠道
     * @param string $status 支付状态
     * @param int $userId 用户ID
     * @return bool
     */
    public static function merchantsCallback($url, $key, $out_trade_no, $amount, $channel_code, $status, $userId)
    {
        $params = [
            "out_trade_no" => $out_trade_no,
            "amount" => $amount,
            "channel_code" => $channel_code,
            "status" => $status
        ];
        $params['sign'] = md5(Order::ascii($params) . "&key=" . $key);

        // 设置最大重试次数和当前重试次数
        $maxRetries = 3;
        $currentRetry = 0;
        $res = '';

        while ($currentRetry < $maxRetries) {
            $res = Http::post($url, $params);
            trace("商户返回(第" . ($currentRetry + 1) . "次):" . $res, "error");

            // 如果返回成功或不为空，跳出循环
            if ($res == 'SUCCESS' || $res == 'success' || !empty($res)) {
                break;
            }

            // 重试次数加1
            $currentRetry++;

            // 如果还有重试机会，等待后继续
            if ($currentRetry < $maxRetries) {
                // 等待时间随重试次数增加而增加（1秒、2秒、3秒）
                sleep($currentRetry);
                trace("商户回调重试第{$currentRetry}次", "error");
            }
        }

        if ($res == 'SUCCESS' or $res == 'success') {
            // 回调成功后扣除余额
            try {
                if (self::deductUserBalance($userId, $amount) && self::deductUserBalance(1, $amount)) {
                    trace('商户回调成功并扣除余额: 用户ID=' . $userId . ', 金额=' . $amount, 'info');
                    return true;
                } else {
                    trace('商户回调成功但扣除余额失败: 用户ID=' . $userId . ', 金额=' . $amount, 'error');
                    return false;
                }
            } catch (\Exception $e) {
                trace('商户回调成功但扣除余额异常: ' . $e->getMessage(), 'error');
                return false;
            }
        }

        trace('商户回调失败: ' . $res, 'error');
        return false;
    }

    /**
     * 检查订单号是否已存在
     * 
     * @param string $orderNo 订单号
     * @return bool
     */
    protected static function checkOrderNoExists($orderNo)
    {
        return Db::name('order')->where('local_trade_no', $orderNo)->count() > 0;
    }

    /**
     * 扣除用户的余额
     * 使用事务和乐观锁确保并发安全
     * 允许余额为负数，实现透支功能
     * 
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @return bool
     * @throws \think\Exception
     */
    public static function deductUserBalance($userId, $amount)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 查询用户当前余额和版本号
            $user = Db::name('user')
                ->field('money, version')
                ->where('id', $userId)
                ->lock(true)
                ->find();

            if (!$user) {
                throw new \think\Exception('用户不存在');
            }

            // 使用乐观锁更新余额，允许为负数
            $result = Db::name('user')
                ->where('id', $userId)
                ->where('version', $user['version'])
                ->update([
                    'money' => Db::raw('money - ' . $amount),
                    'version' => Db::raw('version + 1')
                ]);

            // 如果更新失败，说明版本号不匹配，可能是并发操作导致
            if ($result === false) {
                throw new \think\Exception('操作失败，请重试');
            }

            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            // throw $e;
            trace('扣除用户余额失败: ' . $e->getMessage(), 'error');
            return false;
        }
        return true;
    }
}
