<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\common\library\Upload;
use app\common\model\Area;
use app\common\model\Version;
use fast\Http;
use fast\Random;
use think\captcha\Captcha;
use think\Config;
use think\Hook;

/**
 * 公共接口
 */
class Common extends Api
{
    protected $noNeedLogin = ['init', 'captcha'];
    protected $noNeedRight = '*';

    public function _initialize()
    {

        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header('Access-Control-Expose-Headers: __token__'); //跨域让客户端获取到
        }
        //跨域检测
        check_cors_request();

        if (!isset($_COOKIE['PHPSESSID'])) {
            Config::set('session.id', $this->request->server("HTTP_SID"));
        }
        parent::_initialize();
    }

    /**
     * 加载初始化
     *
     * @ApiParams (name="version", type="string", required=true, description="版本号")
     * @ApiParams (name="lng", type="string", required=true, description="经度")
     * @ApiParams (name="lat", type="string", required=true, description="纬度")
     */
    public function init()
    {
        if ($version = $this->request->request('version')) {
            $lng = $this->request->request('lng');
            $lat = $this->request->request('lat');

            //配置信息
            $upload = Config::get('upload');
            //如果非服务端中转模式需要修改为中转
            if ($upload['storage'] != 'local' && isset($upload['uploadmode']) && $upload['uploadmode'] != 'server') {
                //临时修改上传模式为服务端中转
                set_addon_config($upload['storage'], ["uploadmode" => "server"], false);

                $upload = \app\common\model\Config::upload();
                // 上传信息配置后
                Hook::listen("upload_config_init", $upload);

                $upload = Config::set('upload', array_merge(Config::get('upload'), $upload));
            }

            $upload['cdnurl'] = $upload['cdnurl'] ? $upload['cdnurl'] : cdnurl('', true);
            $upload['uploadurl'] = preg_match("/^((?:[a-z]+:)?\/\/)(.*)/i", $upload['uploadurl']) ? $upload['uploadurl'] : url($upload['storage'] == 'local' ? '/api/common/upload' : $upload['uploadurl'], '', false, true);

            $content = [
                'citydata'    => Area::getCityFromLngLat($lng, $lat),
                'versiondata' => Version::check($version),
                'uploaddata'  => $upload,
                'coverdata'   => Config::get("cover"),
            ];
            $this->success('', $content);
        } else {
            $this->error(__('Invalid parameters'));
        }
    }

    /**
     * 上传文件
     * @ApiMethod (POST)
     * @ApiParams (name="file", type="File", required=true, description="文件流")
     */
    public function upload()
    {
        return '123';
        Config::set('default_return_type', 'json');
        //必须设定cdnurl为空,否则cdnurl函数计算错误
        Config::set('upload.cdnurl', '');
        $chunkid = $this->request->post("chunkid");
        if ($chunkid) {
            if (!Config::get('upload.chunking')) {
                $this->error(__('Chunk file disabled'));
            }
            $action = $this->request->post("action");
            $chunkindex = $this->request->post("chunkindex/d");
            $chunkcount = $this->request->post("chunkcount/d");
            $filename = $this->request->post("filename");
            $method = $this->request->method(true);
            if ($action == 'merge') {
                $attachment = null;
                //合并分片文件
                try {
                    $upload = new Upload();
                    $attachment = $upload->merge($chunkid, $chunkcount, $filename);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success(__('Uploaded successful'), ['url' => $attachment->url, 'fullurl' => cdnurl($attachment->url, true)]);
            } elseif ($method == 'clean') {
                //删除冗余的分片文件
                try {
                    $upload = new Upload();
                    $upload->clean($chunkid);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            } else {
                //上传分片文件
                //默认普通上传文件
                $file = $this->request->file('file');
                try {
                    $upload = new Upload($file);
                    $upload->chunk($chunkid, $chunkindex, $chunkcount);
                } catch (UploadException $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            }
        } else {
            $attachment = null;
            //默认普通上传文件
            $file = $this->request->file('file');
            try {
                $upload = new Upload($file);
                $attachment = $upload->upload();
            } catch (UploadException $e) {
                $this->error($e->getMessage());
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }

            $this->success(__('Uploaded successful'), ['url' => $attachment->url, 'fullurl' => cdnurl($attachment->url, true)]);
        }
    }

    /**
     * 验证码
     * @ApiParams (name="id", type="string", required=true, description="要生成验证码的标识")
     * @return \think\Response
     */
    public function captcha($id = "")
    {
        \think\Config::set([
            'captcha' => array_merge(config('captcha'), [
                'fontSize' => 44,
                'imageH'   => 150,
                'imageW'   => 350,
            ])
        ]);
        $captcha = new Captcha((array)Config::get('captcha'));
        return $captcha->entry($id);
    }

    /**
     * 获取轮询模式状态
     * @ApiMethod (GET)
     * @return \think\Response
     */
    public function getPollingStatus()
    {
        $pollingConfig = Config::get('polling');
        $status = [
            'enabled' => $pollingConfig['enabled'] ?? false
        ];

        $this->success('获取轮询模式状态成功', $status);
    }

    /**
     * 设置轮询模式
     * @ApiMethod (POST)
     * @ApiParams (name="enabled", type="boolean", required=true, description="是否开启轮询模式")
     * @return \think\Response
     */
    public function setPollingMode()
    {
        $enabled = input('enabled');

        // 参数验证
        if (!is_bool($enabled) && !in_array($enabled, [0, 1, '0', '1', true, false])) {
            $this->error('enabled参数必须是布尔值');
        }

        // 转换enabled为布尔值
        $enabled = filter_var($enabled, FILTER_VALIDATE_BOOLEAN);

        // 读取当前配置
        $configFile = APP_PATH . 'extra' . DS . 'polling.php';
        $currentConfig = include $configFile;

        // 更新配置
        $newConfig = [
            'enabled' => $enabled
        ];

        // 写入配置文件
        $configContent = "<?php\n\nreturn " . var_export($newConfig, true) . ";\n";

        if (file_put_contents($configFile, $configContent) === false) {
            $this->error('配置文件写入失败');
        }

        // 清除配置缓存
        \think\Cache::clear();

        $message = $enabled ? '轮询模式已开启' : '轮询模式已关闭';
        $this->success($message, $newConfig);
    }

    /**
     * 删除N天前的归档订单数据
     * @ApiMethod (POST)
     * @ApiParams (name="days", type="integer", required=true, description="删除多少天前的归档订单数据")
     * @ApiParams (name="confirm", type="boolean", required=true, description="确认删除操作")
     * @return \think\Response
     */
    public function deleteOldOrders()
    {
        // 获取参数
        $days = input('days/d', 0);
        $confirm = input('confirm', false);

        // 参数验证
        if ($days <= 0) {
            $this->error('天数必须大于0');
        }

        if (!$confirm) {
            $this->error('请确认删除操作');
        }

        // 权限检查：只有管理员可以执行此操作
        if ($this->auth->group_id != 1) {
            $this->error('只有管理员可以执行此操作');
        }

        // 计算删除的时间点
        $deleteTime = time() - ($days * 24 * 3600);

        // 测试数据库连接
        $testResult = \think\Db::name('order_history')->count();
        if ($testResult === false) {
            $this->error('数据库连接测试失败，请检查数据库配置');
        }

        // 先查询要删除的归档订单数量（在事务外）
        $count = \think\Db::name('order_history')
            ->where('create_time', '<', $deleteTime)
            ->count();

        if ($count == 0) {
            $this->error('没有找到符合条件的归档订单数据');
        }

        // 删除N天前的归档订单数据
        $result = \think\Db::name('order_history')
            ->where('create_time', '<', $deleteTime)
            ->delete();

        if ($result === false) {
            $this->error('删除归档订单数据失败');
        }

        $this->success("成功删除{$result}条{$days}天前的归档订单数据", [
            'deleted_count' => $result,
            'days' => $days,
            'delete_time' => date('Y-m-d H:i:s', $deleteTime)
        ]);
    }

    /**
     * 合并主表(order)和归档表(order_history)的统计数据
     * @param array $where 查询条件
     * @param string $type 统计类型 count/sum
     * @param string $field 字段名，sum时用
     * @return float|int
     */
    private function getOrderStats($where, $type = 'count', $field = 'id')
    {
        if ($type === 'count') {
            $main = \think\Db::name('order')->where($where)->count($field);
            $archive = \think\Db::name('order_history')->where($where)->count($field);
        } elseif ($type === 'sum') {
            $main = \think\Db::name('order')->where($where)->sum($field);
            $archive = \think\Db::name('order_history')->where($where)->sum($field);
        } else {
            $main = 0;
            $archive = 0;
        }
        return $main + $archive;
    }
}
