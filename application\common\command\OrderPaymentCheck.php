<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;
use laifeng\Service;
use Proxy;

class OrderPaymentCheck extends Command
{
    // 最大并发数
    const MAX_CONCURRENT = 100;
    // 执行间隔时间（秒）
    const INTERVAL = 5;
    // 查询时间范围（分钟）
    const TIME_RANGE = 5;
    // 请求超时时间（秒）
    const TIMEOUT = 10;

    protected function configure()
    {
        // php think order:payment-check
        $this->setName('order:payment-check')
            ->setDescription('异步批量查询订单支付状态');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('启动订单支付状态检查服务...');
        
        while (true) {
            try {
                $startTime = microtime(true);
                $output->writeln('开始执行订单支付状态检查...');
                $output->writeln('执行时间：' . date('Y-m-d H:i:s'));

                // 获取近5分钟的未支付订单
                $unpaidOrders = $this->getUnpaidOrders();

                if (empty($unpaidOrders)) {
                    $output->writeln('没有找到需要检查的未支付订单');
                } else {
                    $output->writeln('共找到 ' . count($unpaidOrders) . ' 个未支付订单需要检查');

                    // 异步批量检查订单状态
                    $results = $this->batchCheckOrderStatus($unpaidOrders, $output);

                    // 统计并输出结果
                    $paidCount = 0;
                    $unpaidCount = 0;
                    foreach ($results as $result) {
                        if ($result['success'] && $result['paid']) {
                            $paidCount++;
                        } else {
                            $unpaidCount++;
                        }
                    }

                    $output->writeln("检查结果：已支付 {$paidCount} 个，未支付 {$unpaidCount} 个");
                }

                // 计算耗时
                $endTime = microtime(true);
                $totalTime = round(($endTime - $startTime) * 1000, 2); // 转换为毫秒
                $orderCount = count($unpaidOrders);
                $avgTime = $orderCount > 0 ? round($totalTime / $orderCount, 2) : 0;

                $output->writeln("执行耗时：总耗时 {$totalTime}ms，平均耗时 {$avgTime}ms/订单");
                $output->writeln('本轮检查完成，等待 ' . self::INTERVAL . ' 秒后执行下一轮...');

            } catch (\Exception $e) {
                $errorMsg = "执行订单支付状态检查时发生错误：" . $e->getMessage();
                $output->writeln($errorMsg);
                Log::write($errorMsg, 'error');
            }

            // 执行完成后休眠指定时间
            sleep(self::INTERVAL);
        }
    }

    /**
     * 获取近5分钟的未支付订单
     * @return array
     */
    protected function getUnpaidOrders()
    {
        $timeLimit = time() - (self::TIME_RANGE * 60);
        $data = Db::name('order')
            ->field('id, out_trade_no, pay_trade_no, dy_order_id, merchant_id,
                    user_id, amount, paytype, callback_url, pay_status')
            ->where('pay_status', 0)
            ->where('dy_order_id', 'not null')
            ->where('create_time', '>=', $timeLimit)
            ->select();
        return $data;
    }

    /**
     * 异步批量检查订单状态
     * @param array $orders 订单列表
     * @param Output $output 输出对象
     * @return array 所有处理结果
     */
    protected function batchCheckOrderStatus($orders, Output $output)
    {
        // 分批处理，每批最多100个
        $chunks = array_chunk($orders, self::MAX_CONCURRENT);
        $totalSuccess = 0;
        $totalFailed = 0;
        $allResults = [];

        foreach ($chunks as $chunkIndex => $chunk) {
            $output->writeln("处理第 " . ($chunkIndex + 1) . " 批，共 " . count($chunk) . " 个订单");

            // 创建多进程处理
            $results = $this->processOrdersAsync($chunk, $output);
            $allResults = array_merge($allResults, $results);

            // 统计结果
            foreach ($results as $result) {
                if ($result['success']) {
                    $totalSuccess++;
                } else {
                    $totalFailed++;
                    $output->writeln("订单 {$result['order_id']} 检查失败：{$result['error']}");
                }
            }
        }

        $output->writeln("批量检查完成！成功：{$totalSuccess}，失败：{$totalFailed}");
        Log::write("订单支付状态检查完成，成功：{$totalSuccess}，失败：{$totalFailed}", 'info');

        return $allResults;
    }

    /**
     * 异步处理订单列表
     * @param array $orders 订单列表
     * @param Output $output 输出对象
     * @return array 处理结果
     */
    protected function processOrdersAsync($orders, Output $output)
    {
        $results = [];
        
        // 使用curl_multi实现异步请求
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        
        // 初始化所有curl句柄
        foreach ($orders as $order) {
            $curl = $this->createCurlHandle($order);
            if ($curl) {
                curl_multi_add_handle($multiHandle, $curl);
                $curlHandles[] = [
                    'handle' => $curl,
                    'order' => $order
                ];
            }
        }

        // 执行所有请求
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);

        // 处理结果
        foreach ($curlHandles as $curlData) {
            $response = curl_multi_getcontent($curlData['handle']);
            $httpCode = curl_getinfo($curlData['handle'], CURLINFO_HTTP_CODE);
            $error = curl_error($curlData['handle']);
            
            $result = $this->processOrderResponse($curlData['order'], $response, $httpCode, $error);
            $results[] = $result;
            
            curl_multi_remove_handle($multiHandle, $curlData['handle']);
            curl_close($curlData['handle']);
        }
        
        curl_multi_close($multiHandle);
        
        return $results;
    }

    /**
     * 创建curl句柄
     * @param array $order 订单信息
     * @return resource|false
     */
    protected function createCurlHandle($order)
    {
        try {
            // 获取代理信息
            // $proxy = Proxy::getProxyByShenLong(1, "");
            // if (!$proxy) {
            //     return false;
            // }

            // $proxyUsername = Proxy::$proxyUsername;
            // $proxyPassword = Proxy::$proxyPassword;
            // $proxyUrl = "http://" . $proxyUsername . ":" . $proxyPassword . "@" . $proxy;
            
            $targetUrl = "https://yapi.laifeng.com/lftop/mtop.laifeng.cashier.charge.order.get/2.0/?chargeNo={$order['dy_order_id']}";
            // $url = "http://" . Proxy::getProxyServer() . ":3000/proxy.php?target_url=" . urlencode($targetUrl);
            
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $targetUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => self::TIMEOUT,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => [
                    'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                    'pragma: no-cache',
                    'cache-control: no-cache',
                    'origin: https://zhifu.laifeng.com',
                    'sec-fetch-site: same-site',
                    'sec-fetch-mode: cors',
                    'sec-fetch-dest: empty',
                    'referer: https://zhifu.laifeng.com/',
                    'accept-language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
                    'priority: u=1, i',
                ],
            ]);
            
            return $curl;
            
        } catch (\Exception $e) {
            Log::write("创建curl句柄失败：" . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 处理订单响应
     * @param array $order 订单信息
     * @param string $response 响应内容
     * @param int $httpCode HTTP状态码
     * @param string $error 错误信息
     * @return array 处理结果
     */
    protected function processOrderResponse($order, $response, $httpCode, $error)
    {
        $result = [
            'order_id' => $order['id'],
            'success' => false,
            'paid' => false,
            'error' => ''
        ];

        try {
            if ($error) {
                $result['error'] = "请求错误：" . $error;
                return $result;
            }

            if ($httpCode !== 200) {
                $result['error'] = "HTTP错误：" . $httpCode;
                return $result;
            }

            $responseData = json_decode($response, true);
            if (!$responseData) {
                $result['error'] = "响应解析失败";
                return $result;
            }

            $result['success'] = true;

            // 检查支付状态 (status = 2 表示已支付)
            if (isset($responseData['data']['status']) && $responseData['data']['status'] == 2) {
                $result['paid'] = true;
                
                // 更新订单支付状态并执行回调
                $this->updateOrderPaidStatus($order);
            }

        } catch (\Exception $e) {
            $result['error'] = "处理异常：" . $e->getMessage();
        }

        return $result;
    }

    /**
     * 更新订单支付状态并执行回调
     * @param array $order 订单信息
     */
    protected function updateOrderPaidStatus($order)
    {
        try {
            // 开启事务
            Db::startTrans();
            
            // 更新支付状态
            Db::name('order')->where('id', $order['id'])->update([
                'pay_status' => 1
            ]);

            // 如果还未回调，执行回调逻辑
            $currentOrder = Db::name('order')->where('id', $order['id'])->find();
            if ($currentOrder['callback_status'] != 1) {
                $this->executeCallback($order);
            }
            
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::write("更新订单支付状态失败：" . $e->getMessage(), 'error');
        }
    }

    /**
     * 执行回调逻辑
     * @param array $order 订单信息
     */
    protected function executeCallback($order)
    {
        try {
            // 获取商户信息
            $merchant = Db::name('merchant')->where('id', $order['merchant_id'])->find();
            if (!$merchant) {
                Log::write("订单 {$order['id']} 商户不存在", 'error');
                return;
            }

            // 执行回调
            $callbackResult = \app\common\library\Order::merchantsCallback(
                $order['callback_url'],
                $merchant['key'],
                $order['out_trade_no'],
                $order['amount'],
                $order['paytype'],
                1,
                $order['user_id']
            );

            // 更新回调状态
            if ($callbackResult) {
                Db::name('order')->where('id', $order['id'])->update([
                    'callback_status' => 1,
                    'callback_time' => time()
                ]);
                Log::write("订单 {$order['id']} 回调成功", 'info');
            } else {
                Db::name('order')->where('id', $order['id'])->update([
                    'callback_status' => 2,
                    'callback_retry_count' => Db::raw('callback_retry_count + 1'),
                    'last_callback_time' => time()
                ]);
                Log::write("订单 {$order['id']} 回调失败", 'error');
            }

        } catch (\Exception $e) {
            Log::write("订单 {$order['id']} 回调异常：" . $e->getMessage(), 'error');
        }
    }
}
