<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use fast\Http;
use Proxy;

class Telegram extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        return $this->view->fetch();
    }

    public function messageTask()
    {
        // 获取 微信 最近一小时金额，今日金额，昨日金额
        $hourAmountWx = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'lfwx')->where('pay_status', 1)->sum('amount');
        $todayAmountWx = db('order')->whereTime('callback_time', 'today')->where('paytype', 'lfwx')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWx = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'lfwx')->where('pay_status', 1)->sum('amount');

        // 获取 支付宝 最近一小时金额，今日金额，昨日金额
        $hourAmountZfb = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'lfali')->where('pay_status', 1)->sum('amount');
        $todayAmountZfb = db('order')->whereTime('callback_time', 'today')->where('paytype', 'lfali')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfb = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'lfali')->where('pay_status', 1)->sum('amount');

        // 计算总计金额（在格式化之前）
        $hourAmountTotal = $hourAmountWx + $hourAmountZfb;
        $todayAmountTotal = $todayAmountWx + $todayAmountZfb;
        $yesterdayAmountTotal = $yesterdayAmountWx + $yesterdayAmountZfb;

        // 格式化显示金额
        $hourAmountWx = number_format($hourAmountWx, 2);
        $todayAmountWx = number_format($todayAmountWx, 2);
        $yesterdayAmountWx = number_format($yesterdayAmountWx, 2);

        $hourAmountZfb = number_format($hourAmountZfb, 2);
        $todayAmountZfb = number_format($todayAmountZfb, 2);
        $yesterdayAmountZfb = number_format($yesterdayAmountZfb, 2);

        $hourAmountTotal = number_format($hourAmountTotal, 2);
        $todayAmountTotal = number_format($todayAmountTotal, 2);
        $yesterdayAmountTotal = number_format($yesterdayAmountTotal, 2);

        $botToken = '7517940946:AAHSQOj-cPFZ87o2EPp1hrRqYX8pRlqr810';
        $chatId = -4909053025;

        $message = urlencode("
<b>⏰来自定时推送</b>
<b>———" . date('Y-m-d H:i:s', time()) . "———</b>
<b>通道类型：总计金额</b>
<b>小时金额：" . $hourAmountTotal . "</b>
<b>今日金额：" . $todayAmountTotal . "</b>
<b>昨日金额：" . $yesterdayAmountTotal . "</b>
<b>————————————</b>");
        $url = "https://api.telegram.org/bot" . $botToken . "/sendMessage?chat_id=" . $chatId . "&text=" . $message . "&parse_mode=HTML";
        Http::get($url);
        echo 'ok';
    }

    public function webHook()
    {
        $data = file_get_contents('php://input');
        $data = json_decode($data, true);
        if (isset($data['message']) && isset($data['update_id'])) {
            $message = $data['message'];
            $chatId = $message['chat']['id'];
            $text = $message['text'] ?? '';
            switch ($text) {
                case '/cx_ts':
                    // 获取 微信 最近一小时金额，今日金额，昨日金额
                    $hourAmountWx = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'lfwx')->where('pay_status', 1)->sum('amount');
                    $todayAmountWx = db('order')->whereTime('callback_time', 'today')->where('paytype', 'lfwx')->where('pay_status', 1)->sum('amount');
                    $yesterdayAmountWx = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'lfwx')->where('pay_status', 1)->sum('amount');

                    // 获取 支付宝 最近一小时金额，今日金额，昨日金额
                    $hourAmountZfb = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'lfali')->where('pay_status', 1)->sum('amount');
                    $todayAmountZfb = db('order')->whereTime('callback_time', 'today')->where('paytype', 'lfali')->where('pay_status', 1)->sum('amount');
                    $yesterdayAmountZfb = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'lfali')->where('pay_status', 1)->sum('amount');

                    // 计算总计金额（在格式化之前）
                    $hourAmountTotal = $hourAmountWx + $hourAmountZfb;
                    $todayAmountTotal = $todayAmountWx + $todayAmountZfb;
                    $yesterdayAmountTotal = $yesterdayAmountWx + $yesterdayAmountZfb;

                    // 格式化显示金额
                    $hourAmountWx = number_format($hourAmountWx, 2);
                    $todayAmountWx = number_format($todayAmountWx, 2);
                    $yesterdayAmountWx = number_format($yesterdayAmountWx, 2);

                    $hourAmountZfb = number_format($hourAmountZfb, 2);
                    $todayAmountZfb = number_format($todayAmountZfb, 2);
                    $yesterdayAmountZfb = number_format($yesterdayAmountZfb, 2);

                    $hourAmountTotal = number_format($hourAmountTotal, 2);
                    $todayAmountTotal = number_format($todayAmountTotal, 2);
                    $yesterdayAmountTotal = number_format($yesterdayAmountTotal, 2);

                    $message = urlencode("
<b>⏰来自定时推送</b>
<b>———" . date('Y-m-d H:i:s', time()) . "———</b>
<b>通道类型：总计金额</b>
<b>小时金额：" . $hourAmountTotal . "</b>
<b>今日金额：" . $todayAmountTotal . "</b>
<b>昨日金额：" . $yesterdayAmountTotal . "</b>
<b>————————————</b>");
                    $this->sendMessage($chatId, $message);
                    break;
                case '/id':
                    $this->sendMessage($chatId, "id: " . $chatId);
                    break;
                default:
                    // $this->sendMessage($chatId, "default");
                    break;
            }
        }
    }

    public function messageTasks()
    {
        // http://yy.shuangzi666.com/index/telegram/messageTasks?hourAmountWx=100&todayAmountWx=200&yesterdayAmountWx=300&hourAmountZfb=400&todayAmountZfb=500&yesterdayAmountZfb=600
        $params = input();

        // 获取 微信 最近一小时金额，今日金额，昨日金额
        $hourAmountWx = $params['hourAmountWx'];
        $todayAmountWx = $params['todayAmountWx'];
        $yesterdayAmountWx = $params['yesterdayAmountWx'];

        // 获取 支付宝 最近一小时金额，今日金额，昨日金额
        $hourAmountZfb = $params['hourAmountZfb'];
        $todayAmountZfb = $params['todayAmountZfb'];
        $yesterdayAmountZfb = $params['yesterdayAmountZfb'];

        // 计算总计金额（在格式化之前）
        $hourAmountTotal = $hourAmountWx + $hourAmountZfb;
        $todayAmountTotal = $todayAmountWx + $todayAmountZfb;
        $yesterdayAmountTotal = $yesterdayAmountWx + $yesterdayAmountZfb;

        // 格式化显示金额
        $hourAmountWx = number_format($hourAmountWx, 2);
        $todayAmountWx = number_format($todayAmountWx, 2);
        $yesterdayAmountWx = number_format($yesterdayAmountWx, 2);

        $hourAmountZfb = number_format($hourAmountZfb, 2);
        $todayAmountZfb = number_format($todayAmountZfb, 2);
        $yesterdayAmountZfb = number_format($yesterdayAmountZfb, 2);

        $hourAmountTotal = number_format($hourAmountTotal, 2);
        $todayAmountTotal = number_format($todayAmountTotal, 2);
        $yesterdayAmountTotal = number_format($yesterdayAmountTotal, 2);

        $botToken = '7517940946:AAHSQOj-cPFZ87o2EPp1hrRqYX8pRlqr810';
        $chatId = -4909053025;

        $message = urlencode("
<b>⏰来自定时推送</b>
<b>———" . date('Y-m-d H:i:s', time()) . "———</b>
<b>通道类型：总计金额</b>
<b>小时金额：" . $hourAmountTotal . "</b>
<b>今日金额：" . $todayAmountTotal . "</b>
<b>昨日金额：" . $yesterdayAmountTotal . "</b>
<b>————————————</b>");
        $url = "https://api.telegram.org/bot" . $botToken . "/sendMessage?chat_id=" . $chatId . "&text=" . $message . "&parse_mode=HTML";
        Http::get($url);
        echo 'ok';
    }

    protected function sendMessage($chatId, $message)
    {
        $botToken = '7517940946:AAHSQOj-cPFZ87o2EPp1hrRqYX8pRlqr810';
        $url = "https://api.telegram.org/bot$botToken/sendMessage?chat_id=$chatId&parse_mode=HTML&text=$message";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        curl_close($ch);
    }
}
