<?php

namespace app\common\model;

use think\Db;
use think\Model;
use think\Validate;


/**
 * CK池模型
 */
class CookiePool extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
    ];

    public function getCreatetimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    // public function getPulltimeAttr($value)
    // {
    //     return date('Y-m-d H:i:s', $value);
    // }

}
