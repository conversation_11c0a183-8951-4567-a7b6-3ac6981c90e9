(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-789c58c1"],{"0ccb":function(t,e,a){var n=a("50c4"),r=a("1148"),o=a("1d80"),i=Math.ceil,l=function(t){return function(e,a,l){var s,c,u=String(o(e)),d=u.length,f=void 0===l?" ":String(l),m=n(a);return m<=d||""==f?u:(s=m-d,c=r.call(f,i(s/f.length)),c.length>s&&(c=c.slice(0,s)),t?u+c:c+u)}};t.exports={start:l(!1),end:l(!0)}},1148:function(t,e,a){"use strict";var n=a("a691"),r=a("1d80");t.exports="".repeat||function(t){var e=String(r(this)),a="",o=n(t);if(o<0||o==1/0)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(e+=e))1&o&&(a+=e);return a}},"3e1f":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"收款账号"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"备注"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.remark,callback:function(e){t.$set(t.listQuery,"remark",e)},expression:"listQuery.remark"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"创建人"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.nickname,callback:function(e){t.$set(t.listQuery,"nickname",e)},expression:"listQuery.nickname"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}},[a("span",{style:{color:e.key?"#13ce66":"#ff4949"}},[t._v(t._s(e.label))])])})),1),a("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"是否异常",clearable:""},model:{value:t.listQuery.hasError,callback:function(e){t.$set(t.listQuery,"hasError",e)},expression:"listQuery.hasError"}},t._l(t.errorOptions,(function(t){return a("el-option",{key:t.key,attrs:{label:t.label,value:t.key}})})),1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary"},on:{click:t.handleFilter}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:t.handleAdd}},[t._v(" 新增 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"warning",loading:t.exportLoading},on:{click:t.handleExport}},[t._v(" 导出 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"compact-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"","highlight-current-row":"",size:"mini"}},[a("el-table-column",{attrs:{type:"selection",width:"40",align:"center"}}),a("el-table-column",{attrs:{prop:"id",label:"ID",align:"center",width:"60"}}),a("el-table-column",{attrs:{prop:"name",label:"收款账号","min-width":"100",align:"center"}}),a("el-table-column",{attrs:{prop:"diamond",label:"号内余额",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"diamond-amount"},[t._v(t._s(e.row.diamond||0))])]}}])}),a("el-table-column",{attrs:{label:"收款统计",align:"center"}},[a("el-table-column",{attrs:{prop:"todayAmount",label:"今日收款",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"today-amount"},[t._v(t._s(t.formatAmount(e.row.todayAmount)))]),a("br"),a("span",{staticClass:"today-count"},[t._v(t._s(e.row.todayCount)+"笔")])]}}])}),a("el-table-column",{attrs:{prop:"yesterdayAmount",label:"昨日收款",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-text"},[t._v(t._s(t.formatAmount(e.row.yesterdayAmount)))]),a("br"),a("span",{staticClass:"amount-text"},[t._v(t._s(e.row.yesterdayCount)+"笔")])]}}])}),a("el-table-column",{attrs:{prop:"beforeAmount",label:"前日收款",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-text"},[t._v(t._s(t.formatAmount(e.row.beforeAmount)))]),a("br"),a("span",{staticClass:"amount-text"},[t._v(t._s(e.row.beforeCount)+"笔")])]}}])})],1),a("el-table-column",{attrs:{prop:"status",label:"状态",width:"70",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{prop:"errorMsg",label:"异常信息","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{class:{"error-msg":e.row.errormsg}},[t._v(t._s(e.row.errormsg||"-"))])]}}])}),a("el-table-column",{attrs:{prop:"remark",label:"备注","min-width":"100",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"nickname",label:"创建人","min-width":"90",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"140",align:"center","class-name":"time-column"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatTime(e.row.createtime))+" ")]}}])}),a("el-table-column",{attrs:{prop:"requestTime",label:"请求时间","min-width":"140",align:"center","class-name":"time-column"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatTime(e.row.pulltime))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.handleEdit(e.row)}}},[t._v(" 编辑 ")]),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(" 删除 ")])]}}])})],1),a("div",{staticClass:"pagination-container"},[a("el-pagination",{staticClass:"compact-pagination",attrs:{background:"","current-page":t.currentPage,"page-sizes":[10,20,30,50],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total,small:""},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),a("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleDialogClose}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px",size:"small"}},[a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:t.form.phone,callback:function(e){t.$set(t.form,"phone",e)},expression:"form.phone"}})],1),a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入密码",type:"password"},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",loading:t.loginLoading},on:{click:t.handleLogin}},[t._v("登录")])],1),a("el-form-item",{attrs:{label:"收款账号",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入收款账号"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"Cookie",prop:"cookie"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入cookie"},model:{value:t.form.cookie,callback:function(e){t.$set(t.form,"cookie",e)},expression:"form.cookie"}})],1),a("el-form-item",{attrs:{label:"状态"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949"},model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}})],1),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入备注信息"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")])],1)],1)],1)},r=[],o=a("5530"),i=(a("99af"),a("7db0"),a("d81d"),a("b0c0"),a("b680"),a("d3b7"),a("25f0"),a("4d90"),a("0643"),a("fffc"),a("a573"),a("b775"));function l(t){return Object(i["a"])({url:"/account/list",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/account/add",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/account/delete",method:"post",data:{id:t}})}function u(t){return Object(i["a"])({url:"/account/update",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/account/status",method:"post",data:t})}function f(t){return Object(i["a"])({url:"/account/detail",method:"get",params:{id:t}})}function m(t){return Object(i["a"])({url:"/account/export",method:"get",params:t})}function p(t){return Object(i["a"])({url:"/account/login",method:"post",data:t})}var h={name:"Account",data:function(){return{listQuery:{name:"",remark:"",nickname:"",status:"",hasError:"",page:1,limit:10},statusOptions:[{key:1,label:"开启"},{key:0,label:"禁用"}],errorOptions:[{key:1,label:"是"},{key:0,label:"否"}],tableData:[],currentPage:1,pageSize:10,total:0,loading:!1,dialogVisible:!1,dialogTitle:"",form:{name:"",phone:"",password:"",status:1,remark:"",cookie:""},loginLoading:!1,rules:{name:[{required:!0,message:"请输入收款账号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],cookie:[{required:!0,message:"请输入cookie",trigger:"blur"}]},exportLoading:!1}},created:function(){this.getList()},methods:{formatTime:function(t){if(!t)return"-";var e=new Date(t),a=e.getFullYear(),n=(e.getMonth()+1).toString().padStart(2,"0"),r=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),i=e.getMinutes().toString().padStart(2,"0"),l=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(n,"-").concat(r," ").concat(o,":").concat(i,":").concat(l)},formatAmount:function(t){if(void 0===t||null===t)return"¥0.00";var e=parseFloat(t);return isNaN(e)?"¥0.00":"¥".concat(e.toFixed(2))},getList:function(){var t=this;this.loading=!0;var e=Object(o["a"])({},this.listQuery);""!==e.hasError&&void 0!==e.hasError&&(e.hasErrorMsg=1===e.hasError),delete e.hasError,l(e).then((function(e){var a=e.data,n=a.list,r=a.total,i=a.page,l=a.limit;t.tableData=n.map((function(t){var e=Object(o["a"])(Object(o["a"])({},t),{},{status:1===t.status,todayAmount:t.today_amount,yesterdayAmount:t.yesterday_amount,beforeAmount:t.before_amount,beforeCount:t.before_count,todayCount:t.today_count,yesterdayCount:t.yesterday_count,diamond:t.diamond||0});return e})),t.total=r,t.currentPage=i,t.pageSize=l,t.loading=!1}))},mapPayType:function(t){return t},mapPayTypeToBackend:function(t){return t},handleFilter:function(){this.listQuery.page=1,this.getList()},handleAdd:function(){this.dialogTitle="新增账号",this.dialogVisible=!0,this.resetTemp()},handleEdit:function(t){var e=this;this.dialogTitle="编辑账号",f(t.id).then((function(t){var a,n,r,i=t.data;e.form=Object(o["a"])(Object(o["a"])({},i),{},{status:1===i.status?1:0,cookie:(null===(a=i.config)||void 0===a?void 0:a.cookie)||"",phone:(null===(n=i.config)||void 0===n?void 0:n.phone)||"",password:(null===(r=i.config)||void 0===r?void 0:r.password)||""}),e.dialogVisible=!0}))},resetTemp:function(){this.form={name:"",phone:"",password:"",status:1,remark:"",cookie:""}},handleDelete:function(t){var e=this;this.$confirm("确认删除该账号吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c(t.id).then((function(t){e.$message.success("删除成功"),e.getList()}))})).catch((function(){e.$message.info("已取消删除")}))},handleSizeChange:function(t){this.pageSize=t,this.listQuery.limit=t,this.getList()},handleCurrentChange:function(t){this.currentPage=t,this.listQuery.page=t,this.getList()},handleFormSubmit:function(t){var e=this,a=Object(o["a"])({},t);delete a.payType,"新增账号"===this.dialogTitle?s(a).then((function(t){e.$message.success("新增成功"),e.dialogVisible=!1,e.getList()})):u(a).then((function(t){e.$message.success("编辑成功"),e.dialogVisible=!1,e.getList()}))},handleStatusChange:function(t){var e=this,a=t.status?1:0;d({id:t.id,status:a}).then((function(t){e.$message.success("状态已".concat(1===a?"开启":"禁用"))}))},getPayTypeTagType:function(t){var e=this.payTypeOptions.find((function(e){return e.key===t}));return e?e.tagType:"info"},getPayTypeLabel:function(t){var e=this.payTypeOptions.find((function(e){return e.key===t})),a=e?e.label:t;return a},handleDialogClose:function(){this.resetTemp()},submitForm:function(){var t=this;this.$refs.form.validate((function(e){e?t.handleFormSubmit(t.form):t.$message.error("表单验证失败")}))},handleExport:function(){var t=this;this.$confirm("确认导出当前筛选条件下的账号列表吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){t.exportLoading=!0;var e=Object(o["a"])({},t.listQuery);""!==e.hasError&&void 0!==e.hasError&&(e.hasErrorMsg=1===e.hasError),delete e.hasError,delete e.page,delete e.limit,m(e).then((function(e){if(1===e.code&&e.data){var a=e.data,n=a.download_url,r=a.filename,o=a.total_count,i=document.createElement("a");i.href=n,i.download=r,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),t.$message.success("导出成功，共导出 ".concat(o," 条记录"))}else t.$message.error(e.msg||"导出失败");t.exportLoading=!1})).catch((function(e){console.error("导出失败:",e),t.$message.error("导出失败"),t.exportLoading=!1}))})).catch((function(){t.$message.info("已取消导出")}))},handleLogin:function(){var t=this;this.form.phone&&this.form.password?(this.loginLoading=!0,p({phone:this.form.phone,password:this.form.password}).then((function(e){1===e.code&&e.data?(t.form.name=e.data.userid||"",t.form.cookie=e.data.cookie||"",t.$message.success("登录成功，账号信息已自动填充")):t.$message.error(e.msg||"登录失败")})).catch((function(){t.$message.error("登录接口异常")})).finally((function(){t.loginLoading=!1}))):this.$message.error("请先输入手机号和密码")}}},g=h,b=(a("4e1d"),a("2877")),y=Object(b["a"])(g,n,r,!1,null,"7e7151dd",null);e["default"]=y.exports},"408a":function(t,e,a){var n=a("c6b6");t.exports=function(t){if("number"!=typeof t&&"Number"!=n(t))throw TypeError("Incorrect invocation");return+t}},"4d90":function(t,e,a){"use strict";var n=a("23e7"),r=a("0ccb").start,o=a("9a0c");n({target:"String",proto:!0,forced:o},{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},"4e1d":function(t,e,a){"use strict";a("e8e6")},"7db0":function(t,e,a){"use strict";var n=a("23e7"),r=a("b727").find,o=a("44d2"),i=a("ae40"),l="find",s=!0,c=i(l);l in[]&&Array(1)[l]((function(){s=!1})),n({target:"Array",proto:!0,forced:s||!c},{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),o(l)},"9a0c":function(t,e,a){var n=a("342f");t.exports=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(n)},b680:function(t,e,a){"use strict";var n=a("23e7"),r=a("a691"),o=a("408a"),i=a("1148"),l=a("d039"),s=1..toFixed,c=Math.floor,u=function(t,e,a){return 0===e?a:e%2===1?u(t,e-1,a*t):u(t*t,e/2,a)},d=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},f=s&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!l((function(){s.call({})}));n({target:"Number",proto:!0,forced:f},{toFixed:function(t){var e,a,n,l,s=o(this),f=r(t),m=[0,0,0,0,0,0],p="",h="0",g=function(t,e){var a=-1,n=e;while(++a<6)n+=t*m[a],m[a]=n%1e7,n=c(n/1e7)},b=function(t){var e=6,a=0;while(--e>=0)a+=m[e],m[e]=c(a/t),a=a%t*1e7},y=function(){var t=6,e="";while(--t>=0)if(""!==e||0===t||0!==m[t]){var a=String(m[t]);e=""===e?a:e+i.call("0",7-a.length)+a}return e};if(f<0||f>20)throw RangeError("Incorrect fraction digits");if(s!=s)return"NaN";if(s<=-1e21||s>=1e21)return String(s);if(s<0&&(p="-",s=-s),s>1e-21)if(e=d(s*u(2,69,1))-69,a=e<0?s*u(2,-e,1):s/u(2,e,1),a*=4503599627370496,e=52-e,e>0){g(0,a),n=f;while(n>=7)g(1e7,0),n-=7;g(u(10,n,1),0),n=e-1;while(n>=23)b(1<<23),n-=23;b(1<<n),g(1,1),b(2),h=y()}else g(0,a),g(1<<-e,0),h=y()+i.call("0",f);return f>0?(l=h.length,h=p+(l<=f?"0."+i.call("0",f-l)+h:h.slice(0,l-f)+"."+h.slice(l-f))):h=p+h,h}})},e8e6:function(t,e,a){},fffc:function(t,e,a){"use strict";var n=a("23e7"),r=a("2266"),o=a("1c0b"),i=a("825a");n({target:"Iterator",proto:!0,real:!0},{find:function(t){return i(this),o(t),r(this,(function(e){if(t(e))return r.stop(e)}),void 0,!1,!0).result}})}}]);