<?php

class Proxy
{
    public static $proxyKey = "i9k23v8j";
    public static $proxyUsername = "ktjcht";
    public static $proxyPassword = "nckqytb2";

    // 新增：服务器列表
    public static $proxyServers = [
        '*************',
        '************'
    ];

    // 新增：随机获取一台服务器
    public static function getProxyServer()
    {
        $servers = self::$proxyServers;
        return $servers[array_rand($servers)];
    }

    public static function getCityCode3($ip)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => "http://" . self::getProxyServer() . ":3000/proxy.php?target_url=" . urlencode('https://ip9.com.cn/get?ip=' . $ip),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 3,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'cache-control: max-age=0',
                'sec-ch-ua: "Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "Windows"',
                'upgrade-insecure-requests: 1',
                'sec-fetch-site: none',
                'sec-fetch-mode: navigate',
                'sec-fetch-user: ?1',
                'sec-fetch-dest: document',
                'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'priority: u=0, i',
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return '';
        }
        try {
            $cityCode = json_decode(utf8_decode(trim($response)))->data->post_code;
            return $cityCode;
        } catch (Exception $e) {
            // trace('getCityCode3 error:'. $response, 'error');
            trace('getCityCode3:' . $e->getMessage(), 'error');
            return '';
        }
    }

    public static function getCityCode4($ip)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => "http://" . self::getProxyServer() . ":3000/proxy.php?target_url=" . urlencode('https://api.vore.top/api/IPdata?ip=' . $ip),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 3,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'cache-control: max-age=0',
                'sec-ch-ua: "Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "Windows"',
                'upgrade-insecure-requests: 1',
                'sec-fetch-site: none',
                'sec-fetch-mode: navigate',
                'sec-fetch-user: ?1',
                'sec-fetch-dest: document',
                'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'priority: u=0, i',
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return '';
        }
        try {
            $cityCode = json_decode(utf8_decode(trim($response)))->adcode->a;
            return $cityCode;
        } catch (Exception $e) {
            // trace('getCityCode4 error:'. $response, 'error');
            trace('getCityCode4:' . $e->getMessage(), 'error');
            return '';
        }
    }

    /**
     * 获取地区编码
     */
    public static function getRegionCode($clientIp)
    {
        $regionCode = self::getCityCode4($clientIp);
        if (empty($regionCode)) {
            $regionCode = self::getCityCode3($clientIp);
            if (empty($regionCode)) {
                $regionCode = "";
            }
        }
        return $regionCode;
    }

    /**
     * 获取代理IP
     * @param string $key
     * @param int $count
     * @param string $area
     * @return string|false
     */
    public static function getProxyByShenLong($count = 1, $area = "")
    {
        try {
            $url = "http://api.shenlongip.com/ip?key=" . self::$proxyKey . "&count=" . $count . "&pattern=json&isp=&protocol=1&mr=2&area=" . $area;
            $response = file_get_contents("http://" . self::getProxyServer() . ":3000/proxy.php?target_url=" . urlencode($url), false, stream_context_create([
                'http' => [
                    'timeout' => 3, // 设置超时时间
                ]
            ]));
            if ($response) {
                $jsonData = json_decode($response, false);
                if (empty($jsonData) || empty($jsonData->data) || empty($jsonData->data[0]->ip) || empty($jsonData->data[0]->port)) {
                    return false;
                }
                return $jsonData->data[0]->ip . ":" . $jsonData->data[0]->port;
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
