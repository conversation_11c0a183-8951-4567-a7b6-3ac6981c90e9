(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c24e2a6"],{1148:function(t,e,a){"use strict";var n=a("a691"),r=a("1d80");t.exports="".repeat||function(t){var e=String(r(this)),a="",i=n(t);if(i<0||i==1/0)throw RangeError("Wrong number of repetitions");for(;i>0;(i>>>=1)&&(e+=e))1&i&&(a+=e);return a}},2570:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"商户名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(t){return a("el-option",{key:t.key,attrs:{label:t.label,value:t.key}})})),1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:t.handleAdd}},[t._v(" 新增 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{prop:"id",label:"商户ID",width:"80",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"商户名称","min-width":"120",align:"center"}}),a("el-table-column",{attrs:{prop:"key",label:"密钥","min-width":"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tooltip",{attrs:{effect:"dark",content:e.row.key,placement:"top"}},[a("span",{staticClass:"secret-key"},[t._v(t._s(t._f("formatKey")(e.row.key)))])]),a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-copy-document"},on:{click:function(a){return t.copyKey(e.row.key)}}})]}}])}),a("el-table-column",{attrs:{prop:"today_amount",label:"今日收款","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-value"},[t._v(t._s(t._f("formatMoney")(e.row.today_amount)))])]}}])}),a("el-table-column",{attrs:{prop:"yesterday_amount",label:"昨日收款","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-value"},[t._v(t._s(t._f("formatMoney")(e.row.yesterday_amount)))])]}}])}),a("el-table-column",{attrs:{prop:"total_amount",label:"总收款","min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-value"},[t._v(t._s(t._f("formatMoney")(e.row.total_amount)))])]}}])}),a("el-table-column",{attrs:{prop:"status",label:"状态","min-width":"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{prop:"remark",label:"备注","min-width":"150",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"操作","min-width":"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(a){return t.handleEdit(e.row)}}},[t._v(" 编辑 ")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(" 删除 ")])]}}])})],1),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{background:"","current-page":t.currentPage,"page-sizes":[10,20,30,50],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),a("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"500px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{model:t.temp,"label-position":"right","label-width":"100px",rules:t.rules}},[a("el-form-item",{attrs:{label:"商户名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入商户名称"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"密钥",prop:"key"}},[a("el-input",{attrs:{placeholder:"请输入密钥"},model:{value:t.temp.key,callback:function(e){t.$set(t.temp,"key",e)},expression:"temp.key"}},[a("el-button",{attrs:{slot:"append"},on:{click:t.generateKey},slot:"append"},[t._v("生成")])],1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:t.temp.status,callback:function(e){t.$set(t.temp,"status",e)},expression:"temp.status"}},[a("el-radio",{attrs:{label:1}},[t._v("开启")]),a("el-radio",{attrs:{label:0}},[t._v("关闭")])],1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入备注信息"},model:{value:t.temp.remark,callback:function(e){t.$set(t.temp,"remark",e)},expression:"temp.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.saveData}},[t._v("确 定")])],1)],1)],1)},r=[],i=a("c7eb"),l=a("5530"),s=a("1da1"),o=(a("99af"),a("d81d"),a("b0c0"),a("b680"),a("d3b7"),a("a573"),a("b775"));function c(t){return Object(o["a"])({url:"/merchant/list",method:"get",params:t})}function u(t){return Object(o["a"])({url:"/merchant/add",method:"post",data:t})}function d(t){return Object(o["a"])({url:"/merchant/update",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/merchant/delete",method:"post",data:t})}function m(t){return Object(o["a"])({url:"/merchant/status",method:"post",data:t})}var f={name:"Merchant",filters:{formatStatus:function(t){var e={1:"开启",0:"关闭"};return e[t]||t},formatKey:function(t){return t?t.length>10?"".concat(t.substring(0,6),"****").concat(t.substring(t.length-4)):t:"-"},formatMoney:function(t){return void 0===t||null===t?"¥0.00":"¥".concat(parseFloat(t).toFixed(2))}},data:function(){return{listQuery:{name:"",status:"",page:1,limit:10},statusOptions:[{key:1,label:"开启"},{key:0,label:"关闭"}],tableData:[],currentPage:1,pageSize:10,total:0,loading:!1,dialogVisible:!1,dialogTitle:"",temp:{name:"",key:"",status:1,remark:""},rules:{name:[{required:!0,message:"请输入商户名称",trigger:"blur"}],key:[{required:!0,message:"请输入密钥",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}}},created:function(){this.getList()},methods:{getStatusLabel:function(t){var e={1:"开启",0:"关闭"};return e[t]||t},getList:function(){var t=this;return Object(s["a"])(Object(i["a"])().mark((function e(){return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.loading=!0;try{c(t.listQuery).then((function(e){var a=e.data,n=a.list,r=a.total,i=a.page,s=a.limit;t.tableData=n.map((function(e){return Object(l["a"])(Object(l["a"])({},e),{},{statusLabel:t.getStatusLabel(e.status)})})),t.total=r,t.currentPage=i,t.pageSize=s})).finally((function(){t.loading=!1}))}catch(a){t.$message.error("获取数据失败"),console.error("Error:",a),t.loading=!1}case 2:case"end":return e.stop()}}),e)})))()},handleFilter:function(){this.listQuery.page=1,this.getList()},handleAdd:function(){var t=this;this.dialogTitle="新增商户",this.temp={name:"",key:"",status:1,remark:""},this.dialogVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},handleEdit:function(t){var e=this;this.dialogTitle="编辑商户",this.temp={id:t.id,name:t.name,key:t.key,status:t.status,remark:t.remark||""},this.dialogVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},handleDelete:function(t){var e=this;return Object(s["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,e.$confirm("确认删除该商户吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 3:p({id:t.id}).then((function(t){e.$message.success("删除成功"),e.getList()})),a.next=9;break;case 6:a.prev=6,a.t0=a["catch"](0),"cancel"!==a.t0&&(e.$message.error("删除失败"),console.error("Error:",a.t0));case 9:case"end":return a.stop()}}),a,null,[[0,6]])})))()},handleStatusChange:function(t){var e=this;return Object(s["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:m({id:t.id,status:t.status}).then((function(a){e.$message.success("商户 ".concat(t.name," 状态已更新"))}));case 1:case"end":return a.stop()}}),a)})))()},handleSizeChange:function(t){this.listQuery.limit=t,this.getList()},handleCurrentChange:function(t){this.listQuery.page=t,this.getList()},saveData:function(){var t=this;return Object(s["a"])(Object(i["a"])().mark((function e(){return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$refs["dataForm"].validate(function(){var e=Object(s["a"])(Object(i["a"])().mark((function e(a){var n,r,l;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=14;break}return e.prev=1,n="新增商户"===t.dialogTitle,r=n?u:d,e.next=6,r(t.temp);case 6:l=e.sent,1===l.code?(t.$message.success(n?"新增成功":"编辑成功"),t.dialogVisible=!1,t.getList()):t.$message.error(l.msg||(n?"新增失败":"编辑失败")),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](1),t.$message.error("新增商户"===t.dialogTitle?"新增失败":"编辑失败"),console.error("Error:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})))()},generateKey:function(){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",e="",a=32,n=0;n<a;n++)e+=t.charAt(Math.floor(Math.random()*t.length));this.temp.key=e},copyKey:function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message.success("密钥已复制到剪贴板")}}},h=f,b=(a("766b"),a("2877")),g=Object(b["a"])(h,n,r,!1,null,"5f714740",null);e["default"]=g.exports},"408a":function(t,e,a){var n=a("c6b6");t.exports=function(t){if("number"!=typeof t&&"Number"!=n(t))throw TypeError("Incorrect invocation");return+t}},"766b":function(t,e,a){"use strict";a("a365")},a365:function(t,e,a){},b680:function(t,e,a){"use strict";var n=a("23e7"),r=a("a691"),i=a("408a"),l=a("1148"),s=a("d039"),o=1..toFixed,c=Math.floor,u=function(t,e,a){return 0===e?a:e%2===1?u(t,e-1,a*t):u(t*t,e/2,a)},d=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},p=o&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!s((function(){o.call({})}));n({target:"Number",proto:!0,forced:p},{toFixed:function(t){var e,a,n,s,o=i(this),p=r(t),m=[0,0,0,0,0,0],f="",h="0",b=function(t,e){var a=-1,n=e;while(++a<6)n+=t*m[a],m[a]=n%1e7,n=c(n/1e7)},g=function(t){var e=6,a=0;while(--e>=0)a+=m[e],m[e]=c(a/t),a=a%t*1e7},y=function(){var t=6,e="";while(--t>=0)if(""!==e||0===t||0!==m[t]){var a=String(m[t]);e=""===e?a:e+l.call("0",7-a.length)+a}return e};if(p<0||p>20)throw RangeError("Incorrect fraction digits");if(o!=o)return"NaN";if(o<=-1e21||o>=1e21)return String(o);if(o<0&&(f="-",o=-o),o>1e-21)if(e=d(o*u(2,69,1))-69,a=e<0?o*u(2,-e,1):o/u(2,e,1),a*=4503599627370496,e=52-e,e>0){b(0,a),n=p;while(n>=7)b(1e7,0),n-=7;b(u(10,n,1),0),n=e-1;while(n>=23)g(1<<23),n-=23;g(1<<n),b(1,1),g(2),h=y()}else b(0,a),b(1<<-e,0),h=y()+l.call("0",p);return p>0?(s=h.length,h=f+(s<=p?"0."+l.call("0",p-s)+h:h.slice(0,s-p)+"."+h.slice(s-p))):h=f+h,h}})}}]);