<?

namespace laifeng;

class Service
{
    public static function charge($amount, $cookie, $proxy = "")
    {
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $x_proxy = $proxy ? 'X-Proxy: http://' . $proxyUsername . ":" . $proxyPassword . "@" . $proxy : '';
        $url = "http://" . \Proxy::getProxyServer() . ":3000/proxy3.php?target_url=" . base64_encode('https://yapi.laifeng.com/lftop/mtop.youku.laifeng.cashier.charge.createOrder/3.0/?itemId=445594753&channelId=6&num=' . $amount . '&rechargeRedPacketGId=0&appKey=24679788&info=%7B%22clientInfo%22%3A%7B%22appName%22%3A%22laifengPc%22%2C%22appVersion%22%3A%221.0.0%22%2C%22appKey%22%3A%2224679788%22%2C%22appId%22%3A1000%7D%7D&source=a2h55.8996835.lf_phone');
        $curl = curl_init();
        curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_COOKIE => $cookie,
        CURLOPT_HTTPHEADER => [
            'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'pragma: no-cache',
            'cache-control: no-cache',
            'origin: https://yv.laifeng.com',
            'sec-fetch-site: same-site',
            'sec-fetch-mode: cors',
            'sec-fetch-dest: empty',
            'referer: https://yv.laifeng.com/',
            'accept-language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
            'priority: u=1, i',
            $x_proxy
        ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return false;
        }
        $response = json_decode($response, true);
        return $response;
    }

    public static function pay($chargeNo, $channelId, $cookie, $proxy = "")
    {
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $x_proxy = $proxy ? 'X-Proxy: http://' . $proxyUsername . ":" . $proxyPassword . "@" . $proxy : '';
        $url = "http://" . \Proxy::getProxyServer() . ":3000/proxy3.php?target_url=" . base64_encode("https://yapi.laifeng.com/lftop/mtop.laifeng.cashier.charge.order.pay/2.0/?chargeNo={$chargeNo}&channelId={$channelId}&inWechat=false&appKey=24679788&info=%7B%22clientInfo%22%3A%7B%22appName%22%3A%22laifengPc%22%2C%22appVersion%22%3A%221.0.0%22%2C%22appKey%22%3A%2224679788%22%2C%22appId%22%3A1000%7D%7D");
        $curl = curl_init();
        curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_COOKIE => $cookie,
        CURLOPT_HTTPHEADER => [
            'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'pragma: no-cache',
            'cache-control: no-cache',
            'origin: https://zhifu.laifeng.com',
            'sec-fetch-site: same-site',
            'sec-fetch-mode: cors',
            'sec-fetch-dest: empty',
            'referer: https://zhifu.laifeng.com/',
            'accept-language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
            'priority: u=1, i',
            $x_proxy
        ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return false;
        }
        $response = json_decode($response, true);
        return $response;
    }

    public static function login($phone, $password, $proxy = "")
    {
        $password = md5($password);
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $x_proxy = $proxy ? 'X-Proxy: http://' . $proxyUsername . ":" . $proxyPassword . "@" . $proxy : '';
        $url = "http://" . \Proxy::getProxyServer() . ":3000/proxy3.php?target_url=" . base64_encode("https://pre-yapi.laifeng.com/lftop/mtop.youku.laifeng.ucenter.account.login.account/1.0/?account={$phone}&password={$password}");
        $curl = curl_init();
        curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'accept-language: zh-CN,zh;q=0.9',
            'cache-control: no-cache',
            'origin: https://yv.laifeng.com',
            'pragma: no-cache',
            'priority: u=1, i',
            'referer: https://yv.laifeng.com/',
            'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"',
            'sec-fetch-dest: empty',
            'sec-fetch-mode: cors',
            'sec-fetch-site: same-site',
            $x_proxy
        ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return false;
        }
        $response = json_decode($response, true);
        return $response;
    }

    public static function status($chargeNo, $proxy = "")
    {
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $x_proxy = $proxy ? 'X-Proxy: http://' . $proxyUsername . ":" . $proxyPassword . "@" . $proxy : '';
        $url = "http://" . \Proxy::getProxyServer() . ":3000/proxy3.php?target_url=" . base64_encode("https://yapi.laifeng.com/lftop/mtop.laifeng.cashier.charge.order.get/2.0/?chargeNo={$chargeNo}&appKey=24679788&info=%7B%22clientInfo%22%3A%7B%22appName%22%3A%22laifengPc%22%2C%22appVersion%22%3A%221.0.0%22%2C%22appKey%22%3A%2224679788%22%2C%22appId%22%3A1000%7D%7D");
        $curl = curl_init();
        curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => [
            'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'pragma: no-cache',
            'cache-control: no-cache',
            'origin: https://zhifu.laifeng.com',
            'sec-fetch-site: same-site',
            'sec-fetch-mode: cors',
            'sec-fetch-dest: empty',
            'referer: https://zhifu.laifeng.com/',
            'accept-language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
            'priority: u=1, i',
            $x_proxy
        ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return false;
        }
        $response = json_decode($response, true);
        return $response;
    }

    /**
     * 从支付响应字符串中提取完整的支付宝支付链接
     * @param string $responseString 支付响应的JSON字符串
     * @return string|false 返回完整的支付宝支付链接，失败返回false
     */
    public static function extractAlipayUrl($response)
    {
        try {

            if (!$response || !isset($response['data']['channelParams'])) {
                return false;
            }

            $channelParams = $response['data']['channelParams'];

            // 提取form表单的action URL
            if (!preg_match('/action="([^"]+)"/', $channelParams, $actionMatches)) {
                return false;
            }
            $actionUrl = html_entity_decode($actionMatches[1]);

            // 提取biz_content参数
            if (!preg_match('/name="biz_content"\s+value="([^"]+)"/', $channelParams, $bizContentMatches)) {
                return false;
            }
            $bizContent = html_entity_decode($bizContentMatches[1]);

            // URL编码biz_content参数
            $encodedBizContent = base64_encode($bizContent);

            // 构建完整的支付宝支付链接
            $separator = (strpos($actionUrl, '?') !== false) ? '&' : '?';
            $fullUrl = $actionUrl . $separator . 'biz_content=' . $encodedBizContent;

            return $fullUrl;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 从字符串中提取订单号
     * @param string $str 包含订单号的字符串，如：3Web1900000000051568421787
     * @return string|false 返回提取的订单号，失败返回false
     */
    public static function extractOrderNo($str)
    {
        // 使用正则表达式匹配连续的数字串（至少10位，通常订单号都比较长）
        if (preg_match('/\d{10,}/', $str, $matches)) {
            return $matches[0];
        }

        return false;
    }

    /**
     * 从URL中提取订单号
     * @param string $url URL地址，如：https://laifeng.qianlimalyy.online/pay/submit/2025072001203871338/
     * @return string|false 返回提取的订单号，失败返回false
     */
    public static function extractOrderNoFromUrl($url)
    {
        // 使用正则表达式匹配URL路径中的数字串（至少10位）
        if (preg_match('/\/(\d{10,})\//', $url, $matches)) {
            return $matches[1];
        }

        return false;
    }
}