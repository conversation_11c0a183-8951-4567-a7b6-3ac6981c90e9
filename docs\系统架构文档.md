# 系统架构文档

## 1. 系统概述

### 1.1 系统定位
本系统是一个基于来疯平台的支付网关系统，提供完整的支付解决方案，支持微信和支付宝两种支付方式。

### 1.2 核心特性
- **多渠道支付**: 支持来疯微信(lfwx)和来疯支付宝(lfali)
- **异步监控**: 自动监控订单支付状态，实时更新
- **智能代理**: 支持多代理池管理和智能切换
- **订单归档**: 自动归档历史数据，保持系统性能
- **高并发处理**: 支持大量并发支付请求
- **安全可靠**: 完善的签名验证和数据加密

## 2. 系统架构

### 2.1 整体架构图
```mermaid
graph TB
    subgraph "客户端层"
        A[商户系统]
        B[用户浏览器]
    end
    
    subgraph "网关层"
        C[支付网关API]
        D[收银台系统]
    end
    
    subgraph "业务层"
        E[订单管理]
        F[账号管理]
        G[商户管理]
        H[支付监控]
    end
    
    subgraph "数据层"
        I[MySQL主库]
        J[Redis缓存]
        K[订单归档]
    end
    
    subgraph "外部服务"
        L[来疯API]
        M[代理服务]
    end
    
    subgraph "后台服务"
        N[支付状态监控]
        O[订单归档服务]
        P[账号拉单服务]
    end
    
    A --> C
    B --> D
    C --> E
    D --> E
    E --> I
    F --> I
    G --> I
    H --> L
    E --> J
    H --> N
    I --> K
    O --> K
    N --> L
    P --> L
    L --> M
```

### 2.2 核心模块

#### 2.2.1 支付网关模块
- **功能**: 处理商户支付请求，创建订单
- **位置**: `application/api/controller/Gateway.php`
- **特点**: 
  - 参数验证和签名校验
  - 商户权限检查
  - 订单创建和管理

#### 2.2.2 收银台模块
- **功能**: 为用户提供支付页面
- **位置**: `application/index/controller/Cashier.php`
- **特点**:
  - 支付链接获取
  - 缓存锁防重复处理
  - 智能账号选择

#### 2.2.3 账号管理模块
- **功能**: 管理来疯收款账号
- **位置**: `application/api/controller/Account.php`
- **特点**:
  - 账号登录和配置
  - 收款状态监控
  - 统计数据展示

#### 2.2.4 订单管理模块
- **功能**: 订单查询和管理
- **位置**: `application/api/controller/Order.php`
- **特点**:
  - 主表和归档表联合查询
  - 订单状态管理
  - 数据导出功能

## 3. 核心服务

### 3.1 来疯支付服务
- **位置**: `extend/laifeng/`
- **核心类**:
  - `Pay.php`: 支付订单创建
  - `Service.php`: 基础API调用和工具方法

#### 3.1.1 主要方法
```php
// 创建支付订单
Pay::createOrder($amount, $cookie, $chcode, $proxy)

// 账号登录
Pay::accountLogin($phone, $password, $proxy)

// 提取支付宝链接
Service::extractAlipayUrl($responseString)

// 提取订单号
Service::extractOrderNo($str)
Service::extractOrderNoFromUrl($url)

// 查询支付状态
Service::status($chargeNo, $proxy)
```

### 3.2 异步监控服务
- **位置**: `application/common/command/OrderPaymentCheck.php`
- **功能**: 自动监控订单支付状态

#### 3.2.1 工作流程
1. 每5秒执行一次检查
2. 查询近5分钟的未支付订单
3. 异步并发查询支付状态（最大100并发）
4. 更新支付成功的订单
5. 执行商户回调通知
6. 统计执行性能

#### 3.2.2 性能特点
- 使用curl_multi实现异步并发
- 支持代理池轮换
- 详细的性能统计
- 完善的错误处理

### 3.3 订单归档服务
- **位置**: `application/common/command/ArchiveOrder.php`
- **功能**: 自动归档历史订单数据

#### 3.3.1 归档策略
- 归档条件：创建时间超过30天
- 执行时间：每天凌晨2点
- 批量处理：每批1000条
- 数据验证：确保迁移完整性

## 4. 数据流转

### 4.1 支付流程数据流
```mermaid
sequenceDiagram
    participant M as 商户
    participant G as 网关
    participant C as 收银台
    participant L as 来疯API
    participant D as 数据库
    participant S as 监控服务
    
    M->>G: 创建订单请求
    G->>D: 保存订单(未支付)
    G->>M: 返回收银台链接
    M->>C: 跳转收银台
    C->>L: 调用来疯API
    L->>C: 返回支付链接
    C->>D: 更新支付链接
    C->>M: 跳转支付页面
    
    loop 监控循环
        S->>D: 查询未支付订单
        S->>L: 批量查询支付状态
        L->>S: 返回支付状态
        S->>D: 更新订单状态
        S->>M: 发送回调通知
    end
```

### 4.2 数据存储策略
- **主表**: 存储近期活跃订单（30天内）
- **归档表**: 存储历史订单数据
- **缓存**: Redis存储临时数据和锁
- **索引**: 优化查询性能的复合索引

## 5. 安全机制

### 5.1 签名验证
- 使用MD5算法进行签名
- 参数按字典序排序
- 商户密钥参与签名计算

### 5.2 访问控制
- 基于角色的权限管理
- API接口权限验证
- 数据隔离（用户只能访问自己的数据）

### 5.3 数据安全
- 敏感信息加密存储
- 数据库连接加密
- 定期备份和恢复测试

## 6. 性能优化

### 6.1 数据库优化
- 合理的索引设计
- 读写分离
- 连接池管理
- 慢查询监控

### 6.2 缓存策略
- Redis缓存热点数据
- 分布式锁防止并发问题
- 缓存预热和更新策略

### 6.3 异步处理
- 支付状态异步监控
- 回调通知异步处理
- 批量数据处理

## 7. 监控和运维

### 7.1 系统监控
- 订单支付成功率
- 接口响应时间
- 系统资源使用率
- 错误日志监控

### 7.2 业务监控
- 账号可用性监控
- 代理服务状态
- 支付渠道状态
- 商户回调成功率

### 7.3 告警机制
- 支付失败率告警
- 系统异常告警
- 资源使用告警
- 业务指标告警

## 8. 扩展性设计

### 8.1 水平扩展
- 支持多实例部署
- 负载均衡配置
- 数据库分片策略

### 8.2 功能扩展
- 新支付渠道接入
- 新业务模块添加
- 第三方服务集成

### 8.3 技术升级
- 框架版本升级路径
- 数据库版本兼容
- 缓存系统升级
