# 抖音项目后端

## 账号管理接口

### 账号列表接口
- **接口地址**: `/api/account/list`
- **请求方式**: GET
- **功能说明**: 获取账号列表，支持分页和多种筛选条件
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
  - `name`: 账号名称（模糊搜索）
  - `payType`: 支付类型
  - `status`: 状态
  - `remark`: 备注（模糊搜索）
  - `nickname`: 用户昵称（模糊搜索）
  - `hasErrorMsg`: 是否有错误信息（true/false）

    ### 账号导出接口
    - **接口地址**: `/api/account/export`
    - **请求方式**: GET
    - **功能说明**: 导出账号列表为CSV格式文件
    - **请求参数**:
    - `page`: 页码（默认1，导出时忽略）
    - `limit`: 每页数量（默认10，导出时忽略）
    - `name`: 账号名称（模糊搜索）
    - `payType`: 支付类型
    - `status`: 状态
    - `remark`: 备注（模糊搜索）
    - `nickname`: 用户昵称（模糊搜索）
    - `hasErrorMsg`: 是否有错误信息（true/false）
- **返回数据**:
  - `download_url`: 文件下载链接
  - `filename`: 文件名
  - `total_count`: 导出数据总数
- **导出字段**:
  - ID、账号名称、支付类型、状态、钻石数量、错误信息、备注
  - 创建时间、拉取时间、用户昵称
  - 今日订单数、昨日订单数、前日订单数
  - 今日金额、昨日金额、前日金额
- **权限说明**: 
  - 管理员可导出所有账号
  - 普通用户只能导出自己的账号
- **文件格式**: CSV格式，支持中文，自动添加BOM头解决乱码问题
- **文件管理**: 
  - 导出文件保存在 `public/uploads/export/` 目录
  - 自动清理7天前的导出文件
  - 文件名格式：`账号列表_YYYY-MM-DD_HH-mm-ss.csv`
- **错误处理**:
  - 无数据时返回错误提示
  - 文件创建失败时返回详细错误信息
  - 所有错误都会记录到日志文件
- **使用示例**:
  ```bash
  # 导出所有账号
  GET /api/account/export
  
  # 导出指定状态的账号
  GET /api/account/export?status=1
  
  # 导出包含特定关键词的账号
  GET /api/account/export?name=测试&remark=重要
  
  # 导出指定页的账号（page和limit参数会被忽略，导出全部数据）
  GET /api/account/export?page=1&limit=10&status=1
  ```

## 定时任务系统使用说明

本项目实现了一个简单的定时任务系统，不依赖数据库，可以方便地执行各种定期任务。

### 定时任务命令

定时任务命令类位于 `application/common/command/Cron.php`，已经预设了一些常用的定时任务：

- 每分钟执行：清理临时缓存
- 每小时执行：处理超时订单
- 每天执行：清理日志文件、生成站点统计报告
- 每周执行：清理过期备份文件
- 每月执行：生成月度报表

### 使用方法

#### 方法一：命令行手动执行

```bash
# 执行所有定时任务
php think cron

# 执行指定任务
php think cron --task=cleanCache
php think cron --task=hourly
php think cron --task=daily
php think cron --task=weekly
php think cron --task=monthly
```

#### 方法二：通过独立脚本执行

项目根目录下的 `cron.php` 是一个独立的脚本文件，可以直接通过PHP执行：

```bash
php cron.php
```

#### 方法三：设置系统定时任务（推荐）

在Linux/Unix系统中，可以通过crontab设置系统定时任务：

```bash
# 编辑crontab
crontab -e

# 添加以下内容（每分钟执行一次）
* * * * * /usr/bin/php /path/to/your/project/cron.php >> /path/to/your/project/runtime/log/cron.log 2>&1
```

在Windows系统中，可以通过计划任务（Task Scheduler）设置定时任务。

### 自定义定时任务

如果需要添加新的定时任务，只需在 `Cron.php` 文件中添加新的任务方法，并在 `runAllTasks()` 方法中调用即可。

例如，添加一个每天中午12点执行的任务：

```php
/**
 * 每天中午执行的任务
 * @param Output $output
 */
protected function taskNoon(Output $output)
{
    $output->writeln("执行任务: 每天中午任务");
    
    // 添加任务逻辑
    
    $output->writeln("每天中午任务执行完成");
}
```

然后在 `runAllTasks()` 方法中添加调用：

```php
// 每天中午12点执行的任务
if ($hour == 12 && $minute == 0) {
    $this->taskNoon($output);
}
```

### 注意事项

1. 定时任务执行时间不要过长，避免多个任务同时执行导致系统负载过高
2. 对于耗时较长的任务，建议使用队列系统处理
3. 定时任务日志保存在 `runtime/log/` 目录下
4. 如果使用系统定时任务，建议设置合理的执行频率，避免过于频繁执行 

# 轮询模式管理功能

## 功能说明
本项目新增了轮询模式管理功能，可以通过API接口动态开启和关闭轮询模式，无需使用数据库，所有配置都保存在配置文件中。

## 配置文件
轮询模式配置保存在 `application/extra/polling.php` 文件中，包含以下配置项：
- `enabled`: 轮询模式开关（true/false）
- `interval`: 轮询间隔时间（秒）
- `timeout`: 轮询超时时间（秒）
- `max_retries`: 最大重试次数

## API接口

### 1. 获取轮询模式状态
- **接口地址**: `GET /api/common/getPollingStatus`
- **功能**: 获取当前轮询模式的配置状态
- **权限**: 需要登录验证
- **返回示例**:
```json
{
    "code": 1,
    "msg": "获取轮询模式状态成功",
    "data": {
        "enabled": false
    }
}
```

### 2. 设置轮询模式
- **接口地址**: `POST /api/common/setPollingMode`
- **功能**: 设置轮询模式的配置参数
- **权限**: 需要登录验证
- **参数**:
  - `enabled` (必填): 是否开启轮询模式（true/false）
- **返回示例**:
```json
{
    "code": 1,
    "msg": "轮询模式已开启",
    "data": {
        "enabled": true
    }
}
```

## 使用示例

### 开启轮询模式
```bash
curl -X POST "http://your-domain/api/common/setPollingMode" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"enabled": true}'
```

### 关闭轮询模式
```bash
curl -X POST "http://your-domain/api/common/setPollingMode" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"enabled": false}'
```

## 注意事项
1. 所有接口都需要登录验证
2. 配置修改后会立即生效，无需重启服务
3. 配置文件会自动备份，修改失败不会影响原有配置
4. 参数验证严格，超出范围的参数会返回错误信息

# 订单数据清理功能

## 功能说明
本项目新增了订单数据清理功能，可以安全地删除指定天数前的历史订单数据，帮助系统维护和数据库优化。

## 安全特性
- **权限控制**: 只有管理员（group_id=1）可以执行删除操作
- **确认机制**: 删除操作需要明确确认参数
- **事务保护**: 使用数据库事务确保操作的原子性
- **操作日志**: 自动记录所有删除操作到管理员日志表

## API接口

### 删除N天前的订单数据
- **接口地址**: `POST /api/common/deleteOldOrders`
- **功能**: 删除指定天数前的订单数据
- **参数**:
  - `days` (必填): 删除多少天前的订单数据
  - `confirm` (必填): 确认删除操作（true/false）
- **权限**: 仅管理员可访问
- **返回示例**:
```json
{
    "code": 1,
    "msg": "成功删除1500条30天前的订单数据",
    "data": {
        "deleted_count": 1500,
        "days": 30,
        "delete_time": "2024-01-01 00:00:00"
    }
}
```

## 使用示例

### 删除30天前的订单数据
```bash
curl -X POST "http://your-domain/api/common/deleteOldOrders" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
         "days": 30,
         "confirm": true
     }'
```

## 使用建议

### 1. 分批删除
对于大量数据，建议分批删除：
```bash
# 先删除90天前的数据
POST /api/common/deleteOldOrders {"days": 90, "confirm": true}

# 再删除60天前的数据
POST /api/common/deleteOldOrders {"days": 60, "confirm": true}

# 最后删除30天前的数据
POST /api/common/deleteOldOrders {"days": 30, "confirm": true}
```

### 2. 定期清理
建议定期清理历史数据：
- 每周清理90天前的数据
- 每月清理30天前的数据
- 每季度清理7天前的数据

## 注意事项

1. **数据安全**: 删除操作不可逆，请谨慎操作
2. **权限要求**: 只有管理员可以执行删除操作
3. **确认机制**: 必须设置confirm=true才能执行删除
4. **事务保护**: 删除操作使用事务，失败时会自动回滚
5. **日志记录**: 所有删除操作都会记录到管理员日志表
6. **性能考虑**: 大量数据删除时可能耗时较长，建议在低峰期执行
7. **备份建议**: 删除前建议先备份数据库

## 错误处理

### 常见错误及解决方案

1. **权限不足**
   ```
   {"code": 0, "msg": "只有管理员可以执行此操作"}
   ```
   解决方案：使用管理员账号登录

2. **参数错误**
   ```
   {"code": 0, "msg": "天数必须大于0"}
   ```
   解决方案：检查days参数是否正确

3. **未确认删除**
   ```
   {"code": 0, "msg": "请确认删除操作"}
   ```
   解决方案：设置confirm=true

4. **无数据可删除**
   ```
   {"code": 0, "msg": "没有找到符合条件的订单数据"}
   ```
   解决方案：调整days参数或检查数据是否存在

## 技术实现

### 数据库操作
- 使用ThinkPHP的数据库操作API
- 支持事务回滚机制
- 自动记录操作日志

### 安全机制
- 参数验证和过滤
- 权限检查
- SQL注入防护
- 操作确认机制

### 性能优化
- 使用索引优化查询性能
- 批量删除减少数据库压力
- 事务控制确保数据一致性 

# 仪表盘统计相关接口

## 获取最近一小时成功率（5分钟前的一小时）
- **接口地址**: `/api/dashboard/recent_hour_success_rate`
- **请求方式**: GET
- **功能说明**: 获取距离当前时间5分钟前，往前推1小时内的订单成功率（即：统计[当前时间-65分钟, 当前时间-5分钟]区间内的订单成功率）。
- **请求参数**:
  - `paytype` (可选): 支付类型，默认`lfwx`，可选`lfali`
- **返回数据**:
  - `successRate`: 成功率（百分比，保留两位小数）
  - `totalCount`: 该区间内订单总数
  - `successCount`: 该区间内成功订单数
  - `startTime`: 统计起始时间（格式：Y-m-d H:i:s）
  - `endTime`: 统计结束时间（格式：Y-m-d H:i:s）
- **返回示例**:
```json
{
  "code": 1,
  "msg": "获取最近一小时成功率成功",
  "data": {
    "successRate": 85.71,
    "totalCount": 14,
    "successCount": 12,
    "startTime": "2024-06-01 13:00:00",
    "endTime": "2024-06-01 14:00:00"
  }
}
```

## 获取最近5分钟订单量
- **接口地址**: `/api/dashboard/recent_five_minute_order_count`
- **请求方式**: GET
- **功能说明**: 获取最近5分钟内的订单总数。
- **请求参数**:
  - `paytype` (可选): 支付类型，默认`lfwx`，可选`lfali`
- **返回数据**:
  - `orderCount`: 订单数量
  - `startTime`: 统计起始时间（格式：Y-m-d H:i:s）
  - `endTime`: 统计结束时间（格式：Y-m-d H:i:s）
- **返回示例**:
```json
{
  "code": 1,
  "msg": "获取最近5分钟订单量成功",
  "data": {
    "orderCount": 3,
    "startTime": "2024-06-01 14:00:00",
    "endTime": "2024-06-01 14:05:00"
  }
}
``` 