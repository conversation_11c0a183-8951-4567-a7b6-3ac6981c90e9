<?php

namespace app\common\library;

use think\Cache;

/**
 * 缓存锁工具类
 * 使用ThinkPHP文件缓存实现分布式锁，用于高并发场景下的订单处理
 */
class CacheLock
{
    /**
     * 锁前缀
     */
    const LOCK_PREFIX = 'order_lock_';

    /**
     * 默认锁超时时间（秒）
     */
    const DEFAULT_TIMEOUT = 15;

    /**
     * 获取订单锁
     * 
     * @param string $orderNo 订单号
     * @param int $timeout 超时时间（秒）
     * @return bool 成功返回true，失败返回false
     */
    public static function acquireOrderLock($orderNo, $timeout = self::DEFAULT_TIMEOUT)
    {
        $lockKey = self::LOCK_PREFIX . md5($orderNo);
        $lockValue = [
            'pid' => getmypid(),
            'time' => date('Y-m-d H:i:s'),
            'order_no' => $orderNo,
            'expire_time' => time() + $timeout
        ];

        // 尝试设置缓存，如果已存在则返回false
        $result = Cache::set($lockKey, $lockValue, $timeout);

        // 如果设置成功，说明获取锁成功
        if ($result) {
            return true;
        }

        // 检查是否是因为锁已存在而失败
        $existingLock = Cache::get($lockKey);
        if ($existingLock && $existingLock['expire_time'] > time()) {
            // 锁仍然有效
            return false;
        }

        // 锁已过期，尝试重新获取
        Cache::rm($lockKey);
        return Cache::set($lockKey, $lockValue, $timeout);
    }

    /**
     * 释放订单锁
     * 
     * @param string $orderNo 订单号
     * @return bool
     */
    public static function releaseOrderLock($orderNo)
    {
        $lockKey = self::LOCK_PREFIX . md5($orderNo);
        return Cache::rm($lockKey);
    }

    /**
     * 检查订单是否被锁定
     * 
     * @param string $orderNo 订单号
     * @return bool
     */
    public static function isOrderLocked($orderNo)
    {
        $lockKey = self::LOCK_PREFIX . md5($orderNo);
        $lock = Cache::get($lockKey);

        if (!$lock) {
            return false;
        }

        // 检查锁是否过期
        if ($lock['expire_time'] <= time()) {
            Cache::rm($lockKey);
            return false;
        }

        return true;
    }

    /**
     * 获取锁信息
     * 
     * @param string $orderNo 订单号
     * @return array|null
     */
    public static function getLockInfo($orderNo)
    {
        $lockKey = self::LOCK_PREFIX . md5($orderNo);
        $lock = Cache::get($lockKey);

        if (!$lock) {
            return null;
        }

        // 检查锁是否过期
        if ($lock['expire_time'] <= time()) {
            Cache::rm($lockKey);
            return null;
        }

        return $lock;
    }

    /**
     * 清理过期的锁
     * 
     * @return int 清理的锁数量
     */
    public static function cleanExpiredLocks()
    {
        // 注意：这个方法需要根据具体的缓存驱动来实现
        // 对于文件缓存，可以遍历缓存目录
        $count = 0;

        // 获取缓存目录
        $cachePath = CACHE_PATH;
        $pattern = $cachePath . self::LOCK_PREFIX . '*';

        $files = glob($pattern);
        foreach ($files as $file) {
            $lock = Cache::get(basename($file, '.php'));
            if ($lock && $lock['expire_time'] <= time()) {
                Cache::rm(basename($file, '.php'));
                $count++;
            }
        }

        return $count;
    }
}
