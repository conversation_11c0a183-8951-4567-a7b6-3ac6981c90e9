# API接口文档

## 1. 接口概览

### 基础信息
- **API版本**: v2.0
- **基础URL**: `https://your-domain.com/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **签名算法**: MD5
- **支付渠道**: 来疯微信(lfwx)、来疯支付宝(lfali)

### 通用响应格式
```json
{
    "code": 1,           // 状态码：1成功，0失败
    "msg": "success",    // 响应消息
    "data": {}          // 响应数据（可选）
}
```

### 系统架构特点
- **异步支付检查**: 自动监控订单支付状态
- **代理池管理**: 支持多代理轮换
- **订单归档**: 自动归档历史订单
- **账号管理**: 支持多账号收款

## 2. 支付网关接口

### 2.1 创建支付订单
**接口地址**: `POST /api/gateway/index`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号，全局唯一 |
| mid | int | 是 | 商户ID |
| amount | decimal | 是 | 订单金额，范围0.01-50000.00 |
| channel_code | string | 是 | 支付渠道代码(lfwx/lfali) |
| notify_url | string | 是 | 异步通知地址 |
| callback_url | string | 否 | 同步回调地址 |
| sign | string | 是 | 签名字符串 |

**签名算法**:
```php
// 1. 参数按字典序排序（除sign外）
// 2. 拼接成 key1=value1&key2=value2 格式
// 3. 末尾追加 &key=商户密钥
// 4. 进行MD5加密
$sign = md5(urldecode($paramStr) . "&key=" . $merchantKey);
```

**请求示例**:
```json
{
    "out_trade_no": "*************",
    "mid": 1,
    "amount": "100.00",
    "channel_code": "lfwx",
    "notify_url": "https://merchant.com/notify",
    "callback_url": "https://merchant.com/callback",
    "sign": "abc123def456..."
}
```

**成功响应**:
```json
{
    "code": 1,
    "msg": "订单创建成功",
    "data": {
        "url": "https://pay.domain.com/pay?orderno=*************",
        "order_id": "SYS*************",
        "amount": "100.00"
    }
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "签名验证失败"
}
```

### 2.2 订单状态查询
**接口地址**: `POST /api/gateway/query`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号 |
| mid | int | 是 | 商户ID |
| sign | string | 是 | 签名字符串 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "out_trade_no": "*************",
        "pay_trade_no": "***************",
        "amount": "100.00",
        "real_amount": "100.00",
        "pay_status": 1,
        "pay_time": **********,
        "create_time": **********
    }
}
```

## 3. 账号管理接口

### 3.1 账号登录
**接口地址**: `POST /api/account/login`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号 |
| password | string | 是 | 密码 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "登录成功",
    "data": {
        "cookie": "L_pck_rm=xxx",
        "userid": "***********"
    }
}
```

### 3.2 账号列表查询
**接口地址**: `GET /api/account/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |
| name | string | 否 | 账号名称筛选 |
| status | int | 否 | 状态筛选(0禁用1启用) |
| hasErrorMsg | string | 否 | 是否有错误信息(true/false) |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "total": 100,
        "page": 1,
        "limit": 10,
        "list": [
            {
                "id": 1,
                "name": "test001",
                "status": 1,
                "diamond": 1000,
                "errormsg": "",
                "remark": "测试账号",
                "createtime": "2025-01-01 10:00:00",
                "pulltime": "2025-01-20 14:30:00",
                "nickname": "用户昵称",
                "today_count": 5,
                "yesterday_count": 8,
                "before_count": 3,
                "today_amount": "500.00",
                "yesterday_amount": "800.00",
                "before_amount": "300.00"
            }
        ]
    }
}
```

### 3.3 添加收款账号
**接口地址**: `POST /api/account/add`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 账号名称 |
| cookie | string | 是 | 登录Cookie |
| phone | string | 否 | 手机号 |
| password | string | 否 | 密码 |
| remark | string | 否 | 备注信息 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "添加成功"
}
```

### 3.4 账号导出
**接口地址**: `GET /api/account/export`

**请求参数**: 与列表查询相同

**成功响应**:
```json
{
    "code": 1,
    "msg": "导出成功",
    "data": {
        "download_url": "https://domain.com/uploads/export/账号列表_2025-01-20_14-30-15.csv",
        "filename": "账号列表_2025-01-20_14-30-15.csv",
        "total_count": 100
    }
}
```

### 3.5 修改账号状态
**接口地址**: `POST /api/account/status`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 账号ID |
| status | int | 是 | 状态(0禁用1启用) |

**成功响应**:
```json
{
    "code": 1,
    "msg": "修改账号状态成功"
}
```

### 3.4 账号列表查询
**接口地址**: `GET /api/account/index`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |
| user_id | int | 否 | 用户ID筛选 |
| status | int | 否 | 状态筛选 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "total": 100,
        "page": 1,
        "limit": 10,
        "list": [
            {
                "id": 1,
                "username": "test001",
                "nickname": "测试账号",
                "status": 1,
                "paystatus": 1,
                "balance": "1000.00",
                "total_amount": "5000.00",
                "order_count": 50,
                "success_rate": "95.00",
                "pulltime": **********,
                "create_time": **********
            }
        ]
    }
}
```

## 4. 订单管理接口

### 4.1 订单列表查询
**接口地址**: `GET /api/order/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |
| out_trade_no | string | 否 | 商户订单号 |
| pay_trade_no | string | 否 | 支付单号 |
| pay_status | int | 否 | 支付状态(0未支付1已支付) |
| paytype | string | 否 | 支付类型(lfwx/lfali) |
| callback_status | int | 否 | 回调状态(0未回调1成功2失败) |
| amount_min | decimal | 否 | 最小金额 |
| amount_max | decimal | 否 | 最大金额 |
| start_time | string | 否 | 开始时间 |
| end_time | string | 否 | 结束时间 |
| merchant_name | string | 否 | 商户名称 |

**特殊说明**:
- 默认查询最近3小时的订单
- 自动合并主表和归档表数据
- 非管理员只能查看自己的订单

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "total": 1000,
        "page": 1,
        "limit": 10,
        "list": [
            {
                "id": 1,
                "out_trade_no": "*************",
                "pay_trade_no": "1900000000051565828787",
                "dy_order_id": "3Web1900000000051568421787",
                "merchant_name": "测试商户",
                "account_name": "测试账号",
                "user_nickname": "用户昵称",
                "amount": "100.00",
                "paytype": "lfwx",
                "pay_status": 1,
                "callback_status": 1,
                "create_time": **********,
                "callback_time": **********,
                "remark": "备注信息"
            }
        ]
    }
}
```

### 4.2 订单详情查询
**接口地址**: `GET /api/order/detail`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "id": 1,
        "out_trade_no": "*************",
        "pay_trade_no": "1900000000051565828787",
        "dy_order_id": "3Web1900000000051568421787",
        "paytype": "lfwx",
        "create_time": **********,
        "callback_time": **********,
        "amount": "100.00",
        "pay_status": 1,
        "callback_status": 1,
        "remark": "备注信息",
        "merchant_name": "测试商户",
        "account_name": "测试账号",
        "user_nickname": "用户昵称"
    }
}
```

### 4.3 手动订单回调
**接口地址**: `POST /api/order/callback`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 订单ID |

**成功响应**:
```json
{
    "code": 1,
    "msg": "订单回调成功"
}
```

### 4.4 订单数据导出
**接口地址**: `GET /api/order/export`

**请求参数**: 与列表查询相同

**成功响应**:
```json
{
    "code": 1,
    "msg": "导出成功",
    "data": {
        "download_url": "https://domain.com/uploads/export/订单列表_2025-01-20_14-30-15.csv",
        "filename": "订单列表_2025-01-20_14-30-15.csv",
        "total_count": 1000
    }
}
```

## 5. 来疯支付服务接口

### 5.1 支付链接提取
**功能**: 从来疯支付响应中提取完整的支付宝支付链接

**调用方式**: `\laifeng\Service::extractAlipayUrl($responseString)`

**参数**:
- `$responseString`: 来疯支付API返回的JSON字符串

**返回值**: 完整的支付宝支付链接或false

### 5.2 订单号提取
**功能**: 从字符串中提取订单号

**调用方式**: `\laifeng\Service::extractOrderNo($str)`

**参数**:
- `$str`: 包含订单号的字符串

**返回值**: 提取的订单号或false

### 5.3 URL订单号提取
**功能**: 从URL中提取订单号

**调用方式**: `\laifeng\Service::extractOrderNoFromUrl($url)`

**参数**:
- `$url`: 包含订单号的URL

**返回值**: 提取的订单号或false

## 6. 命令行工具

### 6.1 订单支付状态检查
**命令**: `php think order:payment-check`

**功能**:
- 无限循环监控订单支付状态
- 每轮检查完成后间隔5秒
- 查询近5分钟的未支付订单
- 异步并发查询，最大并发100个
- 自动更新支付成功的订单状态
- 执行商户回调逻辑

**输出示例**:
```
开始执行订单支付状态检查...
执行时间：2025-01-20 14:30:15
共找到 25 个未支付订单需要检查
处理第 1 批，共 25 个订单
批量检查完成！成功：25，失败：0
检查结果：已支付 2 个，未支付 23 个
执行耗时：总耗时 1250.50ms，平均耗时 50.02ms/订单
本轮检查完成，等待 5 秒后执行下一轮...
```

### 6.2 账号拉单任务
**命令**: `php think account:pull`

**功能**:
- 定期检查账号状态
- 自动拉单测试账号可用性
- 更新账号错误信息

### 6.3 订单归档任务
**命令**: `php think archive:order`

**功能**:
- 自动归档历史订单
- 清理主表数据
- 保持系统性能

## 7. 收银台接口

### 5.1 获取支付信息
**接口地址**: `POST /index/cashier/getPayInfo`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号 |
| ip | string | 是 | 客户端IP |

**成功响应**:
```json
{
    "code": 1,
    "msg": "获取支付链接成功",
    "url": "https://pay.lfwx.com/xxx"
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "订单不存在"
}
```

## 6. 错误码定义

### 6.1 通用错误码
| 错误码 | 说明 |
|--------|------|
| 0 | 失败 |
| 1 | 成功 |
| -1 | 系统异常 |
| -2 | 参数错误 |
| -3 | 签名验证失败 |
| -4 | 权限不足 |
| -5 | 频率限制 |

### 6.2 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 商户不存在 |
| 1002 | 商户已禁用 |
| 1003 | 订单已存在 |
| 1004 | 订单不存在 |
| 1005 | 金额超出限制 |
| 1006 | 支付渠道不支持 |
| 1007 | 账号不可用 |
| 1008 | 代理IP不可用 |
| 1009 | 拉单失败 |
| 1010 | 订单已支付 |

## 7. 签名验证

### 7.1 签名生成规则
1. 将所有请求参数（除sign外）按参数名字典序排序
2. 将排序后的参数拼接成 `key1=value1&key2=value2` 格式
3. 在末尾追加商户密钥：`&key=merchant_key`
4. 对整个字符串进行MD5加密得到签名

### 7.2 PHP签名示例
```php
function generateSign($params, $merchantKey) {
    // 移除sign参数
    unset($params['sign']);
    
    // 按key排序
    ksort($params);
    
    // 拼接参数
    $paramStr = http_build_query($params);
    
    // 追加密钥并MD5加密
    return md5(urldecode($paramStr) . "&key=" . $merchantKey);
}
```

### 7.3 验证流程
1. 接收请求参数
2. 提取sign参数
3. 按相同规则生成签名
4. 比较生成的签名与接收的签名
5. 签名一致则验证通过

## 8. 接口调用示例

### 8.1 创建订单完整示例
```php
<?php
$params = [
    'out_trade_no' => time(),
    'mid' => 1,
    'amount' => '100.00',
    'channel_code' => 'Wx-Wap',
    'notify_url' => 'https://merchant.com/notify'
];

$merchantKey = 'your_merchant_key';
$params['sign'] = generateSign($params, $merchantKey);

$response = httpPost('https://domain.com/api/gateway/index', $params);
$result = json_decode($response, true);

if ($result['code'] == 1) {
    // 跳转到支付页面
    header('Location: ' . $result['data']['url']);
} else {
    echo '创建订单失败：' . $result['msg'];
}
?>
```

### 8.2 查询订单状态示例
```php
<?php
$params = [
    'out_trade_no' => '*************',
    'mid' => 1
];

$merchantKey = 'your_merchant_key';
$params['sign'] = generateSign($params, $merchantKey);

$response = httpPost('https://domain.com/api/gateway/query', $params);
$result = json_decode($response, true);

if ($result['code'] == 1) {
    $orderInfo = $result['data'];
    echo '支付状态：' . ($orderInfo['pay_status'] ? '已支付' : '未支付');
} else {
    echo '查询失败：' . $result['msg'];
}
?>
```

## 9. 异步通知

### 9.1 通知格式
商户在创建订单时提供的 `notify_url` 将接收到以下格式的POST通知：

```json
{
    "out_trade_no": "*************",
    "pay_trade_no": "***************",
    "amount": "100.00",
    "real_amount": "100.00",
    "pay_status": 1,
    "pay_time": **********,
    "sign": "abc123def456..."
}
```

### 9.2 通知验证
商户需要验证通知的签名，确保通知来源的真实性。

### 9.3 响应要求
商户处理完通知后，需要返回字符串 `"success"`，否则系统将进行重试。

### 9.4 重试机制
- 重试次数：最多5次
- 重试间隔：1分钟、5分钟、15分钟、30分钟、60分钟
- 超时时间：30秒

## 10. 接口限流

### 10.1 限流规则
- 创建订单：每分钟最多100次
- 查询接口：每分钟最多1000次
- 登录相关：每分钟最多10次

### 10.2 限流响应
```json
{
    "code": -5,
    "msg": "请求过于频繁，请稍后重试"
}
```