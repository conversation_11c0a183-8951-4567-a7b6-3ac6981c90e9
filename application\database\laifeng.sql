/*
Navicat MySQL Data Transfer

Source Server         : 本地
Source Server Version : 50726
Source Host           : localhost:3306
Source Database       : laifeng

Target Server Type    : MYSQL
Target Server Version : 50726
File Encoding         : 65001

Date: 2025-07-21 08:46:30
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for fa_account
-- ----------------------------
DROP TABLE IF EXISTS `fa_account`;
CREATE TABLE `fa_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '收款账号',
  `account_identity` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '账号标识',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '账号开关:1=正常,0=禁用',
  `pulltime` bigint(16) DEFAULT NULL COMMENT '拉单时间',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `errormsg` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '状态信息',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `config` text CHARACTER SET utf8 COMMENT '配置信息',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `diamond` int(16) DEFAULT NULL COMMENT '号内余额',
  `pay_status` int(1) unsigned DEFAULT '1' COMMENT '进单状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- ----------------------------
-- Records of fa_account
-- ----------------------------
INSERT INTO `fa_account` VALUES ('27', '***********', null, '1', '**********', '2', '', '', '{\"cookie\":\"L_pck_rm=rw%2BSD6ZClFDu1gPoVmF8mXCovav%2FIrKIB%2F1ndbK7FqkNAvAVd9cef10undpxqSclCt0C5qRHNpWSig6SGTfmYGBxGgMD6teFVdH8oCAd6RmAkXgJjk6m7bJCB4WgQYiWBudYIIklk%2BCZCEuqwf5b7klY3nQEmzal4KQHN%2FiKGQs%3D\",\"phone\":\"***********\",\"password\":\"huangsi0405\"}', '**********', '**********', '20000', '1');

-- ----------------------------
-- Table structure for fa_admin
-- ----------------------------
DROP TABLE IF EXISTS `fa_admin`;
CREATE TABLE `fa_admin` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(20) DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `password` varchar(32) DEFAULT '' COMMENT '密码',
  `salt` varchar(30) DEFAULT '' COMMENT '密码盐',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `email` varchar(100) DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
  `loginfailure` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '失败次数',
  `logintime` bigint(16) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) DEFAULT '' COMMENT 'Session标识',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- Records of fa_admin
-- ----------------------------
INSERT INTO `fa_admin` VALUES ('1', 'admin', 'Admin', '6ebd919a4520c5a51a4da7b4c0c4aea0', '9e1062', 'http://*********/assets/img/avatar.png', '<EMAIL>', '', '0', '1743835406', '127.0.0.1', '1491635035', '1743835406', 'f8e8213a-f207-49d0-97ec-66179ca4fdae', 'normal');

-- ----------------------------
-- Table structure for fa_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_admin_log`;
CREATE TABLE `fa_admin_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员ID',
  `username` varchar(30) DEFAULT '' COMMENT '管理员名字',
  `url` varchar(1500) DEFAULT '' COMMENT '操作页面',
  `title` varchar(100) DEFAULT '' COMMENT '日志标题',
  `content` longtext NOT NULL COMMENT '内容',
  `ip` varchar(50) DEFAULT '' COMMENT 'IP',
  `useragent` varchar(255) DEFAULT '' COMMENT 'User-Agent',
  `createtime` bigint(16) DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `name` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='管理员日志表';

-- ----------------------------
-- Records of fa_admin_log
-- ----------------------------
INSERT INTO `fa_admin_log` VALUES ('1', '1', 'admin', '/api/common/deleteOldOrders', '删除历史订单数据', '删除了1天前的24条订单数据，删除时间点：2025-06-29 00:29:18', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '1751214558');

-- ----------------------------
-- Table structure for fa_area
-- ----------------------------
DROP TABLE IF EXISTS `fa_area`;
CREATE TABLE `fa_area` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(10) DEFAULT NULL COMMENT '父id',
  `shortname` varchar(100) DEFAULT NULL COMMENT '简称',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `mergename` varchar(255) DEFAULT NULL COMMENT '全称',
  `level` tinyint(4) DEFAULT NULL COMMENT '层级:1=省,2=市,3=区/县',
  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
  `code` varchar(100) DEFAULT NULL COMMENT '长途区号',
  `zip` varchar(100) DEFAULT NULL COMMENT '邮编',
  `first` varchar(50) DEFAULT NULL COMMENT '首字母',
  `lng` varchar(100) DEFAULT NULL COMMENT '经度',
  `lat` varchar(100) DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区表';

-- ----------------------------
-- Records of fa_area
-- ----------------------------

-- ----------------------------
-- Table structure for fa_attachment
-- ----------------------------
DROP TABLE IF EXISTS `fa_attachment`;
CREATE TABLE `fa_attachment` (
  `id` int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) DEFAULT '' COMMENT '类别',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `url` varchar(255) DEFAULT '' COMMENT '物理路径',
  `imagewidth` int(10) unsigned DEFAULT '0' COMMENT '宽度',
  `imageheight` int(10) unsigned DEFAULT '0' COMMENT '高度',
  `imagetype` varchar(30) DEFAULT '' COMMENT '图片类型',
  `imageframes` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '图片帧数',
  `filename` varchar(100) DEFAULT '' COMMENT '文件名称',
  `filesize` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小',
  `mimetype` varchar(100) DEFAULT '' COMMENT 'mime类型',
  `extparam` varchar(255) DEFAULT '' COMMENT '透传数据',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建日期',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `uploadtime` bigint(16) DEFAULT NULL COMMENT '上传时间',
  `storage` varchar(100) NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `sha1` varchar(40) DEFAULT '' COMMENT '文件 sha1编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='附件表';

-- ----------------------------
-- Records of fa_attachment
-- ----------------------------
INSERT INTO `fa_attachment` VALUES ('1', '', '1', '0', '/assets/img/qrcode.png', '150', '150', 'png', '0', 'qrcode.png', '21859', 'image/png', '', '1491635035', '1491635035', '1491635035', 'local', '17163603d0263e4838b9387ff2cd4877e8b018f6');

-- ----------------------------
-- Table structure for fa_auth_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_group`;
CREATE TABLE `fa_auth_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父组别',
  `name` varchar(100) DEFAULT '' COMMENT '组名',
  `rules` text NOT NULL COMMENT '规则ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='分组表';

-- ----------------------------
-- Records of fa_auth_group
-- ----------------------------
INSERT INTO `fa_auth_group` VALUES ('1', '0', 'Admin group', '*', '1491635035', '1491635035', 'normal');
INSERT INTO `fa_auth_group` VALUES ('2', '1', 'Second group', '13,14,16,15,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,40,41,42,43,44,45,46,47,48,49,50,55,56,57,58,59,60,61,62,63,64,65,1,9,10,11,7,6,8,2,4,5', '1491635035', '1491635035', 'normal');
INSERT INTO `fa_auth_group` VALUES ('3', '2', 'Third group', '1,4,9,10,11,13,14,15,16,17,40,41,42,43,44,45,46,47,48,49,50,55,56,57,58,59,60,61,62,63,64,65,5', '1491635035', '1491635035', 'normal');
INSERT INTO `fa_auth_group` VALUES ('4', '1', 'Second group 2', '1,4,13,14,15,16,17,55,56,57,58,59,60,61,62,63,64,65', '1491635035', '1491635035', 'normal');
INSERT INTO `fa_auth_group` VALUES ('5', '2', 'Third group 2', '1,2,6,7,8,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34', '1491635035', '1491635035', 'normal');

-- ----------------------------
-- Table structure for fa_auth_group_access
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_group_access`;
CREATE TABLE `fa_auth_group_access` (
  `uid` int(10) unsigned NOT NULL COMMENT '会员ID',
  `group_id` int(10) unsigned NOT NULL COMMENT '级别ID',
  UNIQUE KEY `uid_group_id` (`uid`,`group_id`),
  KEY `uid` (`uid`),
  KEY `group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限分组表';

-- ----------------------------
-- Records of fa_auth_group_access
-- ----------------------------
INSERT INTO `fa_auth_group_access` VALUES ('1', '1');

-- ----------------------------
-- Table structure for fa_auth_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_rule`;
CREATE TABLE `fa_auth_rule` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('menu','file') NOT NULL DEFAULT 'file' COMMENT 'menu为菜单,file为权限节点',
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
  `name` varchar(100) DEFAULT '' COMMENT '规则名称',
  `title` varchar(50) DEFAULT '' COMMENT '规则名称',
  `icon` varchar(50) DEFAULT '' COMMENT '图标',
  `url` varchar(255) DEFAULT '' COMMENT '规则URL',
  `condition` varchar(255) DEFAULT '' COMMENT '条件',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `ismenu` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否为菜单',
  `menutype` enum('addtabs','blank','dialog','ajax') DEFAULT NULL COMMENT '菜单类型',
  `extend` varchar(255) DEFAULT '' COMMENT '扩展属性',
  `py` varchar(30) DEFAULT '' COMMENT '拼音首字母',
  `pinyin` varchar(100) DEFAULT '' COMMENT '拼音',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`) USING BTREE,
  KEY `pid` (`pid`),
  KEY `weigh` (`weigh`)
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8mb4 COMMENT='节点表';

-- ----------------------------
-- Records of fa_auth_rule
-- ----------------------------
INSERT INTO `fa_auth_rule` VALUES ('1', 'file', '0', 'dashboard', 'Dashboard', 'fa fa-dashboard', '', '', 'Dashboard tips', '1', null, '', 'kzt', 'kongzhitai', '1491635035', '1491635035', '143', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('2', 'file', '0', 'general', 'General', 'fa fa-cogs', '', '', '', '1', null, '', 'cggl', 'changguiguanli', '1491635035', '1491635035', '137', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('3', 'file', '0', 'category', 'Category', 'fa fa-leaf', '', '', 'Category tips', '0', null, '', 'flgl', 'fenleiguanli', '1491635035', '1491635035', '119', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('4', 'file', '0', 'addon', 'Addon', 'fa fa-rocket', '', '', 'Addon tips', '1', null, '', 'cjgl', 'chajianguanli', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('5', 'file', '0', 'auth', 'Auth', 'fa fa-group', '', '', '', '1', null, '', 'qxgl', 'quanxianguanli', '1491635035', '1491635035', '99', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('6', 'file', '2', 'general/config', 'Config', 'fa fa-cog', '', '', 'Config tips', '1', null, '', 'xtpz', 'xitongpeizhi', '1491635035', '1491635035', '60', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('7', 'file', '2', 'general/attachment', 'Attachment', 'fa fa-file-image-o', '', '', 'Attachment tips', '1', null, '', 'fjgl', 'fujianguanli', '1491635035', '1491635035', '53', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('8', 'file', '2', 'general/profile', 'Profile', 'fa fa-user', '', '', '', '1', null, '', 'grzl', 'gerenziliao', '1491635035', '1491635035', '34', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('9', 'file', '5', 'auth/admin', 'Admin', 'fa fa-user', '', '', 'Admin tips', '1', null, '', 'glygl', 'guanliyuanguanli', '1491635035', '1491635035', '118', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('10', 'file', '5', 'auth/adminlog', 'Admin log', 'fa fa-list-alt', '', '', 'Admin log tips', '1', null, '', 'glyrz', 'guanliyuanrizhi', '1491635035', '1491635035', '113', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('11', 'file', '5', 'auth/group', 'Group', 'fa fa-group', '', '', 'Group tips', '1', null, '', 'jsz', 'juesezu', '1491635035', '1491635035', '109', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('12', 'file', '5', 'auth/rule', 'Rule', 'fa fa-bars', '', '', 'Rule tips', '1', null, '', 'cdgz', 'caidanguize', '1491635035', '1491635035', '104', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('13', 'file', '1', 'dashboard/index', 'View', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '136', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('14', 'file', '1', 'dashboard/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '135', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('15', 'file', '1', 'dashboard/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '133', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('16', 'file', '1', 'dashboard/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '134', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('17', 'file', '1', 'dashboard/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '132', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('18', 'file', '6', 'general/config/index', 'View', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '52', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('19', 'file', '6', 'general/config/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '51', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('20', 'file', '6', 'general/config/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '50', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('21', 'file', '6', 'general/config/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '49', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('22', 'file', '6', 'general/config/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '48', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('23', 'file', '7', 'general/attachment/index', 'View', 'fa fa-circle-o', '', '', 'Attachment tips', '0', null, '', '', '', '1491635035', '1491635035', '59', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('24', 'file', '7', 'general/attachment/select', 'Select attachment', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '58', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('25', 'file', '7', 'general/attachment/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '57', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('26', 'file', '7', 'general/attachment/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '56', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('27', 'file', '7', 'general/attachment/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '55', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('28', 'file', '7', 'general/attachment/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '54', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('29', 'file', '8', 'general/profile/index', 'View', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '33', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('30', 'file', '8', 'general/profile/update', 'Update profile', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '32', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('31', 'file', '8', 'general/profile/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '31', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('32', 'file', '8', 'general/profile/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '30', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('33', 'file', '8', 'general/profile/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '29', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('34', 'file', '8', 'general/profile/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '28', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('35', 'file', '3', 'category/index', 'View', 'fa fa-circle-o', '', '', 'Category tips', '0', null, '', '', '', '1491635035', '1491635035', '142', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('36', 'file', '3', 'category/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '141', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('37', 'file', '3', 'category/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '140', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('38', 'file', '3', 'category/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '139', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('39', 'file', '3', 'category/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '138', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('40', 'file', '9', 'auth/admin/index', 'View', 'fa fa-circle-o', '', '', 'Admin tips', '0', null, '', '', '', '1491635035', '1491635035', '117', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('41', 'file', '9', 'auth/admin/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '116', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('42', 'file', '9', 'auth/admin/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '115', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('43', 'file', '9', 'auth/admin/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '114', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('44', 'file', '10', 'auth/adminlog/index', 'View', 'fa fa-circle-o', '', '', 'Admin log tips', '0', null, '', '', '', '1491635035', '1491635035', '112', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('45', 'file', '10', 'auth/adminlog/detail', 'Detail', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '111', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('46', 'file', '10', 'auth/adminlog/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '110', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('47', 'file', '11', 'auth/group/index', 'View', 'fa fa-circle-o', '', '', 'Group tips', '0', null, '', '', '', '1491635035', '1491635035', '108', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('48', 'file', '11', 'auth/group/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '107', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('49', 'file', '11', 'auth/group/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '106', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('50', 'file', '11', 'auth/group/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '105', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('51', 'file', '12', 'auth/rule/index', 'View', 'fa fa-circle-o', '', '', 'Rule tips', '0', null, '', '', '', '1491635035', '1491635035', '103', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('52', 'file', '12', 'auth/rule/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '102', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('53', 'file', '12', 'auth/rule/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '101', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('54', 'file', '12', 'auth/rule/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '100', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('55', 'file', '4', 'addon/index', 'View', 'fa fa-circle-o', '', '', 'Addon tips', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('56', 'file', '4', 'addon/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('57', 'file', '4', 'addon/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('58', 'file', '4', 'addon/del', 'Delete', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('59', 'file', '4', 'addon/downloaded', 'Local addon', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('60', 'file', '4', 'addon/state', 'Update state', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('63', 'file', '4', 'addon/config', 'Setting', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('64', 'file', '4', 'addon/refresh', 'Refresh', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('65', 'file', '4', 'addon/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('66', 'file', '0', 'user', 'User', 'fa fa-user-circle', '', '', '', '1', null, '', 'hygl', 'huiyuanguanli', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('67', 'file', '66', 'user/user', 'User', 'fa fa-user', '', '', '', '1', null, '', 'hygl', 'huiyuanguanli', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('68', 'file', '67', 'user/user/index', 'View', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('69', 'file', '67', 'user/user/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('70', 'file', '67', 'user/user/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('71', 'file', '67', 'user/user/del', 'Del', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('72', 'file', '67', 'user/user/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('73', 'file', '66', 'user/group', 'User group', 'fa fa-users', '', '', '', '1', null, '', 'hyfz', 'huiyuanfenzu', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('74', 'file', '73', 'user/group/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('75', 'file', '73', 'user/group/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('76', 'file', '73', 'user/group/index', 'View', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('77', 'file', '73', 'user/group/del', 'Del', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('78', 'file', '73', 'user/group/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('79', 'file', '66', 'user/rule', 'User rule', 'fa fa-circle-o', '', '', '', '1', null, '', 'hygz', 'huiyuanguize', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('80', 'file', '79', 'user/rule/index', 'View', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('81', 'file', '79', 'user/rule/del', 'Del', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('82', 'file', '79', 'user/rule/add', 'Add', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('83', 'file', '79', 'user/rule/edit', 'Edit', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');
INSERT INTO `fa_auth_rule` VALUES ('84', 'file', '79', 'user/rule/multi', 'Multi', 'fa fa-circle-o', '', '', '', '0', null, '', '', '', '1491635035', '1491635035', '0', 'normal');

-- ----------------------------
-- Table structure for fa_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_category`;
CREATE TABLE `fa_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
  `type` varchar(30) DEFAULT '' COMMENT '栏目类型',
  `name` varchar(30) DEFAULT '',
  `nickname` varchar(50) DEFAULT '',
  `flag` set('hot','index','recommend') DEFAULT '',
  `image` varchar(100) DEFAULT '' COMMENT '图片',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键字',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `diyname` varchar(30) DEFAULT '' COMMENT '自定义名称',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `weigh` (`weigh`,`id`),
  KEY `pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- ----------------------------
-- Records of fa_category
-- ----------------------------

-- ----------------------------
-- Table structure for fa_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_config`;
CREATE TABLE `fa_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) DEFAULT '' COMMENT '变量名',
  `group` varchar(30) DEFAULT '' COMMENT '分组',
  `title` varchar(100) DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `visible` varchar(255) DEFAULT '' COMMENT '可见条件',
  `value` text COMMENT '变量值',
  `content` text COMMENT '变量字典数据',
  `rule` varchar(100) DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COMMENT='系统配置';

-- ----------------------------
-- Records of fa_config
-- ----------------------------
INSERT INTO `fa_config` VALUES ('1', 'name', 'basic', 'Site name', '请填写站点名称', 'string', '', '我的网站', '', 'required', '', '');
INSERT INTO `fa_config` VALUES ('2', 'beian', 'basic', 'Beian', '粤ICP备15000000号-1', 'string', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES ('3', 'cdnurl', 'basic', 'Cdn url', '如果全站静态资源使用第三方云储存请配置该值', 'string', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES ('4', 'version', 'basic', 'Version', '如果静态资源有变动请重新配置该值', 'string', '', '1.0.1', '', 'required', '', '');
INSERT INTO `fa_config` VALUES ('5', 'timezone', 'basic', 'Timezone', '', 'string', '', 'Asia/Shanghai', '', 'required', '', '');
INSERT INTO `fa_config` VALUES ('6', 'forbiddenip', 'basic', 'Forbidden ip', '一行一条记录', 'text', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES ('7', 'languages', 'basic', 'Languages', '', 'array', '', '{\"backend\":\"zh-cn\",\"frontend\":\"zh-cn\"}', '', 'required', '', '');
INSERT INTO `fa_config` VALUES ('8', 'fixedpage', 'basic', 'Fixed page', '请输入左侧菜单栏存在的链接', 'string', '', 'dashboard', '', 'required', '', '');
INSERT INTO `fa_config` VALUES ('9', 'categorytype', 'dictionary', 'Category type', '', 'array', '', '{\"default\":\"Default\",\"page\":\"Page\",\"article\":\"Article\",\"test\":\"Test\"}', '', '', '', '');
INSERT INTO `fa_config` VALUES ('10', 'configgroup', 'dictionary', 'Config group', '', 'array', '', '{\"basic\":\"Basic\",\"email\":\"Email\",\"dictionary\":\"Dictionary\",\"user\":\"User\",\"example\":\"Example\"}', '', '', '', '');
INSERT INTO `fa_config` VALUES ('11', 'mail_type', 'email', 'Mail type', '选择邮件发送方式', 'select', '', '1', '[\"请选择\",\"SMTP\"]', '', '', '');
INSERT INTO `fa_config` VALUES ('12', 'mail_smtp_host', 'email', 'Mail smtp host', '错误的配置发送邮件会导致服务器超时', 'string', '', 'smtp.qq.com', '', '', '', '');
INSERT INTO `fa_config` VALUES ('13', 'mail_smtp_port', 'email', 'Mail smtp port', '(不加密默认25,SSL默认465,TLS默认587)', 'string', '', '465', '', '', '', '');
INSERT INTO `fa_config` VALUES ('14', 'mail_smtp_user', 'email', 'Mail smtp user', '（填写完整用户名）', 'string', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES ('15', 'mail_smtp_pass', 'email', 'Mail smtp password', '（填写您的密码或授权码）', 'password', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES ('16', 'mail_verify_type', 'email', 'Mail vertify type', '（SMTP验证方式[推荐SSL]）', 'select', '', '2', '[\"无\",\"TLS\",\"SSL\"]', '', '', '');
INSERT INTO `fa_config` VALUES ('17', 'mail_from', 'email', 'Mail from', '', 'string', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES ('18', 'attachmentcategory', 'dictionary', 'Attachment category', '', 'array', '', '{\"category1\":\"Category1\",\"category2\":\"Category2\",\"custom\":\"Custom\"}', '', '', '', '');

-- ----------------------------
-- Table structure for fa_ems
-- ----------------------------
DROP TABLE IF EXISTS `fa_ems`;
CREATE TABLE `fa_ems` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) DEFAULT '' COMMENT '事件',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `code` varchar(10) DEFAULT '' COMMENT '验证码',
  `times` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '验证次数',
  `ip` varchar(30) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮箱验证码表';

-- ----------------------------
-- Records of fa_ems
-- ----------------------------

-- ----------------------------
-- Table structure for fa_merchant
-- ----------------------------
DROP TABLE IF EXISTS `fa_merchant`;
CREATE TABLE `fa_merchant` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '商户名称',
  `key` varchar(64) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '密钥',
  `status` int(1) NOT NULL COMMENT '状态',
  `remark` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci COMMENT='商户表';

-- ----------------------------
-- Records of fa_merchant
-- ----------------------------
INSERT INTO `fa_merchant` VALUES ('3', 'ccc', 'EJVmTU57nraGYkgfDFVezHw2JI00I0iP', '1', '');
INSERT INTO `fa_merchant` VALUES ('2', '测试1', 'ZOLpK6LanvwaGoynG9t1YnmpZOxAT3mf', '1', '234');

-- ----------------------------
-- Table structure for fa_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_order`;
CREATE TABLE `fa_order` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `out_trade_no` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '商户订单号',
  `local_trade_no` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '本站订单号',
  `pay_trade_no` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '支付订单号',
  `amount` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `merchant_id` bigint(11) NOT NULL DEFAULT '0' COMMENT '上游商户id',
  `create_ip` varchar(32) CHARACTER SET utf8 DEFAULT '' COMMENT '拉单ip',
  `useragent` text CHARACTER SET utf8 COMMENT '拉单浏览器头',
  `paytype` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '通道编码',
  `pay_url` text CHARACTER SET utf8 COMMENT '支付地址',
  `pay_status` int(11) NOT NULL DEFAULT '0' COMMENT '支付状态:0=未支付;1=已支付',
  `callback_url` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '回调地址',
  `callback_status` int(11) NOT NULL DEFAULT '0' COMMENT '回调状态:1=已回调;0=未回调',
  `callback_time` int(16) DEFAULT NULL COMMENT '回调时间',
  `create_time` int(16) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(16) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `account_id` bigint(11) DEFAULT NULL COMMENT '账号',
  `user_id` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `dy_order_id` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '支付订单号',
  PRIMARY KEY (`id`,`out_trade_no`),
  UNIQUE KEY `idx_out_trade_no` (`out_trade_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_paytype` (`paytype`),
  KEY `idx_callback_status` (`callback_status`),
  KEY `idx_merchant_status_paytype_time` (`merchant_id`,`callback_status`,`paytype`,`callback_time`),
  KEY `idx_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci COMMENT='订单表';

-- ----------------------------
-- Records of fa_order
-- ----------------------------
INSERT INTO `fa_order` VALUES ('15', '**********', '202507201926510233420000', '1900000000051748582787', '20.00', '2', '', null, 'lfali', 'https://openapi.alipay.com/gateway.do?app_cert_sn=52af608a1e9b107e91a931f210814e38&charset=utf-8&alipay_root_cert_sn=687b59193f3f462dd5336e5abf83c5d8_02941eef3187dddf3d3b83462e1dfcf6&method=alipay.trade.wap.pay&sign=p2eLJeKR3K5%2BDsNqGFt%2BxdR0G%2Bng8y6QeDXD6bMwUyB%2Fvb4Lt55tIDDfDU6Ih38BMHUV1I5IZiJ1yq2btnlMvNARTh7iXRxPNTR7hfgmzZJKnYjI%2FClDggdiAyGpq4YXw%2BcFvdedIN5VD8b0DnE3uX2PHfsfpePlVsqh2g1UtuBYpfD2Xo5uqr9Adppjul0oCe9D7goSkW5sGRWAlONsVaa8TMiRz3FwiHWRjdcS778l44AQrN5NkaaiABP1JW7d439ZhAw2DR4P%2BeBp58EnP6kmbkQ7SnBY6YT2e2xadLdtMokChjHAVF2Fhkilpve0KzqdUATLr2ugWJ51KxQdow%3D%3D&return_url=https%3A%2F%2Fzhifu.laifeng.com%2Fm%3Forigin_tradeid%3D1900000000051748611787&notify_url=https%3A%2F%2Fcallback.zhifu.laifeng.com%2Fcharge%2Fdirect%2F7000%2Fcallback%2Fpay&version=1.0&app_id=2021************&sign_type=RSA2&timestamp=2025-07-20+19%3A26%3A56&alipay_sdk=alipay-sdk-java-4.40.237.ALL&format=json&biz_content=%7B%22out_trade_no%22%3A%221900000000051748611787%22%2C%22total_amount%22%3A20.0%2C%22subject%22%3A%22%E6%9D%A5%E7%96%AF%E6%98%9F%E5%B8%81%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22time_expire%22%3A%222025-07-20+19%3A28%3A56%22%2C%22business_params%22%3A%7B%22outTradeRiskInfo%22%3A%22%7B%5C%22desensitizedUid+%5C%22%3A%5C%22fe00f02f6831f481725355889b703880%5C%22%2C%5C%22extraAccountRiskLevel+%5C%22%3A%5C%22low%5C%22%7D%22%2C%22mc_create_trade_ip%22%3A%2249.114.179.85%22%7D%7D', '1', 'https://www.baidu.com', '1', '**********', '**********', '**********', null, '27', '2', '3Web1900000000051748582787');
INSERT INTO `fa_order` VALUES ('16', '**********', '202507201929422376830000', '2025072019294632378', '20.00', '2', '', null, 'lfwx', 'https://laifeng.qianlimalyy.online/pay/submit/2025072019294632378/', '1', 'https://www.baidu.com', '1', '**********', '**********', '**********', null, '27', '2', '3Web1900000000051750177787');
INSERT INTO `fa_order` VALUES ('17', '**********', '202507201935260919180000', '2025072019353051428', '20.00', '2', '', null, 'lfwx', 'https://laifeng.qianlimalyy.online/pay/submit/2025072019353051428/', '1', 'https://www.baidu.com', '1', '**********', '**********', '**********', null, '27', '2', '3Web1900000000051751251787');
INSERT INTO `fa_order` VALUES ('18', '**********', '202507202033296760700000', '1900000000051770636787', '20.00', '2', '', null, 'lfali', 'https://openapi.alipay.com/gateway.do?app_cert_sn=52af608a1e9b107e91a931f210814e38&charset=utf-8&alipay_root_cert_sn=687b59193f3f462dd5336e5abf83c5d8_02941eef3187dddf3d3b83462e1dfcf6&method=alipay.trade.wap.pay&sign=Q29mVeP3Se8qT5hHxPmHhzP7ec%2BSpgy7LW2SzRpPShkolufG7yc2qq7OuEsBwoHpVYkEIBWZFhYm6g%2F4%2FPIe8uLqRet1UESLrskpVLW9rThuALAVkVbApCAv3iG7jn0P4z6UbEDof%2BYyGK6bJxcVfDsBdZxc6IC9tiURSNJAp9QGXwsSFzw7wXzPVi%2F0nCiXfnUIlPTQbKmeGktxoMM1g3e1Jr09rw62g3WLQzVmRQ4bKrBwty26E0nWp1XSvePXzY5mhyoXmTKRh9%2BX1quayDnYuCkAHRy4y5YyLBgbkawXZ965jG%2F0y2Yl%2Fa8MuntVPtoHxr4GA6Mg1hVjbl9t3g%3D%3D&return_url=https%3A%2F%2Fzhifu.laifeng.com%2Fm%3Forigin_tradeid%3D1900000000051770638787&notify_url=https%3A%2F%2Fcallback.zhifu.laifeng.com%2Fcharge%2Fdirect%2F7000%2Fcallback%2Fpay&version=1.0&app_id=2021************&sign_type=RSA2&timestamp=2025-07-20+20%3A33%3A34&alipay_sdk=alipay-sdk-java-4.40.237.ALL&format=json&biz_content=%7B%22out_trade_no%22%3A%221900000000051770638787%22%2C%22total_amount%22%3A20.0%2C%22subject%22%3A%22%E6%9D%A5%E7%96%AF%E6%98%9F%E5%B8%81%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22time_expire%22%3A%222025-07-20+20%3A35%3A34%22%2C%22business_params%22%3A%7B%22outTradeRiskInfo%22%3A%22%7B%5C%22desensitizedUid+%5C%22%3A%5C%22fe00f02f6831f481725355889b703880%5C%22%2C%5C%22extraAccountRiskLevel+%5C%22%3A%5C%22low%5C%22%7D%22%2C%22mc_create_trade_ip%22%3A%22113.6.44.8%22%7D%7D', '0', 'https://www.baidu.com', '0', null, '**********', '**********', null, '27', '2', '3Web1900000000051770636787');
INSERT INTO `fa_order` VALUES ('19', '**********', '202507202033503962690000', '2025072020335598187', '20.00', '2', '', null, 'lfwx', 'https://laifeng.qianlimalyy.online/pay/submit/2025072020335598187/', '0', 'https://www.baidu.com', '0', null, '**********', '**********', null, '27', '2', '3Web1900000000051770719787');

-- ----------------------------
-- Table structure for fa_order_history
-- ----------------------------
DROP TABLE IF EXISTS `fa_order_history`;
CREATE TABLE `fa_order_history` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `out_trade_no` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '商户订单号',
  `local_trade_no` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '本站订单号',
  `pay_trade_no` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '支付订单号',
  `amount` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `merchant_id` bigint(11) NOT NULL DEFAULT '0' COMMENT '上游商户id',
  `create_ip` varchar(32) CHARACTER SET utf8 DEFAULT '' COMMENT '拉单ip',
  `useragent` text CHARACTER SET utf8 COMMENT '拉单浏览器头',
  `paytype` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '通道编码',
  `pay_url` text CHARACTER SET utf8 COMMENT '支付地址',
  `pay_status` int(11) NOT NULL DEFAULT '0' COMMENT '支付状态:0=未支付;1=已支付',
  `callback_url` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '回调地址',
  `callback_status` int(11) NOT NULL DEFAULT '0' COMMENT '回调状态:1=已回调;0=未回调',
  `callback_time` int(16) DEFAULT NULL COMMENT '回调时间',
  `create_time` int(16) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(16) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `account_id` bigint(11) DEFAULT NULL COMMENT '账号',
  `user_id` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `dy_order_id` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '支付订单号',
  `papi_id` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '支付订单号',
  PRIMARY KEY (`id`,`out_trade_no`),
  UNIQUE KEY `idx_out_trade_no` (`out_trade_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_paytype` (`paytype`),
  KEY `idx_callback_status` (`callback_status`),
  KEY `idx_merchant_status_paytype_time` (`merchant_id`,`callback_status`,`paytype`,`callback_time`),
  KEY `idx_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci COMMENT='订单表';

-- ----------------------------
-- Records of fa_order_history
-- ----------------------------

-- ----------------------------
-- Table structure for fa_rate
-- ----------------------------
DROP TABLE IF EXISTS `fa_rate`;
CREATE TABLE `fa_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rate` float(10,2) NOT NULL,
  `merchant_id` int(10) NOT NULL,
  `createtime` bigint(16) NOT NULL COMMENT '创建时间',
  `paytype` varchar(12) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `cost` float(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- ----------------------------
-- Records of fa_rate
-- ----------------------------

-- ----------------------------
-- Table structure for fa_sms
-- ----------------------------
DROP TABLE IF EXISTS `fa_sms`;
CREATE TABLE `fa_sms` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) DEFAULT '' COMMENT '事件',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号',
  `code` varchar(10) DEFAULT '' COMMENT '验证码',
  `times` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '验证次数',
  `ip` varchar(30) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) unsigned DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信验证码表';

-- ----------------------------
-- Records of fa_sms
-- ----------------------------

-- ----------------------------
-- Table structure for fa_test
-- ----------------------------
DROP TABLE IF EXISTS `fa_test`;
CREATE TABLE `fa_test` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) DEFAULT '0' COMMENT '会员ID',
  `admin_id` int(10) DEFAULT '0' COMMENT '管理员ID',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '分类ID(单选)',
  `category_ids` varchar(100) DEFAULT NULL COMMENT '分类ID(多选)',
  `tags` varchar(255) DEFAULT '' COMMENT '标签',
  `week` enum('monday','tuesday','wednesday') DEFAULT NULL COMMENT '星期(单选):monday=星期一,tuesday=星期二,wednesday=星期三',
  `flag` set('hot','index','recommend') DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `genderdata` enum('male','female') DEFAULT 'male' COMMENT '性别(单选):male=男,female=女',
  `hobbydata` set('music','reading','swimming') DEFAULT NULL COMMENT '爱好(多选):music=音乐,reading=读书,swimming=游泳',
  `title` varchar(100) DEFAULT '' COMMENT '标题',
  `content` text COMMENT '内容',
  `image` varchar(100) DEFAULT '' COMMENT '图片',
  `images` varchar(1500) DEFAULT '' COMMENT '图片组',
  `attachfile` varchar(100) DEFAULT '' COMMENT '附件',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键字',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `city` varchar(100) DEFAULT '' COMMENT '省市',
  `array` varchar(255) DEFAULT '' COMMENT '数组:value=值',
  `json` varchar(255) DEFAULT '' COMMENT '配置:key=名称,value=值',
  `multiplejson` varchar(1500) DEFAULT '' COMMENT '二维数组:title=标题,intro=介绍,author=作者,age=年龄',
  `price` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '价格',
  `views` int(10) unsigned DEFAULT '0' COMMENT '点击',
  `workrange` varchar(100) DEFAULT '' COMMENT '时间区间',
  `startdate` date DEFAULT NULL COMMENT '开始日期',
  `activitytime` datetime DEFAULT NULL COMMENT '活动时间(datetime)',
  `year` year(4) DEFAULT NULL COMMENT '年',
  `times` time DEFAULT NULL COMMENT '时间',
  `refreshtime` bigint(16) DEFAULT NULL COMMENT '刷新时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) DEFAULT '0' COMMENT '权重',
  `switch` tinyint(1) DEFAULT '0' COMMENT '开关',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `state` enum('0','1','2') DEFAULT '1' COMMENT '状态值:0=禁用,1=正常,2=推荐',
  `config_json` json NOT NULL COMMENT '渠道配置参数（JSON格式）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试表';

-- ----------------------------
-- Records of fa_test
-- ----------------------------

-- ----------------------------
-- Table structure for fa_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_user`;
CREATE TABLE `fa_user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '组别ID',
  `username` varchar(32) DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `password` varchar(32) DEFAULT '' COMMENT '密码',
  `salt` varchar(30) DEFAULT '' COMMENT '密码盐',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `score` int(10) NOT NULL DEFAULT '0' COMMENT '积分',
  `successions` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '连续登录天数',
  `maxsuccessions` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '最大连续登录天数',
  `prevtime` bigint(16) DEFAULT NULL COMMENT '上次登录时间',
  `logintime` bigint(16) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT '' COMMENT '登录IP',
  `loginfailure` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '失败次数',
  `loginfailuretime` bigint(16) DEFAULT NULL COMMENT '最后登录失败时间',
  `joinip` varchar(50) DEFAULT '' COMMENT '加入IP',
  `jointime` bigint(16) DEFAULT NULL COMMENT '加入时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(50) DEFAULT '' COMMENT 'Token',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  `verification` varchar(255) DEFAULT '' COMMENT '验证',
  `version` int(11) DEFAULT '0',
  `pulltime` bigint(16) DEFAULT NULL COMMENT '拉单时间',
  PRIMARY KEY (`id`),
  KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

-- ----------------------------
-- Records of fa_user
-- ----------------------------
INSERT INTO `fa_user` VALUES ('1', '1', 'admin', '开发者', '2153cf76e619f039cee8819bbb81ff8f', 'jiuK5r', '-1090.00', '0', '1', '5', '1752997209', '1753020941', '127.0.0.1', '0', '1751286569', '127.0.0.1', '1491635035', '0', '1753020941', '', 'normal', '', '12', null);
INSERT INTO `fa_user` VALUES ('2', '2', 'abc1', '雪', 'e0c1a93b5ffd01f78b926a7e7ad9c6de', 'XMEQsa', '10505050.00', '0', '1', '4', '1743516930', '1748520547', '127.0.0.1', '0', '1742009203', '127.0.0.1', '1740572440', '1740572440', '1748520547', '', 'normal', '', '10', '**********');

-- ----------------------------
-- Table structure for fa_user_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_group`;
CREATE TABLE `fa_user_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT '' COMMENT '组名',
  `rules` text COMMENT '权限节点',
  `createtime` bigint(16) DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='会员组表';

-- ----------------------------
-- Records of fa_user_group
-- ----------------------------
INSERT INTO `fa_user_group` VALUES ('1', '超管', '2,4,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42', '1491635035', '1743835753', 'normal');
INSERT INTO `fa_user_group` VALUES ('2', '服务商', '34,33,24,23,22,21,20,19,10,9,12,18,32,2,4', '1740522833', '1741962854', 'normal');
INSERT INTO `fa_user_group` VALUES ('3', '无', '', '1741959422', '1741962901', 'normal');

-- ----------------------------
-- Table structure for fa_user_money_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_money_log`;
CREATE TABLE `fa_user_money_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更余额',
  `before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更前余额',
  `after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更后余额',
  `memo` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='会员余额变动表';

-- ----------------------------
-- Records of fa_user_money_log
-- ----------------------------
INSERT INTO `fa_user_money_log` VALUES ('11', '2', '10000000.00', '505050.00', '10505050.00', '1', '1748520506');

-- ----------------------------
-- Table structure for fa_user_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_rule`;
CREATE TABLE `fa_user_rule` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) DEFAULT NULL COMMENT '父ID',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `title` varchar(50) DEFAULT '' COMMENT '标题',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `ismenu` tinyint(1) DEFAULT NULL COMMENT '是否菜单',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COMMENT='会员规则表';

-- ----------------------------
-- Records of fa_user_rule
-- ----------------------------
INSERT INTO `fa_user_rule` VALUES ('2', '0', 'api', 'API Interface', '', '1', '1491635035', '1491635035', '2', 'normal');
INSERT INTO `fa_user_rule` VALUES ('4', '2', 'user', 'User Module', '', '1', '1491635035', '1491635035', '11', 'normal');
INSERT INTO `fa_user_rule` VALUES ('9', '4', 'api/user/login', 'Login', '', '0', '1491635035', '1491635035', '6', 'normal');
INSERT INTO `fa_user_rule` VALUES ('10', '4', 'api/user/info', '获取个人信息', '', '0', '1491635035', '1740523096', '8', 'normal');
INSERT INTO `fa_user_rule` VALUES ('11', '4', 'api/user/add', '添加会员', '', '0', '1491635035', '1740572081', '10', 'normal');
INSERT INTO `fa_user_rule` VALUES ('12', '4', 'api/user/profile', 'Profile', '', '0', '1491635035', '1491635035', '3', 'normal');
INSERT INTO `fa_user_rule` VALUES ('13', '4', 'api/user/list', '用户列表', '', '0', '1740570111', '1740573033', '13', 'normal');
INSERT INTO `fa_user_rule` VALUES ('14', '4', 'api/user/status', '更新状态', '', '0', '**********', '**********', '14', 'normal');
INSERT INTO `fa_user_rule` VALUES ('15', '4', 'api/user/delete', '删除用户', '', '0', '**********', '**********', '15', 'normal');
INSERT INTO `fa_user_rule` VALUES ('16', '4', 'api/user/update', '编辑用户', '', '0', '**********', '**********', '16', 'normal');
INSERT INTO `fa_user_rule` VALUES ('17', '4', 'api/user/balance', '增减余额', '', '0', '**********', '**********', '17', 'normal');
INSERT INTO `fa_user_rule` VALUES ('18', '2', 'account', '账号模块', '', '1', '**********', '**********', '18', 'normal');
INSERT INTO `fa_user_rule` VALUES ('19', '18', 'api/account/add', '创建账号', '', '0', '**********', '**********', '19', 'normal');
INSERT INTO `fa_user_rule` VALUES ('20', '18', 'api/account/list', '账号列表', '', '0', '**********', '**********', '20', 'normal');
INSERT INTO `fa_user_rule` VALUES ('21', '18', 'api/account/update', '更新账号', '', '0', '**********', '**********', '21', 'normal');
INSERT INTO `fa_user_rule` VALUES ('22', '18', 'api/account/status', '更新状态', '', '0', '**********', '**********', '22', 'normal');
INSERT INTO `fa_user_rule` VALUES ('23', '18', 'api/account/delete', '删除账号', '', '0', '**********', '**********', '23', 'normal');
INSERT INTO `fa_user_rule` VALUES ('24', '18', 'api/account/detail', '账号详情', '', '0', '**********', '**********', '24', 'normal');
INSERT INTO `fa_user_rule` VALUES ('25', '2', 'merchant', '商户模块', '', '1', '**********', '**********', '25', 'normal');
INSERT INTO `fa_user_rule` VALUES ('26', '25', 'api/merchant/list', '商户列表', '', '0', '**********', '**********', '26', 'normal');
INSERT INTO `fa_user_rule` VALUES ('27', '25', 'api/merchant/add', '添加商户', '', '0', '**********', '**********', '27', 'normal');
INSERT INTO `fa_user_rule` VALUES ('28', '25', 'api/merchant/delete', '删除商户', '', '0', '**********', '**********', '28', 'normal');
INSERT INTO `fa_user_rule` VALUES ('29', '25', 'api/merchant/status', '更新状态', '', '0', '**********', '**********', '29', 'normal');
INSERT INTO `fa_user_rule` VALUES ('30', '25', 'api/merchant/detail', '商户详情', '', '0', '**********', '**********', '30', 'normal');
INSERT INTO `fa_user_rule` VALUES ('31', '25', 'api/merchant/update', '更新商户', '', '0', '1741463586', '1741463586', '31', 'normal');
INSERT INTO `fa_user_rule` VALUES ('32', '2', 'order', '订单模块', '', '1', '1741471615', '1741471615', '32', 'normal');
INSERT INTO `fa_user_rule` VALUES ('33', '32', 'api/order/list', '订单列表', '', '0', '1741471631', '1741471631', '33', 'normal');
INSERT INTO `fa_user_rule` VALUES ('34', '32', 'api/order/detail', '订单详情', '', '0', '1741473883', '1741473929', '34', 'normal');
INSERT INTO `fa_user_rule` VALUES ('35', '32', 'api/order/callback', '订单回调', '', '0', '1741474935', '1741474935', '35', 'normal');
INSERT INTO `fa_user_rule` VALUES ('36', '2', 'cookiepool', 'CK池', '', '1', '1743835509', '1743835509', '36', 'normal');
INSERT INTO `fa_user_rule` VALUES ('37', '36', 'api/cookiepool/detail', 'CK详情', '', '0', '1743835565', '1743835565', '37', 'normal');
INSERT INTO `fa_user_rule` VALUES ('38', '36', 'api/cookiepool/delete', '删除CK', '', '0', '1743835600', '1743835833', '38', 'normal');
INSERT INTO `fa_user_rule` VALUES ('39', '36', 'api/cookiepool/status', '更新状态', '', '0', '1743835637', '1743835862', '39', 'normal');
INSERT INTO `fa_user_rule` VALUES ('40', '36', 'api/cookiepool/update', '更新CK', '', '0', '1743835663', '1743835855', '40', 'normal');
INSERT INTO `fa_user_rule` VALUES ('41', '36', 'api/cookiepool/add', '创建CK', '', '0', '1743835721', '1743835848', '41', 'normal');
INSERT INTO `fa_user_rule` VALUES ('42', '36', 'api/cookiepool/list', 'CK列表', '', '0', '1743835739', '1743835839', '42', 'normal');

-- ----------------------------
-- Table structure for fa_user_score_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_score_log`;
CREATE TABLE `fa_user_score_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `score` int(10) NOT NULL DEFAULT '0' COMMENT '变更积分',
  `before` int(10) NOT NULL DEFAULT '0' COMMENT '变更前积分',
  `after` int(10) NOT NULL DEFAULT '0' COMMENT '变更后积分',
  `memo` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员积分变动表';

-- ----------------------------
-- Records of fa_user_score_log
-- ----------------------------

-- ----------------------------
-- Table structure for fa_user_token
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_token`;
CREATE TABLE `fa_user_token` (
  `token` varchar(50) NOT NULL COMMENT 'Token',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `expiretime` bigint(16) DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员Token表';

-- ----------------------------
-- Records of fa_user_token
-- ----------------------------
INSERT INTO `fa_user_token` VALUES ('49a953f3a7aa76a0b2f5f59b7654726908faa063', '1', '1752997209', '1755589209');
INSERT INTO `fa_user_token` VALUES ('8eddf8a79e7a1cd9692c0a6d1cb845e24bc0a663', '1', '1751599432', '1754191432');
INSERT INTO `fa_user_token` VALUES ('8fab7f0919bc4fdd1684caa4e5c40ca5c454af8e', '1', '1751286572', '1753878572');
INSERT INTO `fa_user_token` VALUES ('cc03cbf3cb6eb2539c5aac3234537ba89901edea', '1', '1753020941', '1755612941');
INSERT INTO `fa_user_token` VALUES ('ffa3bc02b2a040a3bb4ad2d30697aea4d9cc71c7', '1', '1751212127', '1753804127');
