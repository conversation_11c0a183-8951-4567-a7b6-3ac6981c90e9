<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Exception;

/**
 * 订单归档命令
 * 将今天之前的订单从order表归档到order_history表
 * 支持内部定时执行功能
 */
class ArchiveOrder extends Command
{
    protected function configure()
    {
        # 测试归档功能（只执行一次）
        // php think archive:order --once

        # 每天凌晨2点执行（默认）
        // php think archive:order

        # 每天凌晨3点30分执行
        // php think archive:order --time=03:30

        # 每天下午3点执行
        // php think archive:order --time=15:00
        $this->setName('archive:order')
            ->setDescription('归档今天之前的订单到order_history表')
            ->addOption('time', 't', null, '指定每天执行的时间，格式：HH:MM，默认02:00')
            ->addOption('once', 'o', null, '只执行一次归档，不循环执行');
    }

    protected function execute(Input $input, Output $output)
    {
        $execTime = $input->getOption('time') ?: '02:00'; // 默认凌晨2点执行
        $isOnce = $input->getOption('once');

        if ($isOnce) {
            // 只执行一次
            $this->runOnce($output);
        } else {
            // 内部定时循环执行
            $this->runWithTimer($output, $execTime);
        }
    }

    /**
     * 内部定时循环执行
     * @param Output $output
     * @param string $execTime 执行时间，格式：HH:MM
     */
    protected function runWithTimer(Output $output, $execTime)
    {
        $output->writeln("启动订单归档定时任务，每天 {$execTime} 执行归档任务");
        $output->writeln("按 Ctrl+C 停止任务");

        // 解析执行时间
        list($hour, $minute) = explode(':', $execTime);
        $hour = (int)$hour;
        $minute = (int)$minute;

        $lastExecDate = null; // 记录上次执行的日期

        while (true) {
            try {
                // 确保数据库连接正常
                $this->ensureDatabaseConnection($output);

                $now = time();
                $currentDate = date('Y-m-d', $now);
                $currentHour = (int)date('H', $now);
                $currentMinute = (int)date('i', $now);

                // 检查是否到达执行时间且今天还没执行过
                if ($currentHour == $hour && $currentMinute == $minute && $lastExecDate !== $currentDate) {
                    $output->writeln("[" . date('Y-m-d H:i:s') . "] 开始执行订单归档任务...");

                    // 执行归档任务
                    $this->performArchive($output);

                    $lastExecDate = $currentDate;
                    $output->writeln("[" . date('Y-m-d H:i:s') . "] 订单归档任务执行完成");
                }

                // 每60秒检查一次
                sleep(60);
            } catch (\Exception $e) {
                $output->writeln("[" . date('Y-m-d H:i:s') . "] 执行归档任务时发生错误: " . $e->getMessage());

                // 如果是数据库连接错误，尝试重连
                if (strpos($e->getMessage(), 'MySQL server has gone away') !== false ||
                    strpos($e->getMessage(), 'Lost connection') !== false) {
                    $output->writeln("[" . date('Y-m-d H:i:s') . "] 检测到数据库连接断开，尝试重新连接...");
                    $this->reconnectDatabase($output);
                }

                // 等待5分钟后继续
                sleep(300);
            }
        }
    }

    /**
     * 执行一次归档任务
     * @param Output $output
     */
    protected function runOnce(Output $output)
    {
        $output->writeln("开始执行订单归档任务...");
        $this->performArchive($output);
        $output->writeln("订单归档任务执行完成");
    }

    /**
     * 执行具体的归档操作
     * @param Output $output
     */
    protected function performArchive(Output $output)
    {
        $batchSize = 10000; // 每批处理条数
        // 计算今日0点的时间戳，只保留今天的数据
        $today = strtotime(date('Y-m-d'));

        $output->writeln("归档时间点：" . date('Y-m-d H:i:s', $today));

        $totalArchived = 0;
        $batchCount = 0;

        while (true) {
            try {
                // 确保数据库连接正常
                $this->ensureDatabaseConnection($output);

                // 查询一批要归档的数据（今日0点之前的都归档）
                $rows = Db::name('order')
                    ->where('create_time', '<', $today)
                    ->limit($batchSize)
                    ->select();

                // 转为数组，兼容thinkphp5的Collection返回值
                $rows = is_array($rows) ? $rows : (is_object($rows) ? $rows->toArray() : []);

                if (empty($rows)) {
                    $output->writeln("归档完成，总共归档：{$totalArchived} 条订单，处理了 {$batchCount} 批数据。");
                    break;
                }

                // 确保数据库连接正常
                $this->ensureDatabaseConnection($output);

                // 插入到归档表
                Db::name('order_history')->insertAll($rows);

                // 确保数据库连接正常
                $this->ensureDatabaseConnection($output);

                // 删除主表中的这些数据
                $ids = array_column($rows, 'id');
                Db::name('order')->where('id', 'in', $ids)->delete();

                $totalArchived += count($rows);
                $batchCount++;

                $output->writeln("已归档 {$totalArchived} 条订单（第 {$batchCount} 批）...");

                // 每批处理后稍作休息，避免数据库压力过大
                usleep(100000); // 休息0.1秒

            } catch (\Exception $e) {
                $output->writeln("归档第 {$batchCount} 批数据时发生错误: " . $e->getMessage());

                // 如果是数据库连接错误，尝试重连
                if (strpos($e->getMessage(), 'MySQL server has gone away') !== false ||
                    strpos($e->getMessage(), 'Lost connection') !== false) {
                    $output->writeln("[" . date('Y-m-d H:i:s') . "] 检测到数据库连接断开，尝试重新连接...");
                    $this->reconnectDatabase($output);
                    // 继续循环，不退出
                    continue;
                }

                break;
            }
        }
    }

    /**
     * 确保数据库连接正常
     * @param Output $output
     */
    protected function ensureDatabaseConnection(Output $output)
    {
        try {
            // 执行一个简单的查询来检查连接
            Db::query('SELECT 1');
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'MySQL server has gone away') !== false ||
                strpos($e->getMessage(), 'Lost connection') !== false) {
                $output->writeln("[" . date('Y-m-d H:i:s') . "] 数据库连接已断开，正在重新连接...");
                $this->reconnectDatabase($output);
            } else {
                throw $e;
            }
        }
    }

    /**
     * 重新连接数据库
     * @param Output $output
     */
    protected function reconnectDatabase(Output $output)
    {
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                // 关闭现有连接
                Db::close();

                // 等待一段时间后重连
                sleep(2);

                // 尝试重新连接
                Db::query('SELECT 1');

                $output->writeln("[" . date('Y-m-d H:i:s') . "] 数据库重新连接成功");
                return;

            } catch (\Exception $e) {
                $retryCount++;
                $output->writeln("[" . date('Y-m-d H:i:s') . "] 数据库重连失败（第 {$retryCount} 次尝试）: " . $e->getMessage());

                if ($retryCount >= $maxRetries) {
                    throw new Exception("数据库重连失败，已尝试 {$maxRetries} 次");
                }

                // 等待更长时间后重试
                sleep(5);
            }
        }
    }
}
