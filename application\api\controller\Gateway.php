<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Merchant;
use app\common\model\Order;
use app\api\validata\OrderCreate;
use think\Db;
use think\Exception;

/**
 * 支付网关控制器
 * 负责处理支付网关相关的请求，包括创建订单和处理支付回调
 */
class Gateway extends Api
{
    // 所有方法无需登录即可访问
    protected $noNeedLogin = ['*'];
    // 所有方法无需权限验证
    protected $noNeedRight = ['*'];

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 创建订单
     * @return \think\response\Json
     */
    public function index()
    {
        try {
            // 1. 获取并验证请求参数
            $data = $this->validateRequest();

            // 2. 获取商户信息
            $merchant = $this->getMerchant($data['mid']);

            // 3. 获取可用服务商
            $user = $this->getUserCanUse($data['amount']);

            // 4. 创建订单
            $orderData = $this->createOrder($data, $merchant, $user);

            // 5. 返回支付链接
            return $this->success('创建成功', $orderData);
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 验证请求参数
     * @return array
     * @throws Exception
     */
    protected function validateRequest()
    {
        $data = $this->request->param();
        $validator = new OrderCreate();
        if (!$validator->batch()->check($data)) {
            throw new Exception(json_encode($validator->getError()));
        }
        return $data;
    }

    /**
     * 获取商户信息
     * @param int $merchantId
     * @return Merchant
     * @throws Exception
     */
    protected function getMerchant($merchantId)
    {
        $merchant = Merchant::where('id', $merchantId)->find();
        if (!$merchant) {
            throw new Exception('商户不存在');
        }
        if ($merchant->status !== 1) {
            throw new Exception('商户状态异常');
        }
        return $merchant;
    }

    /**
     * 获取可用的服务商用户
     * @param float $amount 订单金额
     * @return array|null
     */
    protected function getUserCanUse($amount)
    {
        // 1. 使用 JOIN 查询一次性获取所有需要的数据
        $userList = Db::name('user')           // 1. 开始查询user表
            ->alias('u')                       // 2. 给user表设置别名'u'
            ->join('account a', 'u.id = a.user_id')  // 3. 关联account表，设置别名'a'
            ->where('u.id', '>', 1)            // 4. 排除ID为1的用户
            ->where('u.status', 'normal')      // 5. 只查询状态正常的用户
            ->where('u.money', '>=', $amount)  // 6. 余额必须大于等于订单金额
            // ->where('a.errormsg', '')          // 7. 账号没有错误信息
            ->where('a.status', 1)             // 8. 账号状态正常
            ->field('u.*, a.id as account_id') // 9. 选择需要的字段
            ->order('u.pulltime ASC')          // 10. 按最后使用时间升序排序
            ->group('u.id')                    // 11. 按用户ID分组，避免重复
            ->select();                        // 12. 执行查询

        if (empty($userList)) {
            throw new Exception("暂无可用服务商");
        }

        // 2. 选择第一个用户并更新
        $selectedUser = $userList[0];

        // 3. 更新最后使用时间
        Db::name('user')
            ->where('id', $selectedUser['id'])
            ->update(['pulltime' => time()]);

        return $selectedUser;
    }

    /**
     * 创建订单
     * @param array $data
     * @param Merchant $merchant
     * @param array $user
     * @return array
     * @throws Exception
     */
    protected function createOrder($data, $merchant, $user)
    {
        Db::startTrans();
        try {
            // 创建新订单对象
            $orderData = [
                'out_trade_no' => $data['out_trade_no'],
                'local_trade_no' => \app\common\library\Order::generateOrderNo(),
                'amount' => $data['amount'],
                'merchant_id' => $merchant->id,
                'paytype' => $data['channel_code'],
                'callback_url' => $data['notify_url'],
                'user_id' => $user['id'],
                'create_time' => time(),
                'update_time' => time(),
            ];

            $result = Db::name('order')->insert($orderData);

            if (!$result) {
                throw new Exception("订单号已存在");
            }

            $returnData = [
                'url' => $this->request->domain() . '/index/cashier/laifeng?out_trade_no=' . $data['out_trade_no']
                // 'url' => 'http://8.138.255.115/ks/pay.html?out_trade_no=' . $data['out_trade_no'] . '&amount=' . $data['amount'] . "&channel=" . $data['channel_code']
            ];

            Db::commit();
            return $returnData;
        } catch (Exception $e) {
            Db::rollback();
            return $this->error($e->getMessage());
        }
    }
}
