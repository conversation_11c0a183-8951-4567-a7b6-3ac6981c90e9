(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-506913df"],{"0ccb":function(t,s,a){var e=a("50c4"),r=a("1148"),o=a("1d80"),i=Math.ceil,l=function(t){return function(s,a,l){var n,c,f=String(o(s)),d=f.length,p=void 0===l?" ":String(l),m=e(a);return m<=d||""==p?f:(n=m-d,c=r.call(p,i(n/p.length)),c.length>n&&(c=c.slice(0,n)),t?f+c:c+f)}};t.exports={start:l(!1),end:l(!0)}},1148:function(t,s,a){"use strict";var e=a("a691"),r=a("1d80");t.exports="".repeat||function(t){var s=String(r(this)),a="",o=e(t);if(o<0||o==1/0)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(s+=s))1&o&&(a+=s);return a}},"1db4":function(t,s,a){},"4d90":function(t,s,a){"use strict";var e=a("23e7"),r=a("0ccb").start,o=a("9a0c");e({target:"String",proto:!0,forced:o},{padStart:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},9383:function(t,s,a){"use strict";a("1db4")},"9a0c":function(t,s,a){var e=a("342f");t.exports=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(e)},ecac:function(t,s,a){"use strict";a.r(s);var e=function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"box-card user-info-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"card-title"},[a("i",{staticClass:"el-icon-user"}),t._v(" 个人资料")])]),a("div",{staticClass:"text-center avatar-container"},[a("div",{staticClass:"avatar-wrapper"},[a("svg",{staticClass:"user-avatar",attrs:{viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"}},[a("path",{attrs:{d:"M512 64L896 192v288c0 259.2-172.8 428.8-384 480-211.2-51.2-384-220.8-384-480V192L512 64z",fill:"#1890ff"}}),a("defs",[a("linearGradient",{attrs:{id:"metallic",x1:"0%",y1:"0%",x2:"100%",y2:"100%"}},[a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0.6"},attrs:{offset:"0%"}}),a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0"},attrs:{offset:"50%"}}),a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0.2"},attrs:{offset:"100%"}})],1),a("linearGradient",{attrs:{id:"centerGlow",x1:"50%",y1:"0%",x2:"50%",y2:"100%"}},[a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0.8"},attrs:{offset:"0%"}}),a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0.3"},attrs:{offset:"50%"}}),a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0"},attrs:{offset:"100%"}})],1),a("radialGradient",{attrs:{id:"innerShine",cx:"50%",cy:"40%",r:"60%"}},[a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0.6"},attrs:{offset:"0%"}}),a("stop",{staticStyle:{"stop-color":"#ffffff","stop-opacity":"0"},attrs:{offset:"100%"}})],1)],1),a("path",{attrs:{d:"M512 64L896 192v288c0 259.2-172.8 428.8-384 480-211.2-51.2-384-220.8-384-480V192L512 64z",fill:"url(#metallic)"}}),a("path",{attrs:{d:"M512 128L832 230.4v249.6c0 224-149.3 371.2-320 416-170.7-44.8-320-192-320-416V230.4L512 128z",fill:"none",stroke:"#ffffff","stroke-width":"2",opacity:"0.3"}}),a("circle",{attrs:{cx:"512",cy:"384",r:"160",fill:"url(#innerShine)",opacity:"0.4"}}),a("path",{attrs:{d:"M512 320l128 96-48 160-80 48-80-48-48-160 128-96z",fill:"#ffffff",opacity:"0.15"}}),a("path",{attrs:{d:"M512 352l96 64-32 128-64 32-64-32-32-128 96-64z",fill:"#ffffff",opacity:"0.3"}}),a("path",{attrs:{d:"M512 384l64 32-16 96-48 16-48-16-16-96 64-32z",fill:"url(#centerGlow)"}}),a("circle",{attrs:{cx:"512",cy:"448",r:"96",fill:"none",stroke:"#ffffff","stroke-width":"1",opacity:"0.2"}}),a("circle",{attrs:{cx:"512",cy:"448",r:"64",fill:"none",stroke:"#ffffff","stroke-width":"1",opacity:"0.3"}}),a("path",{attrs:{d:"M512 96L832 208v48L512 144 192 256v-48L512 96z",fill:"#ffffff",opacity:"0.4"}}),a("path",{attrs:{d:"M512 64L896 192 512 320 128 192 512 64z",fill:"none",stroke:"#ffffff","stroke-width":"4",opacity:"0.5"}}),a("circle",{attrs:{cx:"512",cy:"448",r:"8",fill:"#ffffff",opacity:"0.8"}}),a("circle",{attrs:{cx:"512",cy:"448",r:"4",fill:"#ffffff"}}),a("path",{attrs:{d:"M472 408l-16-16 16-16 16 16-16 16z",fill:"#ffffff",opacity:"0.4"}}),a("path",{attrs:{d:"M552 408l-16-16 16-16 16 16-16 16z",fill:"#ffffff",opacity:"0.4"}}),a("path",{attrs:{d:"M472 488l-16-16 16-16 16 16-16 16z",fill:"#ffffff",opacity:"0.4"}}),a("path",{attrs:{d:"M552 488l-16-16 16-16 16 16-16 16z",fill:"#ffffff",opacity:"0.4"}})])]),a("h3",{staticClass:"username"},[t._v(t._s(t.name))]),a("p",{staticClass:"user-role"},[a("el-tag",{attrs:{size:"small",type:"success"}},[t._v(t._s(t.username))])],1)]),a("el-divider",{attrs:{"content-position":"center"}},[t._v("账户信息")]),a("div",{staticClass:"user-stats"},[a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-icon"},[a("el-badge",{staticClass:"money-badge",attrs:{value:t.money>-1?"VIP":""}},[a("i",{staticClass:"el-icon-wallet"})])],1),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-label"},[t._v("账户余额")]),a("div",{staticClass:"stat-value money"},[t._v("¥ "+t._s(t.formatMoney(t.money)))])])]),a("el-divider"),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-icon"},[a("i",{staticClass:"el-icon-time"})]),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-label"},[t._v("最后登录时间")]),a("div",{staticClass:"stat-value"},[t._v(t._s(t.formatDate(t.logintime)))])])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-icon"},[a("i",{staticClass:"el-icon-location-information"})]),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-label"},[t._v("登录IP")]),a("div",{staticClass:"stat-value"},[t._v(t._s(t.loginip))])])])],1)],1)],1),a("el-col",{attrs:{span:16}},[a("el-card",{staticClass:"box-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"card-title"},[a("i",{staticClass:"el-icon-edit"}),t._v(" 修改昵称")])]),a("el-form",{ref:"nicknameForm",staticClass:"profile-form",attrs:{model:t.nicknameForm,rules:t.nicknameRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"新昵称",prop:"nickname"}},[a("el-input",{attrs:{placeholder:"请输入新昵称"},model:{value:t.nicknameForm.nickname,callback:function(s){t.$set(t.nicknameForm,"nickname",s)},expression:"nicknameForm.nickname"}},[a("template",{slot:"prefix"},[a("i",{staticClass:"el-icon-user"})])],2)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:t.handleUpdateNickname}},[t._v("保存修改")])],1)],1)],1),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"},attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"card-title"},[a("i",{staticClass:"el-icon-lock"}),t._v(" 修改密码")])]),a("el-form",{ref:"passwordForm",staticClass:"profile-form",attrs:{model:t.passwordForm,rules:t.passwordRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"原密码",prop:"oldPassword"}},[a("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入原密码"},model:{value:t.passwordForm.oldPassword,callback:function(s){t.$set(t.passwordForm,"oldPassword",s)},expression:"passwordForm.oldPassword"}},[a("template",{slot:"prefix"},[a("i",{staticClass:"el-icon-key"})])],2)],1),a("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[a("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入新密码"},model:{value:t.passwordForm.newPassword,callback:function(s){t.$set(t.passwordForm,"newPassword",s)},expression:"passwordForm.newPassword"}},[a("template",{slot:"prefix"},[a("i",{staticClass:"el-icon-lock"})])],2)],1),a("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[a("el-input",{attrs:{type:"password","show-password":"",placeholder:"请再次输入新密码"},model:{value:t.passwordForm.confirmPassword,callback:function(s){t.$set(t.passwordForm,"confirmPassword",s)},expression:"passwordForm.confirmPassword"}},[a("template",{slot:"prefix"},[a("i",{staticClass:"el-icon-lock"})])],2)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-check"},on:{click:t.handleUpdatePassword}},[t._v("修改密码")]),a("el-button",{attrs:{icon:"el-icon-refresh-right"},on:{click:t.resetPasswordForm}},[t._v("重置")])],1)],1)],1)],1)],1)],1)},r=[],o=a("5530"),i=(a("99af"),a("d3b7"),a("ac1f"),a("25f0"),a("4d90"),a("5319"),a("2f62")),l=a("c24f"),n={name:"Profile",data:function(){var t=this,s=function(s,a,e){a!==t.passwordForm.newPassword?e(new Error("两次输入的密码不一致")):e()};return{userInfo:{username:"",nickname:"",money:0,logintime:"",loginip:""},nicknameForm:{nickname:""},nicknameRules:{nickname:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}]},passwordForm:{oldPassword:"",newPassword:"",confirmPassword:""},passwordRules:{oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:s,trigger:"blur"}]}}},computed:Object(o["a"])({},Object(i["b"])(["name","username","loginip","logintime","money"])),methods:{handleUpdateNickname:function(){var t=this;this.$refs.nicknameForm.validate((function(s){s&&Object(l["h"])({nickname:t.nicknameForm.nickname}).then((function(s){t.$message.success("昵称修改成功"),t.$store.commit("user/SET_NAME",t.nicknameForm.nickname),t.userInfo.nickname=t.nicknameForm.nickname,t.nicknameForm.nickname=""})).catch((function(s){t.$message.error("昵称修改失败："+s.message)}))}))},handleUpdatePassword:function(){var t=this;this.$refs.passwordForm.validate((function(s){s&&Object(l["h"])({oldPassword:t.passwordForm.oldPassword,newPassword:t.passwordForm.newPassword}).then((function(s){t.$message.success("密码修改成功"),t.resetPasswordForm(),t.$store.dispatch("user/getInfo")}))}))},resetPasswordForm:function(){this.$refs.passwordForm.resetFields(),this.passwordForm={oldPassword:"",newPassword:"",confirmPassword:""}},formatMoney:function(t){return parseFloat(t).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},formatDate:function(t){if(!t)return"";if("string"===typeof t||"number"===typeof t){var s=parseInt(t);isNaN(s)?"string"===typeof t&&(t=new Date(t.replace(/-/g,"/"))):t=new Date(10===s.toString().length?1e3*s:s)}if(!(t instanceof Date)||isNaN(t))return"";var a=t.getFullYear(),e=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0"),o=String(t.getHours()).padStart(2,"0"),i=String(t.getMinutes()).padStart(2,"0"),l=String(t.getSeconds()).padStart(2,"0");return"".concat(a,"-").concat(e,"-").concat(r," ").concat(o,":").concat(i,":").concat(l)}}},c=n,f=(a("9383"),a("2877")),d=Object(f["a"])(c,e,r,!1,null,"3e058fd3",null);s["default"]=d.exports}}]);